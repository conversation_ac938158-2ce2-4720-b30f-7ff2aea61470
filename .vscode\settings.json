{"python.defaultInterpreter": "./.venv/Scripts/python.exe", "python.pythonPath": "./.venv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "python.linting.enabled": true, "python.linting.pylintEnabled": false, "python.linting.flake8Enabled": false, "python.analysis.typeCheckingMode": "basic", "python.analysis.autoImportCompletions": true, "python.analysis.extraPaths": ["./.venv/Lib/site-packages"], "files.associations": {"*.py": "python"}, "python.formatting.provider": "black", "pylance.insidersChannel": "off"}