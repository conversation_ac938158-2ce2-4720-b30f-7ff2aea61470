{"python.defaultInterpreter": "./.venv/Scripts/python.exe", "python.analysis.typeCheckingMode": "off", "python.linting.enabled": false, "python.analysis.diagnosticMode": "openFilesOnly", "python.analysis.autoImportCompletions": false, "pylance.insidersChannel": "off", "python.analysis.diagnosticSeverityOverrides": {"reportMissingImports": "none", "reportMissingTypeStubs": "none", "reportImportCycles": "none", "reportUnusedImport": "none", "reportUnusedClass": "none", "reportUnusedFunction": "none", "reportUnusedVariable": "none", "reportGeneralTypeIssues": "none", "reportOptionalSubscript": "none", "reportOptionalMemberAccess": "none", "reportOptionalCall": "none", "reportOptionalIterable": "none", "reportOptionalContextManager": "none", "reportOptionalOperand": "none", "reportUntypedFunctionDecorator": "none", "reportUntypedClassDecorator": "none", "reportUntypedBaseClass": "none", "reportUntypedNamedTuple": "none", "reportPrivateUsage": "none", "reportConstantRedefinition": "none", "reportIncompatibleMethodOverride": "none", "reportIncompatibleVariableOverride": "none", "reportInconsistentConstructor": "none", "reportOverlappingOverloads": "none", "reportMissingSuperCall": "none", "reportUninitializedInstanceVariable": "none", "reportInvalidStringEscapeSequence": "none", "reportUnknownParameterType": "none", "reportUnknownArgumentType": "none", "reportUnknownLambdaType": "none", "reportUnknownVariableType": "none", "reportUnknownMemberType": "none", "reportMissingParameterType": "none", "reportMissingTypeArgument": "none", "reportInvalidTypeVarUse": "none", "reportCallInDefaultInitializer": "none", "reportUnnecessaryIsInstance": "none", "reportUnnecessaryCast": "none", "reportUnnecessaryComparison": "none", "reportAssertAlwaysTrue": "none", "reportSelfClsParameterName": "none", "reportImplicitStringConcatenation": "none"}}