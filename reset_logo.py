#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script لحذف الشعار الوهمي من قاعدة البيانات
"""

import sqlite3
import os

def reset_logo():
    """حذف الشعار الوهمي من قاعدة البيانات"""
    print("🗑️ حذف الشعار الوهمي من قاعدة البيانات")
    print("=" * 50)
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()
        
        # عرض البيانات الحالية
        cursor.execute("SELECT id, nom, logo FROM societe")
        rows = cursor.fetchall()
        
        print("📊 البيانات الحالية:")
        for row in rows:
            print(f"   ID: {row[0]}, الاسم: {row[1]}, الشعار: {row[2]}")
        
        # حذف الشعار من قاعدة البيانات
        cursor.execute("UPDATE societe SET logo = NULL")
        affected_rows = cursor.rowcount
        
        print(f"\n🔄 تم تحديث {affected_rows} صف(وف)")
        
        # عرض البيانات بعد التحديث
        cursor.execute("SELECT id, nom, logo FROM societe")
        rows = cursor.fetchall()
        
        print("\n📊 البيانات بعد التحديث:")
        for row in rows:
            print(f"   ID: {row[0]}, الاسم: {row[1]}, الشعار: {row[2]}")
        
        # حفظ التغييرات
        conn.commit()
        print("\n✅ تم حفظ التغييرات بنجاح")
        
        # حذف ملفات الشعار الموجودة في مجلد uploads
        uploads_dir = 'static/uploads'
        if os.path.exists(uploads_dir):
            logo_files = [f for f in os.listdir(uploads_dir) if f.startswith('logo_')]
            if logo_files:
                print(f"\n🗂️ ملفات الشعار الموجودة في {uploads_dir}:")
                for logo_file in logo_files:
                    file_path = os.path.join(uploads_dir, logo_file)
                    print(f"   - {logo_file}")
                    try:
                        os.remove(file_path)
                        print(f"     ✅ تم حذف {logo_file}")
                    except Exception as e:
                        print(f"     ❌ خطأ في حذف {logo_file}: {e}")
            else:
                print(f"\n📁 لا توجد ملفات شعار في {uploads_dir}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False
    
    return True

if __name__ == "__main__":
    if reset_logo():
        print("\n🎯 تم إعادة تعيين الشعار بنجاح!")
        print("💡 يمكنك الآن تجربة تحميل شعار جديد من الواجهة")
    else:
        print("\n❌ فشل في إعادة تعيين الشعار")
