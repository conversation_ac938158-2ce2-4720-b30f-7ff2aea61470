{% extends "base.html" %}

{% block title %}Ajouter un Marché - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Ajouter un Marché{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-file-contract"></i> Ajouter un nouveau marché
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('ajouter_marche') }}">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="numero" class="form-label">N° de marché</label>
                            <input type="text" class="form-control" id="numero" name="numero" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="domaine" class="form-label">Domaine</label>
                            <select class="form-select" id="domaine" name="domaine" required>
                                <option value="">Sélectionner un domaine</option>
                                <option value="SVS">SVS</option>
                                <option value="EXTINCTEUR">EXTINCTEUR</option>
                                <option value="SYSTEME D'INCENDIE">SYSTEME D'INCENDIE</option>
                                <option value="SYSTEME D'ALARME">SYSTEME D'ALARME</option>
                                <option value="SYSTEME TELEPHONIQUE">SYSTEME TELEPHONIQUE</option>
                                <option value="L'AFFICHAGE DYNAMIQUE">L'AFFICHAGE DYNAMIQUE</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="date" name="date" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="objet" class="form-label">Objet de marché</label>
                            <input type="text" class="form-control" id="objet" name="objet" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="client" class="form-label">Client</label>
                            <input type="text" class="form-control" id="client" name="client" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="montant" class="form-label">Montant de marché (DH)</label>
                            <input type="number" step="0.01" class="form-control" id="montant" name="montant" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="delai_execution" class="form-label">Délai d'exécution</label>
                            <input type="text" class="form-control" id="delai_execution" name="delai_execution" placeholder="Ex: 12 mois">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="periode_interventions" class="form-label">Période d'interventions</label>
                            <select class="form-select" id="periode_interventions" name="periode_interventions">
                                <option value="">Sélectionner une période</option>
                                <option value="annuel">Annuel</option>
                                <option value="semestriel">Semestriel</option>
                                <option value="trimestriel">Trimestriel</option>
                                <option value="mensuel">Mensuel</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="lieu" class="form-label">Lieu</label>
                            <input type="text" class="form-control" id="lieu" name="lieu">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="date_ordre_service" class="form-label">Date d'ordre de service</label>
                            <input type="date" class="form-control" id="date_ordre_service" name="date_ordre_service">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="caution_definitif" class="form-label">Caution définitif</label>
                            <input type="text" class="form-control" id="caution_definitif" name="caution_definitif">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="mode_paiement" class="form-label">Mode de paiement</label>
                            <select class="form-select" id="mode_paiement" name="mode_paiement">
                                <option value="">Sélectionner un mode</option>
                                <option value="Chèque">Chèque</option>
                                <option value="Espèce">Espèce</option>
                                <option value="Ordre de Virement">Ordre de Virement</option>
                            </select>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer
                        </button>
                        <a href="{{ url_for('marches') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Générer automatiquement la date du jour
    document.addEventListener('DOMContentLoaded', function() {
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('date').value = today;
    });
</script>
{% endblock %}
