{% extends "base.html" %}

{% block title %}Tableau de Bord - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Tableau de Bord{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- En-tête de bienvenue amélioré -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-lg" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body py-5">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h1 class="mb-2 text-white fw-bold">
                                <i class="fas fa-chart-line me-3"></i>Bienvenue, {{ session.get('nom_complet', 'Utilisateur') }}!
                            </h1>
                            <p class="mb-0 text-white-75 fs-5">
                                {% if company_info and company_info.nom %}
                                    {{ company_info.nom }} - Système de Gestion de Maintenance
                                {% else %}
                                    Tableau de bord - Système de Gestion de Maintenance
                                {% endif %}
                            </p>
                            <p class="mb-0 text-white-50 mt-2">
                                <i class="fas fa-user-circle me-2"></i>Connecté en tant que {{ session.get('role', 'Utilisateur').title() }}
                            </p>
                        </div>
                        <div class="col-md-4 text-end">
                            <div class="text-white-75 mb-2">
                                <i class="fas fa-calendar-alt me-2"></i>
                                <span id="current-date"></span>
                            </div>
                            <div class="text-white-50">
                                <i class="fas fa-clock me-2"></i>
                                <span id="current-time"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques améliorées -->
    <div class="row mb-5">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-lg h-100 stat-card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="small fw-bold text-uppercase mb-2 opacity-75">Marchés Actifs</div>
                            <div class="h3 mb-0 fw-bold count-up">{{ stats.marches|default(0) }}</div>
                            <div class="small mt-1 opacity-75">
                                <i class="fas fa-arrow-up me-1"></i>Contrats en cours
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="icon-circle">
                                <i class="fas fa-file-contract fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-lg h-100 stat-card" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                <div class="card-body text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="small fw-bold text-uppercase mb-2 opacity-75">Interventions</div>
                            <div class="h3 mb-0 fw-bold count-up">{{ stats.interventions|default(0) }}</div>
                            <div class="small mt-1 opacity-75">
                                <i class="fas fa-tools me-1"></i>Opérations techniques
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="icon-circle">
                                <i class="fas fa-tools fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-lg h-100 stat-card" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
                <div class="card-body text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="small fw-bold text-uppercase mb-2 opacity-75">Réclamations</div>
                            <div class="h3 mb-0 fw-bold count-up">{{ stats.reclamations|default(0) }}</div>
                            <div class="small mt-1 opacity-75">
                                <i class="fas fa-exclamation-triangle me-1"></i>À traiter
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="icon-circle">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-lg h-100 stat-card" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
                <div class="card-body text-white">
                    <div class="row align-items-center">
                        <div class="col">
                            <div class="small fw-bold text-uppercase mb-2 opacity-75">Sites</div>
                            <div class="h3 mb-0 fw-bold count-up">{{ stats.sites|default(0) }}</div>
                            <div class="small mt-1 opacity-75">
                                <i class="fas fa-map-marker-alt me-1"></i>Emplacements
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="icon-circle">
                                <i class="fas fa-map-marker-alt fa-2x"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modules principaux - Première rangée -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm module-card">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3">
                            <i class="fas fa-building fa-2x text-primary"></i>
                        </div>
                        <h5 class="card-title mb-0 fw-bold">Informations Société</h5>
                    </div>
                    <p class="card-text text-muted flex-grow-1">Gérez les informations de votre entreprise, logo, coordonnées et autres détails importants.</p>
                    <a href="{{ url_for('societe') }}" class="btn btn-primary btn-sm mt-auto">
                        <i class="fas fa-arrow-right me-1"></i>Accéder
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm module-card">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3">
                            <i class="fas fa-file-contract fa-2x text-success"></i>
                        </div>
                        <h5 class="card-title mb-0 fw-bold">Marchés</h5>
                    </div>
                    <p class="card-text text-muted flex-grow-1">Gérez vos contrats et marchés avec les clients, suivez les délais et les paiements.</p>
                    <a href="{{ url_for('marches') }}" class="btn btn-success btn-sm mt-auto">
                        <i class="fas fa-arrow-right me-1"></i>Accéder
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm module-card">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3">
                            <i class="fas fa-tools fa-2x text-info"></i>
                        </div>
                        <h5 class="card-title mb-0 fw-bold">Interventions</h5>
                    </div>
                    <p class="card-text text-muted flex-grow-1">Suivez toutes vos interventions techniques par type de système et par client.</p>
                    <a href="{{ url_for('interventions') }}" class="btn btn-info btn-sm mt-auto">
                        <i class="fas fa-arrow-right me-1"></i>Accéder
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Modules principaux - Deuxième rangée -->
    <div class="row mb-4">
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm module-card">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3">
                            <i class="fas fa-exclamation-circle fa-2x text-warning"></i>
                        </div>
                        <h5 class="card-title mb-0 fw-bold">Réclamations</h5>
                    </div>
                    <p class="card-text text-muted flex-grow-1">Gérez les réclamations clients et suivez leur résolution par type de système.</p>
                    <a href="{{ url_for('reclamations') }}" class="btn btn-warning btn-sm mt-auto">
                        <i class="fas fa-arrow-right me-1"></i>Accéder
                    </a>
                </div>
            </div>
        </div>

        {% if session.get('role') == 'admin' %}
        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm module-card">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3">
                            <i class="fas fa-users fa-2x text-danger"></i>
                        </div>
                        <h5 class="card-title mb-0 fw-bold">Utilisateurs</h5>
                    </div>
                    <p class="card-text text-muted flex-grow-1">Gérez les utilisateurs du système et leurs permissions d'accès.</p>
                    <a href="{{ url_for('utilisateurs') }}" class="btn btn-danger btn-sm mt-auto">
                        <i class="fas fa-arrow-right me-1"></i>Accéder
                    </a>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="col-lg-4 col-md-6 mb-4">
            <div class="card h-100 border-0 shadow-sm module-card">
                <div class="card-body d-flex flex-column">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3">
                            <i class="fas fa-map fa-2x text-secondary"></i>
                        </div>
                        <h5 class="card-title mb-0 fw-bold">Carte du Maroc</h5>
                    </div>
                    <p class="card-text text-muted flex-grow-1">Visualisez toutes vos interventions, réclamations et sites sur une carte interactive du Maroc.</p>
                    <a href="{{ url_for('carte') }}" class="btn btn-secondary btn-sm mt-auto">
                        <i class="fas fa-map-marked-alt me-1"></i>Accéder
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Régions et Sites -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="icon-wrapper me-3">
                            <i class="fas fa-map-marker-alt fa-2x text-primary"></i>
                        </div>
                        <h5 class="card-title mb-0 fw-bold">Régions et Sites</h5>
                    </div>
                    <p class="card-text text-muted mb-4">Gérez les régions et les sites où vous intervenez.</p>
                    <div class="d-flex gap-3">
                        <a href="{{ url_for('regions') }}" class="btn btn-outline-primary">
                            <i class="fas fa-map-marker-alt me-1"></i>Régions
                        </a>
                        <a href="{{ url_for('sites') }}" class="btn btn-outline-primary">
                            <i class="fas fa-building me-1"></i>Sites
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mettre à jour la date et l'heure
        function updateDateTime() {
            const now = new Date();
            const dateOptions = {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            };
            const timeOptions = {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            };

            const dateElement = document.getElementById('current-date');
            const timeElement = document.getElementById('current-time');

            if (dateElement) {
                dateElement.textContent = now.toLocaleDateString('fr-FR', dateOptions);
            }
            if (timeElement) {
                timeElement.textContent = now.toLocaleTimeString('fr-FR', timeOptions);
            }
        }

        // Mettre à jour immédiatement et puis chaque seconde
        updateDateTime();
        setInterval(updateDateTime, 1000);

        // Animation des statistiques améliorée
        const statElements = document.querySelectorAll('.count-up');
        statElements.forEach((element, index) => {
            const finalValue = parseInt(element.textContent);
            let currentValue = 0;
            const duration = 2000; // ms
            const interval = 50; // ms
            const steps = duration / interval;
            const increment = finalValue / steps;

            // Délai pour créer un effet en cascade
            setTimeout(() => {
                element.textContent = '0';
                const counter = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= finalValue) {
                        element.textContent = finalValue;
                        clearInterval(counter);
                        // Ajouter un effet de pulsation à la fin
                        element.style.transform = 'scale(1.1)';
                        setTimeout(() => {
                            element.style.transform = 'scale(1)';
                        }, 200);
                    } else {
                        element.textContent = Math.floor(currentValue);
                    }
                }, interval);
            }, index * 200);
        });

        // Animation des cartes de modules
        const moduleCards = document.querySelectorAll('.module-card');
        moduleCards.forEach((card, index) => {
            card.style.opacity = '0';
            card.style.transform = 'translateY(30px)';

            setTimeout(() => {
                card.style.transition = 'all 0.6s ease';
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
            }, 500 + (index * 100));
        });

        // Effet hover amélioré pour les cartes de statistiques
        const statCards = document.querySelectorAll('.stat-card');
        statCards.forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-5px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });
    });
</script>

<style>
    .stat-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }

    .module-card {
        transition: all 0.3s ease;
    }

    .module-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    }

    .icon-wrapper {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255,255,255,0.1);
        border-radius: 50%;
    }

    .icon-circle {
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
    }

    .count-up {
        transition: all 0.3s ease;
    }
</style>
{% endblock %}
