#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تكوين الشبكة لنظام إدارة الصيانة
"""

import os
import sys
import socket
import configparser
import subprocess

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        # الاتصال بخادم خارجي للحصول على IP المحلي
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "*************"  # IP افتراضي

def check_port_available(port):
    """فحص إذا كان المنفذ متاح"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.bind(('0.0.0.0', port))
        s.close()
        return True
    except:
        return False

def get_config_file():
    """الحصول على مسار ملف الإعدادات"""
    if getattr(sys, 'frozen', False):
        app_dir = os.path.dirname(sys.executable)
    else:
        app_dir = os.path.dirname(os.path.abspath(__file__))
    
    return os.path.join(app_dir, 'config.ini')

def create_network_config(installation_type=1, port=5000):
    """إنشاء إعدادات شبكية"""
    
    config = configparser.ConfigParser()
    
    # إعدادات أساسية
    config['Settings'] = {
        'InstallationType': str(installation_type),  # 1=شبكي, 2=خادم
        'Port': str(port),
        'MultiUser': 'True',
        'ServerMode': 'True' if installation_type == 2 else 'False',
        'Version': '1.0.0'
    }
    
    # إعدادات الشبكة
    config['Network'] = {
        'Host': '0.0.0.0',  # للسماح بالوصول من أي جهاز
        'AllowExternalAccess': 'True'
    }
    
    # إعدادات قاعدة البيانات
    config['Database'] = {
        'Path': 'maintenance.db',
        'BackupEnabled': 'True',
        'BackupInterval': '24'
    }
    
    # حفظ الإعدادات
    config_file = get_config_file()
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            config.write(f)
        return True
    except Exception as e:
        print(f"❌ خطأ في حفظ الإعدادات: {e}")
        return False

def configure_firewall(port):
    """تكوين جدار الحماية للسماح بالمنفذ"""
    
    print(f"🔥 تكوين جدار الحماية للمنفذ {port}...")
    
    try:
        # إضافة قاعدة جدار الحماية
        rule_name = f"Maintenance System Port {port}"
        
        # حذف القاعدة إذا كانت موجودة
        subprocess.run([
            'netsh', 'advfirewall', 'firewall', 'delete', 'rule', 
            f'name={rule_name}'
        ], capture_output=True)
        
        # إضافة قاعدة جديدة
        result = subprocess.run([
            'netsh', 'advfirewall', 'firewall', 'add', 'rule',
            f'name={rule_name}',
            'dir=in',
            'action=allow',
            'protocol=TCP',
            f'localport={port}'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"✅ تم تكوين جدار الحماية بنجاح")
            return True
        else:
            print(f"⚠️ قد تحتاج لتشغيل البرنامج كمدير لتكوين جدار الحماية")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تكوين جدار الحماية: {e}")
        return False

def test_network_connectivity(port):
    """اختبار الاتصال الشبكي"""
    
    local_ip = get_local_ip()
    
    print(f"\n🌐 اختبار الاتصال الشبكي:")
    print(f"   📡 عنوان IP المحلي: {local_ip}")
    print(f"   🔌 المنفذ: {port}")
    
    # اختبار المنفذ
    if check_port_available(port):
        print(f"   ✅ المنفذ {port} متاح")
    else:
        print(f"   ❌ المنفذ {port} مستخدم")
        return False
    
    # عرض روابط الوصول
    print(f"\n🔗 روابط الوصول:")
    print(f"   🏠 محلي: http://127.0.0.1:{port}")
    print(f"   🌐 شبكي: http://{local_ip}:{port}")
    
    return True

def main():
    """الدالة الرئيسية"""
    
    print("🌐 أداة تكوين الشبكة - نظام إدارة الصيانة")
    print("🌐 Network Configuration Tool - Maintenance System")
    print("=" * 60)
    
    # الحصول على عنوان IP المحلي
    local_ip = get_local_ip()
    
    print(f"\n📊 معلومات الشبكة الحالية:")
    print(f"   📡 عنوان IP المحلي: {local_ip}")
    
    # اختيار نوع التكوين
    print(f"\n🔧 أنواع التكوين المتاحة:")
    print(f"   1. تكوين شبكي (عدة مستخدمين)")
    print(f"   2. تكوين خادم (للشبكة المحلية)")
    print(f"   3. اختبار الاتصال فقط")
    
    try:
        choice = input(f"\nاختر نوع التكوين (1-3): ").strip()
        
        if choice == "3":
            # اختبار الاتصال فقط
            port = int(input(f"أدخل المنفذ (افتراضي 5000): ") or "5000")
            test_network_connectivity(port)
            return
        
        if choice not in ["1", "2"]:
            print(f"❌ اختيار غير صحيح")
            return
        
        # اختيار المنفذ
        port = int(input(f"أدخل المنفذ (افتراضي 5000): ") or "5000")
        
        if not (1024 <= port <= 65535):
            print(f"❌ المنفذ يجب أن يكون بين 1024 و 65535")
            return
        
        # فحص توفر المنفذ
        if not check_port_available(port):
            print(f"❌ المنفذ {port} مستخدم، اختر منفذ آخر")
            return
        
        # إنشاء الإعدادات
        installation_type = int(choice)
        
        print(f"\n🔧 إنشاء إعدادات التكوين...")
        
        if create_network_config(installation_type, port):
            print(f"✅ تم إنشاء إعدادات التكوين بنجاح")
            
            # تكوين جدار الحماية
            firewall_choice = input(f"\nهل تريد تكوين جدار الحماية تلقائياً؟ (y/n): ").strip().lower()
            
            if firewall_choice in ['y', 'yes', 'نعم']:
                configure_firewall(port)
            
            # اختبار الاتصال
            test_network_connectivity(port)
            
            print(f"\n🎉 تم التكوين بنجاح!")
            print(f"\n📋 الخطوات التالية:")
            print(f"   1. شغل البرنامج: Maintenance_Management_System.exe")
            print(f"   2. من أجهزة أخرى، اذهب إلى: http://{local_ip}:{port}")
            print(f"   3. تأكد من أن جدار الحماية يسمح بالمنفذ {port}")
            
        else:
            print(f"❌ فشل في إنشاء إعدادات التكوين")
    
    except KeyboardInterrupt:
        print(f"\n⏹️ تم إلغاء التكوين")
    except ValueError:
        print(f"❌ يرجى إدخال رقم صحيح")
    except Exception as e:
        print(f"❌ خطأ غير متوقع: {e}")

if __name__ == "__main__":
    main()
    input(f"\nاضغط Enter للخروج...")
