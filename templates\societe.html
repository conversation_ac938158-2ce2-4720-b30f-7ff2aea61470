{% extends "base.html" %}

{% block title %}Informations Société - Système de Gestion de Maintenance{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Informations de la Société</h6>
                </div>
                <div class="card-body">
                    <form method="POST" action="" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}

                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.nom.label(class="form-label") }}
                                {% if form.nom.errors %}
                                    {{ form.nom(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.nom.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.nom(class="form-control") }}
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {{ form.responsable.label(class="form-label") }}
                                {% if form.responsable.errors %}
                                    {{ form.responsable(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.responsable.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.responsable(class="form-control") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.telephone.label(class="form-label") }}
                                {% if form.telephone.errors %}
                                    {{ form.telephone(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.telephone.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.telephone(class="form-control") }}
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {{ form.email.label(class="form-label") }}
                                {% if form.email.errors %}
                                    {{ form.email(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.email.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.email(class="form-control") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.adresse.label(class="form-label") }}
                            {% if form.adresse.errors %}
                                {{ form.adresse(class="form-control is-invalid", rows=3) }}
                                <div class="invalid-feedback">
                                    {% for error in form.adresse.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.adresse(class="form-control", rows=3) }}
                            {% endif %}
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-3">
                                {{ form.if_fiscal.label(class="form-label") }}
                                {% if form.if_fiscal.errors %}
                                    {{ form.if_fiscal(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.if_fiscal.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.if_fiscal(class="form-control") }}
                                {% endif %}
                            </div>
                            <div class="col-md-3">
                                {{ form.rc.label(class="form-label") }}
                                {% if form.rc.errors %}
                                    {{ form.rc(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.rc.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.rc(class="form-control") }}
                                {% endif %}
                            </div>
                            <div class="col-md-3">
                                {{ form.patente.label(class="form-label") }}
                                {% if form.patente.errors %}
                                    {{ form.patente(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.patente.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.patente(class="form-control") }}
                                {% endif %}
                            </div>
                            <div class="col-md-3">
                                {{ form.ice.label(class="form-label") }}
                                {% if form.ice.errors %}
                                    {{ form.ice(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.ice.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.ice(class="form-control") }}
                                {% endif %}
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                {{ form.cnss.label(class="form-label") }}
                                {% if form.cnss.errors %}
                                    {{ form.cnss(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.cnss.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.cnss(class="form-control") }}
                                {% endif %}
                            </div>
                            <div class="col-md-6">
                                {{ form.logo.label(class="form-label") }}
                                {% if form.logo.errors %}
                                    {{ form.logo(class="form-control is-invalid") }}
                                    <div class="invalid-feedback">
                                        {% for error in form.logo.errors %}
                                            <span>{{ error }}</span>
                                        {% endfor %}
                                    </div>
                                {% else %}
                                    {{ form.logo(class="form-control") }}
                                {% endif %}
                                {% if info.logo %}
                                <div class="mt-2">
                                    <img src="{{ url_for('static', filename='uploads/' + info.logo) }}" alt="Logo" class="img-thumbnail" style="max-height: 100px;">
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <div class="mb-3">
                            {{ form.pied_de_pages.label(class="form-label") }}
                            {% if form.pied_de_pages.errors %}
                                {{ form.pied_de_pages(class="form-control is-invalid", rows=3) }}
                                <div class="invalid-feedback">
                                    {% for error in form.pied_de_pages.errors %}
                                        <span>{{ error }}</span>
                                    {% endfor %}
                                </div>
                            {% else %}
                                {{ form.pied_de_pages(class="form-control", rows=3) }}
                                <small class="text-muted">{{ form.pied_de_pages.description }}</small>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            {{ form.submit(class="btn btn-primary") }}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
