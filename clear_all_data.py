#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حذف جميع بيانات الشركة وترك الخانات فارغة تماماً
"""

import sqlite3
import os

def clear_all_company_data():
    """حذف جميع بيانات الشركة وترك الخانات فارغة"""
    print("🧹 حذف جميع بيانات الشركة...")
    print("=" * 50)
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()
        
        # عرض البيانات الحالية
        print("📊 البيانات الحالية:")
        cursor.execute("SELECT * FROM societe")
        rows = cursor.fetchall()
        for i, row in enumerate(rows):
            print(f"   الصف {i+1}: {row}")
        
        # حذف جميع البيانات من جدول الشركة
        print("\n🗑️ حذف جميع البيانات...")
        cursor.execute("DELETE FROM societe")
        deleted_rows = cursor.rowcount
        print(f"   تم حذف {deleted_rows} صف(وف)")
        
        # إدراج صف واحد بجميع الخانات فارغة
        print("\n📝 إنشاء صف فارغ...")
        cursor.execute('''
        INSERT INTO societe (nom, logo, telephone, responsable, email, adresse, if_fiscal, rc, patente, ice, cnss, pied_page)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            '',  # اسم فارغ
            None,  # لا يوجد شعار
            '',  # تلفون فارغ
            '',  # مسؤول فارغ
            '',  # إيميل فارغ
            '',  # عنوان فارغ
            '',  # IF فارغ
            '',  # RC فارغ
            '',  # Patente فارغ
            '',  # ICE فارغ
            '',  # CNSS فارغ
            ''   # Pied de page فارغ
        ))
        
        # حفظ التغييرات
        conn.commit()
        print("✅ تم إنشاء صف بخانات فارغة")
        
        # عرض البيانات الجديدة للتأكيد
        print("\n📊 البيانات بعد التنظيف:")
        cursor.execute("SELECT * FROM societe")
        rows = cursor.fetchall()
        for i, row in enumerate(rows):
            print(f"   الصف {i+1}: {row}")
        
        conn.close()
        
        # حذف جميع ملفات الشعار
        print("\n🗂️ تنظيف مجلد uploads...")
        uploads_dir = 'static/uploads'
        if os.path.exists(uploads_dir):
            files = os.listdir(uploads_dir)
            logo_files = [f for f in files if f.startswith('logo_')]
            
            if logo_files:
                for logo_file in logo_files:
                    file_path = os.path.join(uploads_dir, logo_file)
                    try:
                        os.remove(file_path)
                        print(f"   🗑️ تم حذف {logo_file}")
                    except Exception as e:
                        print(f"   ❌ خطأ في حذف {logo_file}: {e}")
            else:
                print("   📁 لا توجد ملفات شعار للحذف")
        
        print("\n✅ تم تنظيف جميع البيانات بنجاح!")
        print("🎯 الآن جميع الخانات فارغة ويمكنك:")
        print("   1. تحديث صفحة المتصفح")
        print("   2. ملء معلومات الشركة من الصفر")
        print("   3. تحميل شعار جديد")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

if __name__ == "__main__":
    print("🧹 أداة تنظيف بيانات الشركة")
    print("=" * 50)
    print("⚠️ تحذير: هذا سيحذف جميع بيانات الشركة!")
    print("📝 سيتم ترك جميع الخانات فارغة تماماً")
    
    confirm = input("\nهل أنت متأكد؟ اكتب 'نعم' للمتابعة: ").strip()
    
    if confirm.lower() in ['نعم', 'yes', 'y']:
        if clear_all_company_data():
            print("\n🎉 تمت عملية التنظيف بنجاح!")
            print("🔄 يرجى تحديث صفحة المتصفح لرؤية التغييرات")
        else:
            print("\n❌ فشلت عملية التنظيف!")
    else:
        print("🚫 تم إلغاء العملية")
