#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Launcher للتطبيق - يتعامل مع التشغيل كملف تنفيذي
"""

import os
import sys
import threading
import time
import webbrowser
import socket
import subprocess
import tkinter as tk
from tkinter import messagebox
from contextlib import closing

# إضافة المسار الحالي إلى sys.path
if getattr(sys, 'frozen', False):
    # Running as compiled executable
    application_path = sys._MEIPASS
    # للملف التنفيذي، استخدم مجلد الملف التنفيذي للبيانات
    data_path = os.path.dirname(sys.executable)
else:
    # Running as script
    application_path = os.path.dirname(os.path.abspath(__file__))
    data_path = application_path

sys.path.insert(0, application_path)
os.chdir(data_path)

def find_free_port():
    """البحث عن منفذ متاح"""
    with closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as s:
        s.bind(('', 0))
        s.listen(1)
        port = s.getsockname()[1]
    return port

def show_startup_message(port):
    """عرض رسالة بدء التشغيل"""
    try:
        root = tk.Tk()
        root.withdraw()  # إخفاء النافذة الرئيسية

        message = f"""🚀 نظام إدارة الصيانة يعمل الآن!

🌐 الرابط المحلي: http://127.0.0.1:{port}
📱 للوصول من أجهزة أخرى: http://[IP]:{port}

سيتم فتح المتصفح تلقائياً...
إذا لم يفتح، انسخ الرابط أعلاه في المتصفح."""

        messagebox.showinfo("نظام إدارة الصيانة", message)
        root.destroy()
    except:
        pass

def open_browser(port):
    """فتح المتصفح بعد تأخير قصير"""
    time.sleep(3)  # انتظار حتى يبدأ الخادم

    url = f'http://127.0.0.1:{port}'

    try:
        # محاولة فتح المتصفح بطرق مختلفة
        if sys.platform.startswith('win'):
            # Windows
            os.startfile(url)
        else:
            # طرق أخرى
            webbrowser.open(url)
    except:
        try:
            # محاولة أخرى باستخدام subprocess
            subprocess.run(['start', url], shell=True, check=False)
        except:
            try:
                # محاولة أخيرة
                webbrowser.open_new_tab(url)
            except:
                pass

def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    try:
        # استيراد التطبيق
        from app import app

        # البحث عن منفذ متاح
        port = find_free_port()

        # عرض رسالة بدء التشغيل
        message_thread = threading.Thread(target=show_startup_message, args=(port,))
        message_thread.daemon = True
        message_thread.start()

        # تشغيل المتصفح في thread منفصل
        browser_thread = threading.Thread(target=open_browser, args=(port,))
        browser_thread.daemon = True
        browser_thread.start()

        # تشغيل التطبيق
        print(f"🚀 تشغيل نظام إدارة الصيانة على المنفذ {port}")
        print(f"🌐 الرابط: http://127.0.0.1:{port}")
        print("📝 للإيقاف: اضغط Ctrl+C")

        app.run(
            host='0.0.0.0',  # للسماح بالوصول من أجهزة أخرى
            port=port,
            debug=False,
            threaded=True,  # للسماح بعدة مستخدمين
            use_reloader=False
        )

    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق")
        sys.exit(0)
    except ImportError as e:
        error_msg = f"❌ خطأ في استيراد المكتبات: {e}\n\nتأكد من وجود جميع الملفات المطلوبة."
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ في التشغيل", error_msg)
            root.destroy()
        except:
            print(error_msg)
        sys.exit(1)
    except Exception as e:
        error_msg = f"❌ خطأ في تشغيل التطبيق: {e}"
        try:
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror("خطأ في التشغيل", error_msg)
            root.destroy()
        except:
            print(error_msg)
        sys.exit(1)

if __name__ == '__main__':
    main()
