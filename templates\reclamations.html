{% extends "base.html" %}

{% block title %}Gestion des Réclamations - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Gestion des Réclamations{% endblock %}

{% block content %}
<div class="container-fluid mt-4 mb-5">
  <!-- Header moderne -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-lg border-0">
        <div class="card-header bg-gradient-danger text-white py-4">
          <div class="d-flex justify-content-between align-items-center w-100">
            <div class="header-left">
              <div class="d-flex align-items-center">
                <div class="icon-circle bg-white bg-opacity-20 me-3">
                  <i class="fas fa-exclamation-triangle text-white"></i>
                </div>
                <div>
                  <h3 class="mb-0 fw-bold">Gestion des Réclamations</h3>
                  <small class="opacity-75"><PERSON><PERSON><PERSON> et traitez toutes les réclamations clients</small>
                </div>
              </div>
            </div>
            <div class="header-right">
              <div class="btn-group" role="group">
                <a href="{{ url_for('ajouter_reclamation') }}" class="btn btn-primary btn-sm">
                  <i class="fas fa-plus"></i> Ajouter
                </a>
                <button type="button" class="btn btn-light btn-sm" onclick="window.print()">
                  <i class="fas fa-print"></i> Imprimer
                </button>
                <button type="button" class="btn btn-success btn-sm" id="exportExcel">
                  <i class="fas fa-file-excel"></i> Excel
                </button>
                <button type="button" class="btn btn-info btn-sm" onclick="location.reload()">
                  <i class="fas fa-sync-alt"></i> Actualiser
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>



  <!-- Section des filtres -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-sm border-0">
        <div class="card-header bg-gradient-light">
          <h6 class="mb-0 text-dark fw-bold">
            <i class="fas fa-filter me-2 text-danger"></i>Filtres de recherche
          </h6>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-3">
              <div class="form-floating">
                <input type="text" class="form-control" id="searchReclamation" placeholder="Rechercher...">
                <label for="searchReclamation"><i class="fas fa-search me-1"></i>Rechercher</label>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-floating">
                <select class="form-select" id="filterDomaine">
                  <option value="">Tous les domaines</option>
                  <option value="SVS">SVS</option>
                  <option value="EXTINCTEUR">EXTINCTEUR</option>
                  <option value="SYSTEME D'INCENDIE">SYSTEME D'INCENDIE</option>
                  <option value="SYSTEME D'ALARME">SYSTEME D'ALARME</option>
                  <option value="SYSTEME TELEPHONIQUE">SYSTEME TELEPHONIQUE</option>
                  <option value="L'AFFICHAGE DYNAMIQUE">L'AFFICHAGE DYNAMIQUE</option>
                </select>
                <label for="filterDomaine"><i class="fas fa-tags me-1"></i>Domaine</label>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-floating">
                <input type="text" class="form-control" id="filterClient" placeholder="Client...">
                <label for="filterClient"><i class="fas fa-user me-1"></i>Client</label>
              </div>
            </div>
            <div class="col-md-3">
              <div class="d-flex gap-2">
                <button type="button" class="btn btn-danger btn-modern flex-fill" id="applyFilters">
                  <i class="fas fa-search me-1"></i>Filtrer
                </button>
                <button type="button" class="btn btn-outline-secondary btn-modern" id="resetFilters">
                  <i class="fas fa-undo me-1"></i>Reset
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Tableau des réclamations -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow-lg border-0">
        <div class="card-header bg-gradient-warning text-dark">
          <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0 fw-bold">
              <i class="fas fa-list me-2"></i>Liste des Réclamations
            </h6>
            <span class="badge bg-danger">{{ reclamations|length if reclamations else 0 }} réclamations</span>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0" id="reclamationsTable">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold"><i class="fas fa-hashtag me-1"></i>ID</th>
                  <th class="fw-bold"><i class="fas fa-file-contract me-1"></i>N° Marché</th>
                  <th class="fw-bold"><i class="fas fa-tags me-1"></i>Domaine</th>
                  <th class="fw-bold"><i class="fas fa-user-tie me-1"></i>Client</th>
                  <th class="fw-bold"><i class="fas fa-map-marker-alt me-1"></i>Lieu</th>
                  <th class="fw-bold"><i class="fas fa-clock me-1"></i>Date création</th>
                  <th class="fw-bold text-center"><i class="fas fa-cogs me-1"></i>Actions</th>
                </tr>
              </thead>
                        <tbody>
                            {% if reclamations %}
                                {% for reclamation in reclamations %}
                                <tr>
                                    <td>{{ reclamation.id }}</td>
                                    <td>{{ reclamation.numero_marche }}</td>
                                    <td>
                                        {% if reclamation.domaine == 'SVS' %}
                                        <span class="badge bg-primary">SVS</span>
                                        {% elif reclamation.domaine == 'EXTINCTEUR' %}
                                        <span class="badge bg-danger">EXTINCTEUR</span>
                                        {% elif reclamation.domaine == 'SYSTEME D\'INCENDIE' %}
                                        <span class="badge bg-warning">SYSTEME D'INCENDIE</span>
                                        {% elif reclamation.domaine == 'SYSTEME D\'ALARME' %}
                                        <span class="badge bg-info">SYSTEME D'ALARME</span>
                                        {% elif reclamation.domaine == 'SYSTEME TELEPHONIQUE' %}
                                        <span class="badge bg-success">SYSTEME TELEPHONIQUE</span>
                                        {% elif reclamation.domaine == "L'AFFICHAGE DYNAMIQUE" %}
                                        <span class="badge bg-dark">L'AFFICHAGE DYNAMIQUE</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ reclamation.domaine }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ reclamation.client }}</td>
                                    <td>{{ reclamation.lieu }}</td>
                                    <td>{{ reclamation.date_creation }}</td>
                                    <td>
                                        <a href="{{ url_for('voir_reclamation', id=reclamation.id) }}" class="btn btn-sm btn-info" title="Voir les détails">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('rapport_reclamation', id=reclamation.id) }}" class="btn btn-sm btn-success" title="Rapport d'impression" target="_blank">
                                            <i class="fas fa-file-alt"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-danger"
                                                onclick="confirmDelete('la réclamation #{{ reclamation.id }}', '{{ url_for('supprimer_reclamation', id=reclamation.id) }}')"
                                                title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="7" class="text-center">Aucune réclamation trouvée</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
  .card {
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  }

  .table th {
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6 !important;
    font-weight: 600 !important;
    padding: 15px 12px !important;
    font-size: 0.9rem !important;
  }

  .table td {
    padding: 15px 12px !important;
    vertical-align: middle !important;
    border-bottom: 1px solid #dee2e6 !important;
  }

  .table-hover tbody tr:hover {
    background-color: rgba(220,53,69,0.05) !important;
  }

  .btn-group .btn {
    margin: 0 1px !important;
    border-radius: 6px !important;
  }

  .badge {
    font-size: 0.75rem !important;
    padding: 6px 10px !important;
    font-weight: 500 !important;
  }

  .text-white-75 {
    color: rgba(255,255,255,0.75) !important;
  }

  .text-white-50 {
    color: rgba(255,255,255,0.5) !important;
  }

  .card-header {
    border-bottom: 1px solid rgba(0,0,0,0.125) !important;
    padding: 1rem 1.25rem !important;
  }

  .card-body {
    padding: 1.25rem !important;
  }

  .bg-gradient-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
  }

  .bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
  }

  .bg-gradient-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  }

  @media (max-width: 768px) {
    .btn-group {
      flex-direction: column !important;
    }

    .btn-group .btn {
      margin: 2px 0 !important;
    }

    .table-responsive {
      font-size: 0.85rem !important;
    }

    .card-body {
      padding: 1rem !important;
    }
  }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Export Excel
        document.getElementById('exportExcel').addEventListener('click', function() {
            exportTableToExcel('reclamationsTable', 'reclamations');
        });
    });
</script>
{% endblock %}
