#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مباشر في الوضع الشبكي
"""

import os
import sys
import socket
import subprocess
import time
import threading
import webbrowser

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "*************"

def configure_firewall():
    """تكوين جدار الحماية"""
    try:
        print("🔥 تكوين جدار الحماية...")
        
        # حذف القاعدة إذا كانت موجودة
        subprocess.run([
            'netsh', 'advfirewall', 'firewall', 'delete', 'rule', 
            'name=Maintenance System Port 5000'
        ], capture_output=True)
        
        # إضافة قاعدة جديدة
        result = subprocess.run([
            'netsh', 'advfirewall', 'firewall', 'add', 'rule',
            'name=Maintenance System Port 5000',
            'dir=in',
            'action=allow',
            'protocol=TCP',
            'localport=5000'
        ], capture_output=True)
        
        if result.returncode == 0:
            print("✅ تم تكوين جدار الحماية بنجاح")
        else:
            print("⚠️ قد تحتاج لتشغيل البرنامج كمدير لتكوين جدار الحماية")
            
    except Exception as e:
        print(f"❌ خطأ في تكوين جدار الحماية: {e}")

def create_network_config():
    """إنشاء ملف تكوين شبكي"""
    config_content = """[Settings]
InstallationType=1
Port=5000
MultiUser=True
ServerMode=True
Version=1.0.0

[Network]
Host=0.0.0.0
AllowExternalAccess=True

[Database]
Path=maintenance.db
BackupEnabled=True
BackupInterval=24
"""
    
    try:
        with open('config.ini', 'w', encoding='utf-8') as f:
            f.write(config_content)
        print("✅ تم إنشاء ملف التكوين الشبكي")
        return True
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف التكوين: {e}")
        return False

def open_browser_delayed(url, delay=5):
    """فتح المتصفح بعد تأخير"""
    time.sleep(delay)
    try:
        webbrowser.open(url)
        print(f"🌐 تم فتح المتصفح: {url}")
    except Exception as e:
        print(f"❌ فشل في فتح المتصفح: {e}")

def main():
    """الدالة الرئيسية"""
    
    print("🌐 تشغيل نظام إدارة الصيانة - الوضع الشبكي")
    print("🌐 Maintenance Management System - Network Mode")
    print("=" * 60)
    
    # الحصول على عنوان IP المحلي
    local_ip = get_local_ip()
    port = 5000
    
    print(f"📡 عنوان IP المحلي: {local_ip}")
    print(f"🔌 المنفذ: {port}")
    print()
    
    # إنشاء ملف التكوين الشبكي
    print("🔧 إعداد التكوين الشبكي...")
    if not create_network_config():
        print("❌ فشل في إعداد التكوين")
        input("اضغط Enter للخروج...")
        return
    
    # تكوين جدار الحماية
    configure_firewall()
    
    print()
    print("🚀 تشغيل النظام...")
    print()
    print("📊 معلومات الاتصال:")
    print(f"   🏠 محلي: http://127.0.0.1:{port}")
    print(f"   🌐 شبكي: http://{local_ip}:{port}")
    print()
    print("📋 للوصول من أجهزة أخرى:")
    print(f"   1. تأكد من اتصال الجهاز بنفس الشبكة")
    print(f"   2. افتح المتصفح واذهب إلى: http://{local_ip}:{port}")
    print(f"   3. استخدم admin/admin123 لتسجيل الدخول")
    print()
    
    # فتح المتصفح في thread منفصل
    browser_thread = threading.Thread(
        target=open_browser_delayed, 
        args=(f"http://127.0.0.1:{port}",)
    )
    browser_thread.daemon = True
    browser_thread.start()
    
    # تشغيل التطبيق
    try:
        # إضافة مسار التطبيق
        if getattr(sys, 'frozen', False):
            app_dir = os.path.dirname(sys.executable)
        else:
            app_dir = os.path.dirname(os.path.abspath(__file__))
        
        sys.path.insert(0, app_dir)
        
        # استيراد وتشغيل التطبيق
        from app import app
        
        print("🔥 الخادم يعمل الآن...")
        print("🌍 يمكن الوصول من أي جهاز في الشبكة المحلية")
        print("⏹️ لإيقاف النظام: اضغط Ctrl+C")
        print()
        
        app.run(
            host='0.0.0.0',  # قبول الاتصالات من أي جهاز
            port=port,
            debug=False,
            threaded=True,   # دعم عدة مستخدمين
            use_reloader=False
        )
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف النظام بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("\n💡 تأكد من:")
        print("   - وجود ملف app.py")
        print("   - تثبيت جميع المكتبات المطلوبة")
        print("   - عدم استخدام المنفذ 5000 من برنامج آخر")
        input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
