{% extends "base.html" %}

{% block title %}Modifier l'Intervention - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Modifier l'Intervention{% endblock %}

{% block content %}
<div class="container mt-4 mb-5">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="text-primary"><i class="fas fa-edit me-2"></i> Modifier l'intervention #{{ intervention.id }}</h4>
    <a href="{{ url_for('details_intervention', id=intervention.id) }}" class="btn btn-outline-secondary">
      <i class="fas fa-arrow-left me-1"></i> Retour aux détails
    </a>
  </div>

  <!-- Formulaire de modification -->
  <div class="card shadow-sm">
    <div class="card-header bg-light">
      <h5 class="mb-0"><i class="fas fa-edit me-2"></i> Informations de l'intervention</h5>
    </div>
    <div class="card-body">
      <form method="POST" action="{{ url_for('modifier_intervention', id=intervention.id) }}">
        <div class="row mb-4">
          <div class="col-md-6 mb-3">
            <label for="client" class="form-label">Client</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-user"></i></span>
              </div>
              <input type="text" class="form-control" id="client" name="client" value="{{ intervention.client }}" required>
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <label for="numero_marche" class="form-label">N° de marché</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
              </div>
              <select class="form-select" id="numero_marche" name="numero_marche" required>
                <option value="">Sélectionner un marché</option>
                {% for marche in marches %}
                <option value="{{ marche.numero }}" {% if marche.numero == intervention.numero_marche %}selected{% endif %}>
                  {{ marche.numero }} - {{ marche.objet }}
                </option>
                {% endfor %}
              </select>
            </div>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-6 mb-3">
            <label for="objet_marche" class="form-label">Objet de marché</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-file-alt"></i></span>
              </div>
              <input type="text" class="form-control" id="objet_marche" name="objet_marche" value="{{ intervention.objet_marche }}" required>
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <label for="delai_execution" class="form-label">Délai d'exécution</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-clock"></i></span>
              </div>
              <input type="text" class="form-control" id="delai_execution" name="delai_execution" value="{{ intervention.delai_execution }}" required>
            </div>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-6 mb-3">
            <label for="domaine" class="form-label">Domaine</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-cogs"></i></span>
              </div>
              <select class="form-select" id="domaine" name="domaine" required>
                <option value="">Sélectionner un domaine</option>
                <option value="Incendie" {% if intervention.domaine == 'Incendie' %}selected{% endif %}>Incendie</option>
                <option value="Camera" {% if intervention.domaine == 'Camera' %}selected{% endif %}>Camera</option>
                <option value="Téléphonée" {% if intervention.domaine == 'Téléphonée' %}selected{% endif %}>Téléphonée</option>
                <option value="Extincteur" {% if intervention.domaine == 'Extincteur' %}selected{% endif %}>Extincteur</option>
                <option value="SVS" {% if intervention.domaine == 'SVS' %}selected{% endif %}>SVS</option>
                <option value="SYSTEME D'INCENDIE" {% if intervention.domaine == "SYSTEME D'INCENDIE" %}selected{% endif %}>SYSTEME D'INCENDIE</option>
                <option value="SYSTEME D'ALARME" {% if intervention.domaine == "SYSTEME D'ALARME" %}selected{% endif %}>SYSTEME D'ALARME</option>
                <option value="SYSTEME TELEPHONIQUE" {% if intervention.domaine == 'SYSTEME TELEPHONIQUE' %}selected{% endif %}>SYSTEME TELEPHONIQUE</option>
              </select>
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <label for="periode_interventions" class="form-label">Période d'interventions</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-calendar"></i></span>
              </div>
              <input type="text" class="form-control" id="periode_interventions" name="periode_interventions" value="{{ intervention.periode_interventions }}" required>
            </div>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-6 mb-3">
            <label for="semestre" class="form-label">Semestre</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
              </div>
              <select class="form-select" id="semestre" name="semestre">
                <option value="">Sélectionner un semestre</option>
                <option value="1er Semestre" {% if intervention.semestre == '1er Semestre' %}selected{% endif %}>1er Semestre</option>
                <option value="2ème Semestre" {% if intervention.semestre == '2ème Semestre' %}selected{% endif %}>2ème Semestre</option>
              </select>
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <label for="periode_marche" class="form-label">Période de marché</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-calendar-check"></i></span>
              </div>
              <input type="text" class="form-control" id="periode_marche" name="periode_marche" value="{{ intervention.periode_marche }}" required>
            </div>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-12 mb-3">
            <label for="lieu" class="form-label">Lieu</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
              </div>
              <input type="text" class="form-control" id="lieu" name="lieu" value="{{ intervention.lieu }}" required>
            </div>
          </div>
        </div>

        <!-- Boutons d'action -->
        <div class="d-flex justify-content-between mt-4">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-1"></i> Enregistrer les modifications
          </button>
          <div>
            <button type="reset" class="btn btn-outline-secondary me-2">
              <i class="fas fa-undo me-1"></i> Réinitialiser
            </button>
            <a href="{{ url_for('details_intervention', id=intervention.id) }}" class="btn btn-outline-danger">
              <i class="fas fa-times me-1"></i> Annuler
            </a>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
  .input-group {
    margin-bottom: 0;
    border-radius: 0.25rem;
    transition: all 0.2s ease-in-out;
  }
  
  .input-group-prepend {
    display: flex;
  }
  
  .input-group-text {
    display: flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: center;
    white-space: nowrap;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    border-radius: 0.25rem 0 0 0.25rem;
  }
  
  .form-select, .form-control {
    border-radius: 0 0.25rem 0.25rem 0;
  }
</style>
{% endblock %}
