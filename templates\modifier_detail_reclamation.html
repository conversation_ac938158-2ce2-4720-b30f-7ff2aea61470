{% extends "base.html" %}

{% block title %}Modifier un Détail de Réclamation - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Modifier un Détail de Réclamation{% endblock %}

{% block extra_css %}
<style>
  /* Styles pour les input-group */
  .input-group {
    margin-bottom: 0;
    border-radius: 0.25rem;
    transition: all 0.2s ease-in-out;
  }
  
  .input-group-prepend {
    display: flex;
  }
  
  .input-group-text {
    display: flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: center;
    white-space: nowrap;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    border-radius: 0.25rem 0 0 0.25rem;
  }
  
  .form-select, .form-control {
    border-radius: 0 0.25rem 0.25rem 0;
  }
  
  .input-group:focus-within {
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  }
  
  .input-group:focus-within .input-group-text {
    border-color: #80bdff;
    background-color: #e7f1ff;
  }
  
  .card {
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: box-shadow 0.15s ease-in-out;
  }
  
  .card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  }
  
  .form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.5rem;
  }
  
  .btn {
    border-radius: 0.25rem;
    font-weight: 500;
    transition: all 0.2s ease-in-out;
  }
  
  .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
  }
  
  .text-primary {
    color: #007bff !important;
  }
  
  .bg-light {
    background-color: #f8f9fa !important;
  }
  
  .shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
  }
  
  .rounded {
    border-radius: 0.25rem !important;
  }
  
  .mb-3 {
    margin-bottom: 1rem !important;
  }
  
  .mb-4 {
    margin-bottom: 1.5rem !important;
  }
  
  .mb-5 {
    margin-bottom: 3rem !important;
  }
  
  .mt-4 {
    margin-top: 1.5rem !important;
  }
  
  .me-1 {
    margin-right: 0.25rem !important;
  }
  
  .me-2 {
    margin-right: 0.5rem !important;
  }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4 mb-5">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="text-primary"><i class="fas fa-edit me-2"></i> Modifier le détail #{{ detail.id }}</h4>
    <a href="{{ url_for('voir_reclamation', id=reclamation.id) }}" class="btn btn-outline-secondary">
      <i class="fas fa-arrow-left me-1"></i> Retour aux détails
    </a>
  </div>

  <!-- Informations de la réclamation -->
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-light">
      <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> Informations de la réclamation</h5>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-3">
          <p><strong>N° de marché:</strong> {{ reclamation.numero_marche }}</p>
        </div>
        <div class="col-md-3">
          <p><strong>Client:</strong> {{ reclamation.client }}</p>
        </div>
        <div class="col-md-3">
          <p><strong>Domaine:</strong> {{ reclamation.domaine }}</p>
        </div>
        <div class="col-md-3">
          <p><strong>Lieu:</strong> {{ reclamation.lieu }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Formulaire de modification de détail -->
  <div class="card shadow-sm">
    <div class="card-header bg-light">
      <h5 class="mb-0"><i class="fas fa-list-alt me-2"></i> Modifier les détails de l'intervention</h5>
    </div>
    <div class="card-body">
      <form method="POST" action="{{ url_for('modifier_detail_reclamation', id=detail.id) }}">
        <div class="row mb-4">
          <div class="col-md-4 mb-3">
            <label for="region" class="form-label">Région</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-map"></i></span>
              </div>
              <select class="form-select" id="region" name="region" required>
                <option value="">Sélectionner une région</option>
                {% for region in regions %}
                <option value="{{ region.nom }}" {% if detail.region == region.nom %}selected{% endif %}>{{ region.nom }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="nom_site" class="form-label">Nom du site</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-building"></i></span>
              </div>
              <input type="text" class="form-control" id="nom_site" name="nom_site" value="{{ detail.nom_site }}" required>
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="nbr_systeme" class="form-label">Nombre de systèmes</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-hashtag"></i></span>
              </div>
              <input type="number" class="form-control" id="nbr_systeme" name="nbr_systeme" value="{{ detail.nbr_systeme }}" min="1" required>
            </div>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-6 mb-3">
            <label for="date_reclamation" class="form-label">Date de réclamation</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
              </div>
              <input type="date" class="form-control" id="date_reclamation" name="date_reclamation" value="{{ detail.date_reclamation }}" required>
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <label for="date_intervention" class="form-label">Date d'intervention</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-calendar-check"></i></span>
              </div>
              <input type="date" class="form-control" id="date_intervention" name="date_intervention" value="{{ detail.date_intervention }}" required>
            </div>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-4 mb-3">
            <label for="technicien" class="form-label">Technicien</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-user-cog"></i></span>
              </div>
              <input type="text" class="form-control" id="technicien" name="technicien" value="{{ detail.technicien }}" required>
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="situation" class="form-label">Situation</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-info-circle"></i></span>
              </div>
              <select class="form-select" id="situation" name="situation" required>
                <option value="">Sélectionner une situation</option>
                <option value="Réglé" {% if detail.situation == 'Réglé' %}selected{% endif %}>Réglé</option>
                <option value="Pas encore" {% if detail.situation == 'Pas encore' %}selected{% endif %}>Pas encore</option>
                <option value="Problème" {% if detail.situation == 'Problème' %}selected{% endif %}>Problème</option>
              </select>
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="etat_materiel" class="form-label">État du matériel</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-cogs"></i></span>
              </div>
              <select class="form-select" id="etat_materiel" name="etat_materiel" required>
                <option value="">Sélectionner un état</option>
                <option value="Opérationnel" {% if detail.etat_materiel == 'Opérationnel' %}selected{% endif %}>Opérationnel</option>
                <option value="Non Opérationnel" {% if detail.etat_materiel == 'Non Opérationnel' %}selected{% endif %}>Non Opérationnel</option>
                <option value="En maintenance" {% if detail.etat_materiel == 'En maintenance' %}selected{% endif %}>En maintenance</option>
              </select>
            </div>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-12 mb-3">
            <label for="observation" class="form-label">Observation</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-comment"></i></span>
              </div>
              <textarea class="form-control" id="observation" name="observation" rows="4" placeholder="Détails de l'observation...">{{ detail.observation }}</textarea>
            </div>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-4 mb-3">
            <label for="telephone_chef_site" class="form-label">Téléphone chef de site</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-phone"></i></span>
              </div>
              <input type="text" class="form-control" id="telephone_chef_site" name="telephone_chef_site" value="{{ detail.telephone_chef_site }}" placeholder="Ex: +212 6 XX XX XX XX">
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="telephone_securite" class="form-label">Téléphone de sécurité</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-shield-alt"></i></span>
              </div>
              <input type="text" class="form-control" id="telephone_securite" name="telephone_securite" value="{{ detail.telephone_securite }}" placeholder="Ex: +212 6 XX XX XX XX">
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="technicien_contact" class="form-label">Technicien à contacter</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-user-tie"></i></span>
              </div>
              <input type="text" class="form-control" id="technicien_contact" name="technicien_contact" value="{{ detail.technicien_contact }}" placeholder="Nom du technicien">
            </div>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-12 mb-3">
            <label for="gps" class="form-label">Coordonnées GPS</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
              </div>
              <input type="text" class="form-control" id="gps" name="gps" value="{{ detail.gps }}" placeholder="Ex: 31.6295, -7.9811">
            </div>
          </div>
        </div>

        <div class="d-flex justify-content-end gap-2">
          <a href="{{ url_for('voir_reclamation', id=reclamation.id) }}" class="btn btn-secondary">
            <i class="fas fa-times me-1"></i> Annuler
          </a>
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-1"></i> Enregistrer les modifications
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}
