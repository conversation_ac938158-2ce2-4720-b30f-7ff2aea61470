{% extends "base.html" %}

{% block title %}Gestion des Interventions - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Gestion des Interventions{% endblock %}

{% block content %}
<div class="container-fluid mt-4 mb-5">
  <!-- Header moderne -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-lg border-0">
        <div class="card-header bg-gradient-success text-white py-4">
          <div class="d-flex justify-content-between align-items-center w-100">
            <div class="header-left">
              <div class="d-flex align-items-center">
                <div class="icon-circle bg-white bg-opacity-20 me-3">
                  <i class="fas fa-tools text-white"></i>
                </div>
                <div>
                  <h3 class="mb-0 fw-bold">Gestion des Interventions</h3>
                  <small class="opacity-75"><PERSON><PERSON><PERSON> et gérez toutes vos interventions techniques</small>
                </div>
              </div>
            </div>
            <div class="header-right">
              <div class="btn-group" role="group">
                <a href="{{ url_for('ajouter_intervention') }}" class="btn btn-primary btn-sm">
                  <i class="fas fa-plus"></i> Ajouter
                </a>
                <button type="button" class="btn btn-light btn-sm" onclick="window.print()">
                  <i class="fas fa-print"></i> Imprimer
                </button>
                <button type="button" class="btn btn-success btn-sm" id="exportExcel">
                  <i class="fas fa-file-excel"></i> Excel
                </button>
                <button type="button" class="btn btn-info btn-sm" onclick="location.reload()">
                  <i class="fas fa-sync-alt"></i> Actualiser
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>



  <!-- Section des filtres -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-sm border-0">
        <div class="card-header bg-gradient-light">
          <h6 class="mb-0 text-dark fw-bold">
            <i class="fas fa-filter me-2 text-success"></i>Filtres de recherche
          </h6>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-3">
              <div class="form-floating">
                <input type="text" class="form-control" id="searchIntervention" placeholder="Rechercher...">
                <label for="searchIntervention"><i class="fas fa-search me-1"></i>Rechercher</label>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-floating">
                <select class="form-select" id="filterDomaine">
                  <option value="">Tous les domaines</option>
                  <option value="SVS">SVS</option>
                  <option value="EXTINCTEUR">EXTINCTEUR</option>
                  <option value="SYSTEME D'INCENDIE">SYSTEME D'INCENDIE</option>
                  <option value="SYSTEME D'ALARME">SYSTEME D'ALARME</option>
                  <option value="SYSTEME TELEPHONIQUE">SYSTEME TELEPHONIQUE</option>
                  <option value="L'AFFICHAGE DYNAMIQUE">L'AFFICHAGE DYNAMIQUE</option>
                </select>
                <label for="filterDomaine"><i class="fas fa-tags me-1"></i>Domaine</label>
              </div>
            </div>
            <div class="col-md-3">
              <div class="form-floating">
                <input type="text" class="form-control" id="filterClient" placeholder="Client...">
                <label for="filterClient"><i class="fas fa-user me-1"></i>Client</label>
              </div>
            </div>
            <div class="col-md-3">
              <div class="d-flex gap-2">
                <button type="button" class="btn btn-success btn-modern flex-fill" id="applyFilters">
                  <i class="fas fa-search me-1"></i>Filtrer
                </button>
                <button type="button" class="btn btn-outline-secondary btn-modern" id="resetFilters">
                  <i class="fas fa-undo me-1"></i>Reset
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Tableau des interventions -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow-lg border-0">
        <div class="card-header bg-gradient-warning text-dark">
          <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0 fw-bold">
              <i class="fas fa-list me-2"></i>Liste des Interventions
            </h6>
            <span class="badge bg-dark">{{ interventions|length if interventions else 0 }} interventions</span>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0" id="interventionsTable">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold"><i class="fas fa-hashtag me-1"></i>ID</th>
                  <th class="fw-bold"><i class="fas fa-file-contract me-1"></i>N° Marché</th>
                  <th class="fw-bold"><i class="fas fa-tags me-1"></i>Domaine</th>
                  <th class="fw-bold"><i class="fas fa-user-tie me-1"></i>Client</th>
                  <th class="fw-bold"><i class="fas fa-map-marker-alt me-1"></i>Lieu</th>
                  <th class="fw-bold"><i class="fas fa-calendar-alt me-1"></i>Période</th>
                  <th class="fw-bold"><i class="fas fa-clock me-1"></i>Date création</th>
                  <th class="fw-bold text-center"><i class="fas fa-cogs me-1"></i>Actions</th>
                </tr>
              </thead>
                        <tbody>
                            {% if interventions %}
                                {% for intervention in interventions %}
                                <tr>
                                    <td>{{ intervention.id }}</td>
                                    <td>{{ intervention.numero_marche }}</td>
                                    <td>
                                        {% if intervention.domaine == 'SVS' %}
                                        <span class="badge bg-primary">SVS</span>
                                        {% elif intervention.domaine == 'EXTINCTEUR' %}
                                        <span class="badge bg-danger">EXTINCTEUR</span>
                                        {% elif intervention.domaine == 'SYSTEME D\'INCENDIE' %}
                                        <span class="badge bg-warning">SYSTEME D'INCENDIE</span>
                                        {% elif intervention.domaine == 'SYSTEME D\'ALARME' %}
                                        <span class="badge bg-info">SYSTEME D'ALARME</span>
                                        {% elif intervention.domaine == 'SYSTEME TELEPHONIQUE' %}
                                        <span class="badge bg-success">SYSTEME TELEPHONIQUE</span>
                                        {% elif intervention.domaine == "L'AFFICHAGE DYNAMIQUE" %}
                                        <span class="badge bg-dark">L'AFFICHAGE DYNAMIQUE</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ intervention.domaine }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ intervention.client }}</td>
                                    <td>{{ intervention.lieu }}</td>
                                    <td>{{ intervention.periode_interventions }}</td>
                                    <td>{{ intervention.date_creation }}</td>
                                    <td>
                                        <a href="{{ url_for('details_intervention', id=intervention.id) }}" class="btn btn-sm btn-info" title="Voir les détails">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ url_for('rapport_intervention', id=intervention.id) }}" class="btn btn-sm btn-success" title="Rapport d'impression" target="_blank">
                                            <i class="fas fa-file-alt"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-danger"
                                                onclick="confirmDelete('l\'intervention #{{ intervention.id }}', '{{ url_for('supprimer_intervention', id=intervention.id) }}')"
                                                title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="8" class="text-center">Aucune intervention trouvée</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
  .card {
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  }

  .table th {
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6 !important;
    font-weight: 600 !important;
    padding: 15px 12px !important;
    font-size: 0.9rem !important;
  }

  .table td {
    padding: 15px 12px !important;
    vertical-align: middle !important;
    border-bottom: 1px solid #dee2e6 !important;
  }

  .table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.05) !important;
  }

  .btn-group .btn {
    margin: 0 1px !important;
    border-radius: 6px !important;
  }

  .badge {
    font-size: 0.75rem !important;
    padding: 6px 10px !important;
    font-weight: 500 !important;
  }

  .text-white-75 {
    color: rgba(255,255,255,0.75) !important;
  }

  .text-white-50 {
    color: rgba(255,255,255,0.5) !important;
  }

  .card-header {
    border-bottom: 1px solid rgba(0,0,0,0.125) !important;
    padding: 1rem 1.25rem !important;
  }

  .card-body {
    padding: 1.25rem !important;
  }

  .bg-gradient-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
  }

  .bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%) !important;
  }

  .bg-gradient-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  }

  @media (max-width: 768px) {
    .btn-group {
      flex-direction: column !important;
    }

    .btn-group .btn {
      margin: 2px 0 !important;
    }

    .table-responsive {
      font-size: 0.85rem !important;
    }

    .card-body {
      padding: 1rem !important;
    }
  }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchIntervention');
        const filterDomaine = document.getElementById('filterDomaine');
        const filterClient = document.getElementById('filterClient');
        const resetBtn = document.getElementById('resetFilters');
        const table = document.getElementById('interventionsTable');
        const rows = table.querySelectorAll('tbody tr');

        // Fonction de filtrage en temps réel
        function filterTable() {
            const searchTerm = searchInput.value.toLowerCase();
            const selectedDomaine = filterDomaine.value.toLowerCase();
            const clientFilter = filterClient.value.toLowerCase();

            let visibleCount = 0;

            rows.forEach(row => {
                if (row.querySelector('td[colspan]')) return; // Skip "no data" row

                const cells = row.querySelectorAll('td');
                const id = cells[0]?.textContent.toLowerCase() || '';
                const numeroMarche = cells[1]?.textContent.toLowerCase() || '';
                const domaine = cells[2]?.textContent.toLowerCase() || '';
                const client = cells[3]?.textContent.toLowerCase() || '';
                const lieu = cells[4]?.textContent.toLowerCase() || '';
                const periode = cells[5]?.textContent.toLowerCase() || '';
                const dateCreation = cells[6]?.textContent.toLowerCase() || '';

                // Recherche globale
                const matchesSearch = !searchTerm ||
                    id.includes(searchTerm) ||
                    numeroMarche.includes(searchTerm) ||
                    domaine.includes(searchTerm) ||
                    client.includes(searchTerm) ||
                    lieu.includes(searchTerm) ||
                    periode.includes(searchTerm) ||
                    dateCreation.includes(searchTerm);

                // Filtre par domaine
                const matchesDomaine = !selectedDomaine || domaine.includes(selectedDomaine);

                // Filtre par client
                const matchesClient = !clientFilter || client.includes(clientFilter);

                const isVisible = matchesSearch && matchesDomaine && matchesClient;

                row.style.display = isVisible ? '' : 'none';
                if (isVisible) visibleCount++;
            });

            // Mettre à jour le compteur
            updateCounter(visibleCount);
        }

        // Mettre à jour le compteur de résultats
        function updateCounter(count) {
            const badge = document.querySelector('.badge.bg-dark');
            if (badge) {
                badge.textContent = `${count} intervention${count !== 1 ? 's' : ''}`;
            }
        }

        // Événements de filtrage en temps réel
        searchInput.addEventListener('input', filterTable);
        filterDomaine.addEventListener('change', filterTable);
        filterClient.addEventListener('input', filterTable);

        // Reset des filtres
        resetBtn.addEventListener('click', function() {
            searchInput.value = '';
            filterDomaine.value = '';
            filterClient.value = '';
            filterTable();

            // Animation de reset
            [searchInput, filterDomaine, filterClient].forEach(element => {
                element.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 150);
            });
        });

        // Export Excel functionality
        document.getElementById('exportExcel').addEventListener('click', function() {
            exportTableToExcel('interventionsTable', 'interventions');
        });

        // Fonction d'export Excel
        function exportTableToExcel(tableId, filename) {
            const table = document.getElementById(tableId);
            const visibleRows = Array.from(table.querySelectorAll('tbody tr')).filter(row =>
                row.style.display !== 'none' && !row.querySelector('td[colspan]')
            );

            if (visibleRows.length === 0) {
                alert('Aucune donnée à exporter');
                return;
            }

            let csv = 'ID,N° Marché,Domaine,Client,Lieu,Période,Date création\n';

            visibleRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                const rowData = [
                    cells[0]?.textContent.trim() || '',
                    cells[1]?.textContent.trim() || '',
                    cells[2]?.textContent.trim().replace(/\s+/g, ' ') || '',
                    cells[3]?.textContent.trim() || '',
                    cells[4]?.textContent.trim() || '',
                    cells[5]?.textContent.trim() || '',
                    cells[6]?.textContent.trim() || ''
                ];
                csv += rowData.map(field => `"${field}"`).join(',') + '\n';
            });

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Animation d'entrée pour les lignes
        rows.forEach((row, index) => {
            row.style.opacity = '0';
            row.style.transform = 'translateY(20px)';
            setTimeout(() => {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '1';
                row.style.transform = 'translateY(0)';
            }, index * 50);
        });
    });
</script>
{% endblock %}
