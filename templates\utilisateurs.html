{% extends "base.html" %}

{% block title %}Gestion des Utilisateurs - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Gestion des Utilisateurs{% endblock %}

{% block content %}
<div class="container-fluid mt-4 mb-5">
  <!-- Header moderne -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-lg border-0">
        <div class="card-header bg-gradient-primary text-white py-4">
          <div class="row align-items-center">
            <div class="col-md-8">
              <div class="d-flex align-items-center">
                <div class="icon-circle bg-white bg-opacity-20 me-3">
                  <i class="fas fa-users text-white"></i>
                </div>
                <div>
                  <h3 class="mb-0 fw-bold">Gestion des Utilisateurs</h3>
                  <small class="opacity-75">G<PERSON>rez les comptes utilisateurs et leurs permissions</small>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="header-right">
                <div class="btn-group" role="group">
                  <a href="{{ url_for('ajouter_utilisateur') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-user-plus"></i> Ajouter
                  </a>
                  <a href="{{ url_for('rapport_utilisateurs') }}" class="btn btn-light btn-sm" target="_blank">
                    <i class="fas fa-print"></i> Imprimer
                  </a>
                  <button type="button" class="btn btn-success btn-sm" id="exportExcel">
                    <i class="fas fa-file-excel"></i> Excel
                  </button>
                  <button type="button" class="btn btn-info btn-sm" onclick="location.reload()">
                    <i class="fas fa-sync-alt"></i> Actualiser
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistiques des utilisateurs -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card border-0 bg-gradient-primary text-white">
        <div class="card-body text-center">
          <div class="icon-circle bg-white bg-opacity-20 mx-auto mb-3">
            <i class="fas fa-users text-white"></i>
          </div>
          <h4 class="fw-bold">{{ utilisateurs|length }}</h4>
          <p class="mb-0">Total Utilisateurs</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card border-0 bg-gradient-danger text-white">
        <div class="card-body text-center">
          <div class="icon-circle bg-white bg-opacity-20 mx-auto mb-3">
            <i class="fas fa-user-shield text-white"></i>
          </div>
          <h4 class="fw-bold">{{ utilisateurs|selectattr('role', 'equalto', 'admin')|list|length }}</h4>
          <p class="mb-0">Administrateurs</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card border-0 bg-gradient-info text-white">
        <div class="card-body text-center">
          <div class="icon-circle bg-white bg-opacity-20 mx-auto mb-3">
            <i class="fas fa-user text-white"></i>
          </div>
          <h4 class="fw-bold">{{ utilisateurs|selectattr('role', 'equalto', 'user')|list|length }}</h4>
          <p class="mb-0">Utilisateurs</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card border-0 bg-gradient-success text-white">
        <div class="card-body text-center">
          <div class="icon-circle bg-white bg-opacity-20 mx-auto mb-3">
            <i class="fas fa-calendar-check text-white"></i>
          </div>
          <h4 class="fw-bold">Actif</h4>
          <p class="mb-0">Système</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Filtres de recherche -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card border-0 bg-light">
        <div class="card-header bg-gradient-info text-white">
          <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filtres de recherche</h5>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-4">
              <div class="form-floating">
                <input type="text" class="form-control" id="searchName" placeholder="Rechercher par nom">
                <label for="searchName"><i class="fas fa-search me-2"></i>Nom ou Prénom</label>
              </div>
            </div>
            <div class="col-md-4">
              <div class="form-floating">
                <select class="form-select" id="filterRole">
                  <option value="">Tous les rôles</option>
                  <option value="admin">Administrateur</option>
                  <option value="user">Utilisateur</option>
                </select>
                <label for="filterRole"><i class="fas fa-user-tag me-2"></i>Rôle</label>
              </div>
            </div>
            <div class="col-md-4">
              <div class="d-flex gap-2 h-100 align-items-end">
                <button type="button" class="btn btn-primary btn-modern flex-fill" onclick="applyFilters()">
                  <i class="fas fa-search me-1"></i> Rechercher
                </button>
                <button type="button" class="btn btn-secondary btn-modern flex-fill" onclick="clearFilters()">
                  <i class="fas fa-times me-1"></i> Effacer
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Tableau des utilisateurs -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow-lg border-0">
        <div class="card-header bg-gradient-secondary text-white">
          <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <div class="icon-circle bg-white bg-opacity-20 me-3">
                <i class="fas fa-table text-white"></i>
              </div>
              <div>
                <h6 class="mb-0 fw-bold">Liste des Utilisateurs</h6>
                <small class="opacity-75">{{ utilisateurs|length }} utilisateurs enregistrés</small>
              </div>
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0" id="utilisateursTable">
              <thead class="table-dark">
                <tr>
                  <th class="text-center">#</th>
                  <th><i class="fas fa-user me-2"></i>Nom Complet</th>
                  <th><i class="fas fa-envelope me-2"></i>Contact</th>
                  <th><i class="fas fa-user-tag me-2"></i>Rôle</th>
                  <th><i class="fas fa-calendar me-2"></i>Création</th>
                  <th class="text-center"><i class="fas fa-cogs me-2"></i>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% for utilisateur in utilisateurs %}
                <tr class="user-row" data-role="{{ utilisateur.role }}" data-name="{{ utilisateur.nom }} {{ utilisateur.prenom }}">
                  <td class="text-center">
                    <div class="user-avatar">
                      <span class="avatar-text">{{ utilisateur.prenom[0] }}{{ utilisateur.nom[0] }}</span>
                    </div>
                  </td>
                  <td>
                    <div class="user-info">
                      <div class="fw-bold text-primary">{{ utilisateur.prenom }} {{ utilisateur.nom }}</div>
                      <small class="text-muted">@{{ utilisateur.nom_utilisateur }}</small>
                    </div>
                  </td>
                  <td>
                    <div class="contact-info">
                      <div><i class="fas fa-envelope text-info me-1"></i>{{ utilisateur.email }}</div>
                    </div>
                  </td>
                  <td>
                    {% if utilisateur.role == 'admin' %}
                    <span class="badge bg-gradient-danger px-3 py-2">
                      <i class="fas fa-user-shield me-1"></i>Administrateur
                    </span>
                    {% else %}
                    <span class="badge bg-gradient-primary px-3 py-2">
                      <i class="fas fa-user me-1"></i>Utilisateur
                    </span>
                    {% endif %}
                  </td>
                  <td>
                    <div class="date-info">
                      <div class="fw-bold">{{ utilisateur.date_creation.split(' ')[0] if utilisateur.date_creation else 'N/A' }}</div>
                      <small class="text-muted">{{ utilisateur.date_creation.split(' ')[1] if utilisateur.date_creation and ' ' in utilisateur.date_creation else '' }}</small>
                    </div>
                  </td>
                  <td class="text-center">
                    <div class="btn-group" role="group">
                      <a href="{{ url_for('modifier_utilisateur', id=utilisateur.id) }}"
                         class="btn btn-sm btn-warning btn-modern"
                         title="Modifier">
                        <i class="fas fa-edit"></i>
                      </a>
                      {% if utilisateur.id != session.get('utilisateur_id') %}
                      <button type="button"
                              class="btn btn-sm btn-danger btn-modern"
                              onclick="confirmDelete('l\'utilisateur {{ utilisateur.prenom }} {{ utilisateur.nom }}', '{{ url_for('supprimer_utilisateur', id=utilisateur.id) }}')"
                              title="Supprimer">
                        <i class="fas fa-trash"></i>
                      </button>
                      {% else %}
                      <button type="button"
                              class="btn btn-sm btn-secondary btn-modern"
                              disabled
                              title="Vous ne pouvez pas supprimer votre propre compte">
                        <i class="fas fa-lock"></i>
                      </button>
                      {% endif %}
                    </div>
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


{% endblock %}

{% block extra_css %}
<style>
  .card {
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  }

  .table th {
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6 !important;
    font-weight: 600 !important;
    padding: 15px 12px !important;
    font-size: 0.9rem !important;
  }

  .table td {
    padding: 15px 12px !important;
    vertical-align: middle !important;
    border-bottom: 1px solid #dee2e6 !important;
  }

  .table-hover tbody tr:hover {
    background-color: rgba(102,126,234,0.05) !important;
  }

  .btn-group .btn {
    margin: 0 1px !important;
    border-radius: 6px !important;
  }

  .badge {
    font-size: 0.75rem !important;
    padding: 6px 10px !important;
    font-weight: 500 !important;
  }

  .text-white-75 {
    color: rgba(255,255,255,0.75) !important;
  }

  .text-white-50 {
    color: rgba(255,255,255,0.5) !important;
  }

  .card-header {
    border-bottom: 1px solid rgba(0,0,0,0.125) !important;
    padding: 1rem 1.25rem !important;
  }

  .card-body {
    padding: 1.25rem !important;
  }

  .bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  }

  .bg-gradient-danger {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%) !important;
  }

  .bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
  }

  .bg-gradient-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
  }

  .bg-gradient-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
  }

  .text-white {
    color: #ffffff !important;
  }

  .card-header.bg-gradient-primary,
  .card-header.bg-gradient-info,
  .card-header.bg-gradient-secondary {
    color: #ffffff !important;
  }

  .card-header.bg-gradient-primary *,
  .card-header.bg-gradient-info *,
  .card-header.bg-gradient-secondary * {
    color: #ffffff !important;
  }

  /* Avatar utilisateur */
  .user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
  }

  .avatar-text {
    color: white;
    font-weight: bold;
    font-size: 14px;
  }

  /* Responsive */
  @media (max-width: 768px) {
    .btn-group {
      flex-direction: column !important;
    }

    .btn-group .btn {
      margin: 2px 0 !important;
    }

    .table-responsive {
      font-size: 0.85rem !important;
    }

    .card-body {
      padding: 1rem !important;
    }

    .user-avatar {
      width: 30px;
      height: 30px;
    }

    .avatar-text {
      font-size: 12px;
    }
  }
</style>
{% endblock %}

{% block extra_js %}
<script>
  // Fonction confirmDelete est maintenant gérée par delete-modal.js

  // Fonction de filtrage
  function applyFilters() {
    const searchName = document.getElementById('searchName').value.toLowerCase();
    const filterRole = document.getElementById('filterRole').value;
    const rows = document.querySelectorAll('.user-row');

    rows.forEach(row => {
      const name = row.dataset.name.toLowerCase();
      const role = row.dataset.role;

      const nameMatch = !searchName || name.includes(searchName);
      const roleMatch = !filterRole || role === filterRole;

      if (nameMatch && roleMatch) {
        row.style.display = '';
      } else {
        row.style.display = 'none';
      }
    });
  }

  // Fonction pour effacer les filtres
  function clearFilters() {
    document.getElementById('searchName').value = '';
    document.getElementById('filterRole').value = '';
    document.querySelectorAll('.user-row').forEach(row => {
      row.style.display = '';
    });
  }

  // Export Excel
  document.getElementById('exportExcel').addEventListener('click', function() {
    const table = document.getElementById('utilisateursTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "Utilisateurs"});
    XLSX.writeFile(wb, 'utilisateurs.xlsx');
  });

  // Recherche en temps réel
  document.getElementById('searchName').addEventListener('input', applyFilters);
  document.getElementById('filterRole').addEventListener('change', applyFilters);
</script>
{% endblock %}
