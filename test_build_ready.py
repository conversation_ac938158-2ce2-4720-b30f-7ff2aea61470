#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار جاهزية البناء للملف التنفيذي
"""

import os
import sys
import platform

def test_build_readiness():
    """اختبار جاهزية البناء"""
    
    print("🔍 اختبار جاهزية بناء الملف التنفيذي")
    print("=" * 60)
    
    # معلومات النظام
    print(f"💻 نظام التشغيل: {platform.system()} {platform.release()}")
    print(f"🏗️ المعمارية: {platform.machine()} ({platform.architecture()[0]})")
    print(f"🐍 Python: {sys.version}")
    print()
    
    # فحص الملفات الأساسية
    essential_files = {
        'app.py': 'ملف التطبيق الرئيسي',
        'launcher.py': 'ملف التشغيل',
        'maintenance_app.spec': 'ملف مواصفات PyInstaller',
        'build_exe.py': 'سكريپت البناء',
        'requirements.txt': 'ملف المتطلبات',
        'templates/': 'مجلد القوالب',
        'static/': 'مجلد الملفات الثابتة'
    }
    
    print("📁 فحص الملفات الأساسية:")
    missing_files = []
    
    for file_path, description in essential_files.items():
        if os.path.exists(file_path):
            print(f"   ✅ {file_path} - {description}")
        else:
            print(f"   ❌ {file_path} - {description} (غير موجود)")
            missing_files.append(file_path)
    
    # فحص المكتبات المطلوبة
    print(f"\n📦 فحص المكتبات المطلوبة:")
    required_packages = [
        'flask',
        'werkzeug',
        'jinja2',
        'pyinstaller'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} (غير مثبت)")
            missing_packages.append(package)
    
    # فحص قاعدة البيانات
    print(f"\n🗄️ فحص قاعدة البيانات:")
    if os.path.exists('maintenance.db'):
        print(f"   ✅ maintenance.db موجودة")
        db_size = os.path.getsize('maintenance.db')
        print(f"   📊 حجم قاعدة البيانات: {db_size:,} بايت")
    else:
        print(f"   ⚠️ maintenance.db غير موجودة (سيتم إنشاؤها تلقائياً)")
    
    # فحص مجلد الرفع
    print(f"\n📤 فحص مجلد الرفع:")
    upload_dirs = ['static/uploads', 'uploads']
    upload_found = False
    
    for upload_dir in upload_dirs:
        if os.path.exists(upload_dir):
            print(f"   ✅ {upload_dir} موجود")
            upload_found = True
            break
    
    if not upload_found:
        print(f"   ⚠️ مجلد الرفع غير موجود (سيتم إنشاؤه تلقائياً)")
    
    # فحص القوالب المهمة
    print(f"\n📄 فحص القوالب المهمة:")
    important_templates = [
        'templates/base.html',
        'templates/dashboard.html',
        'templates/login.html',
        'templates/marches.html',
        'templates/interventions.html',
        'templates/reclamations.html',
        'templates/interventions/svs.html',
        'templates/voir_reclamation.html'
    ]
    
    missing_templates = []
    
    for template in important_templates:
        if os.path.exists(template):
            print(f"   ✅ {template}")
        else:
            print(f"   ❌ {template} (غير موجود)")
            missing_templates.append(template)
    
    # فحص الأزرار المصغرة
    print(f"\n🔘 فحص الأزرار المصغرة:")
    
    # فحص قالب SVS
    if os.path.exists('templates/interventions/svs.html'):
        with open('templates/interventions/svs.html', 'r', encoding='utf-8') as f:
            svs_content = f.read()
        
        if 'btn-xs' in svs_content and 'font-size: 10px' in svs_content:
            print(f"   ✅ أزرار SVS مصغرة")
        else:
            print(f"   ❌ أزرار SVS غير مصغرة")
    
    # فحص قالب الريكلاماسيون
    if os.path.exists('templates/voir_reclamation.html'):
        with open('templates/voir_reclamation.html', 'r', encoding='utf-8') as f:
            reclamation_content = f.read()
        
        if 'btn-xs' in reclamation_content and 'font-size: 10px' in reclamation_content:
            print(f"   ✅ أزرار الريكلاماسيون مصغرة")
        else:
            print(f"   ❌ أزرار الريكلاماسيون غير مصغرة")
    
    # فحص دعم PyInstaller في app.py
    print(f"\n🔧 فحص دعم PyInstaller:")
    if os.path.exists('app.py'):
        with open('app.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        if 'sys.frozen' in app_content and '_MEIPASS' in app_content:
            print(f"   ✅ دعم PyInstaller مضاف إلى app.py")
        else:
            print(f"   ❌ دعم PyInstaller غير مضاف إلى app.py")
    
    # النتيجة النهائية
    print(f"\n📊 ملخص الجاهزية:")
    print("=" * 60)
    
    total_issues = len(missing_files) + len(missing_packages) + len(missing_templates)
    
    if total_issues == 0:
        print(f"✅ النظام جاهز للبناء!")
        print(f"🚀 يمكنك تشغيل build_exe.py أو build_exe.bat")
        print(f"💻 سيعمل على Windows 7, 10, 11 (32/64 bit)")
        print(f"👥 يدعم عدة مستخدمين في نفس الوقت")
        print(f"🔘 الأزرار مصغرة في الجداول")
        return True
    else:
        print(f"⚠️ يوجد {total_issues} مشكلة تحتاج إصلاح:")
        
        if missing_files:
            print(f"   📁 ملفات مفقودة: {', '.join(missing_files)}")
        
        if missing_packages:
            print(f"   📦 مكتبات مفقودة: {', '.join(missing_packages)}")
            print(f"   💡 شغل: pip install -r requirements.txt")
        
        if missing_templates:
            print(f"   📄 قوالب مفقودة: {', '.join(missing_templates)}")
        
        return False

def show_build_instructions():
    """عرض تعليمات البناء"""
    
    print(f"\n📋 تعليمات البناء:")
    print("=" * 60)
    
    print(f"🔧 الطريقة السريعة:")
    print(f"   1. شغل: install_requirements.bat")
    print(f"   2. شغل: build_exe.bat")
    
    print(f"\n🔧 الطريقة اليدوية:")
    print(f"   1. pip install -r requirements.txt")
    print(f"   2. python build_exe.py")
    
    print(f"\n📁 بعد البناء:")
    print(f"   • الملفات ستكون في مجلد 'dist'")
    print(f"   • انسخ المجلد كاملاً إلى أي مكان")
    print(f"   • شغل Maintenance_Management_System.exe")
    
    print(f"\n🌐 للاستخدام متعدد المستخدمين:")
    print(f"   • شغل النظام على جهاز واحد (الخادم)")
    print(f"   • شارك عنوان IP مع المستخدمين الآخرين")
    print(f"   • كل مستخدم يفتح: http://[IP]:[PORT]")

if __name__ == "__main__":
    print("🎯 اختبار جاهزية بناء نظام إدارة الصيانة")
    print("🎯 Testing Maintenance System Build Readiness")
    print()
    
    try:
        is_ready = test_build_readiness()
        show_build_instructions()
        
        if is_ready:
            print(f"\n🎉 النظام جاهز للبناء!")
            print(f"🎉 System is ready to build!")
        else:
            print(f"\n⚠️ يرجى إصلاح المشاكل أولاً")
            print(f"⚠️ Please fix the issues first")
        
        input(f"\nاضغط Enter للخروج...")
        
    except KeyboardInterrupt:
        print(f"\n⏹️ تم إلغاء الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        input("اضغط Enter للخروج...")
