{% extends "rapport_base.html" %}

{% block title %}Rapport des Sites - Système de Gestion de Maintenance{% endblock %}

{% block rapport_title %}Rapport des Sites{% endblock %}

{% block rapport_subtitle %}Liste complète des sites de maintenance{% endblock %}

{% block rapport_meta %}
<div class="rapport-meta">
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-building me-2"></i>Total des Sites:</span>
        <span class="meta-value">{{ sites|length }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-map-marker-alt me-2"></i>Régions Couvertes:</span>
        <span class="meta-value">{{ sites|map(attribute='region_nom')|unique|list|length }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-map-marked-alt me-2"></i>Sites avec GPS:</span>
        <span class="meta-value">{{ sites|selectattr('gps')|list|length }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-user-tie me-2"></i>Sites avec Responsable:</span>
        <span class="meta-value">{{ sites|selectattr('responsable')|list|length }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-calendar me-2"></i>Date de Génération:</span>
        <span class="meta-value">{{ moment().format('DD/MM/YYYY HH:mm') if moment else date_now }}</span>
    </div>
</div>
{% endblock %}

{% block rapport_content %}
<!-- Répartition par région -->
<div class="section-title">
    <i class="fas fa-chart-pie me-2"></i>Répartition par Région
</div>

<div class="row mb-4">
    {% set regions_stats = {} %}
    {% for site in sites %}
        {% if regions_stats.update({site.region_nom: regions_stats.get(site.region_nom, 0) + 1}) %}{% endif %}
    {% endfor %}
    
    {% for region, count in regions_stats.items() %}
    <div class="col-md-3 mb-3">
        <div class="card border-0 bg-light">
            <div class="card-body text-center">
                <h5 class="text-primary">{{ count }}</h5>
                <p class="mb-0 text-muted">{{ region }}</p>
            </div>
        </div>
    </div>
    {% endfor %}
</div>

<!-- Liste détaillée des sites -->
<div class="section-title">
    <i class="fas fa-list me-2"></i>Liste Détaillée des Sites
</div>

<div class="table-responsive">
    <table class="table table-custom">
        <thead>
            <tr>
                <th style="width: 5%;">#</th>
                <th style="width: 15%;">Région</th>
                <th style="width: 20%;">Nom du Site</th>
                <th style="width: 25%;">Adresse</th>
                <th style="width: 15%;">Coordonnées GPS</th>
                <th style="width: 20%;">Contact</th>
            </tr>
        </thead>
        <tbody>
            {% for site in sites %}
            <tr>
                <td class="text-center">{{ loop.index }}</td>
                <td>
                    <span class="badge-custom badge-info">{{ site.region_nom }}</span>
                </td>
                <td class="fw-bold">{{ site.nom }}</td>
                <td>{{ site.adresse or 'Non spécifiée' }}</td>
                <td>
                    {% if site.gps %}
                        <span class="text-success">
                            <i class="fas fa-map-marked-alt me-1"></i>{{ site.gps }}
                        </span>
                    {% else %}
                        <span class="text-muted">Non défini</span>
                    {% endif %}
                </td>
                <td>
                    {% if site.responsable %}
                        <div class="fw-bold">{{ site.responsable }}</div>
                    {% endif %}
                    {% if site.telephone %}
                        <div><i class="fas fa-phone me-1"></i>{{ site.telephone }}</div>
                    {% endif %}
                    {% if site.email %}
                        <div><i class="fas fa-envelope me-1"></i>{{ site.email }}</div>
                    {% endif %}
                    {% if not site.responsable and not site.telephone and not site.email %}
                        <span class="text-muted">Non défini</span>
                    {% endif %}
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>

<!-- Statistiques détaillées -->
<div class="section-title">
    <i class="fas fa-chart-bar me-2"></i>Statistiques Détaillées
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card border-0 bg-light">
            <div class="card-body">
                <h6 class="card-title text-primary">
                    <i class="fas fa-info-circle me-2"></i>Informations Générales
                </h6>
                <ul class="list-unstyled mb-0">
                    <li><strong>Total des sites:</strong> {{ sites|length }}</li>
                    <li><strong>Régions couvertes:</strong> {{ sites|map(attribute='region_nom')|unique|list|length }}</li>
                    <li><strong>Sites avec GPS:</strong> {{ sites|selectattr('gps')|list|length }} ({{ "%.1f"|format((sites|selectattr('gps')|list|length / sites|length * 100) if sites|length > 0 else 0) }}%)</li>
                    <li><strong>Sites avec responsable:</strong> {{ sites|selectattr('responsable')|list|length }} ({{ "%.1f"|format((sites|selectattr('responsable')|list|length / sites|length * 100) if sites|length > 0 else 0) }}%)</li>
                    <li><strong>Sites avec téléphone:</strong> {{ sites|selectattr('telephone')|list|length }} ({{ "%.1f"|format((sites|selectattr('telephone')|list|length / sites|length * 100) if sites|length > 0 else 0) }}%)</li>
                    <li><strong>Sites avec email:</strong> {{ sites|selectattr('email')|list|length }} ({{ "%.1f"|format((sites|selectattr('email')|list|length / sites|length * 100) if sites|length > 0 else 0) }}%)</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card border-0 bg-light">
            <div class="card-body">
                <h6 class="card-title text-success">
                    <i class="fas fa-check-circle me-2"></i>Qualité des Données
                </h6>
                {% set completeness = (sites|selectattr('gps')|list|length + sites|selectattr('responsable')|list|length + sites|selectattr('telephone')|list|length + sites|selectattr('email')|list|length) / (sites|length * 4) * 100 if sites|length > 0 else 0 %}
                <div class="mb-3">
                    <div class="d-flex justify-content-between">
                        <span>Complétude des données</span>
                        <span>{{ "%.1f"|format(completeness) }}%</span>
                    </div>
                    <div class="progress" style="height: 10px;">
                        <div class="progress-bar bg-success" style="width: {{ completeness }}%"></div>
                    </div>
                </div>
                <ul class="list-unstyled mb-0">
                    <li>• Toutes les informations de contact sont importantes</li>
                    <li>• Les coordonnées GPS facilitent la localisation</li>
                    <li>• Un responsable désigné améliore la communication</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Sites par région (détaillé) -->
<div class="section-title">
    <i class="fas fa-map me-2"></i>Répartition Détaillée par Région
</div>

{% for region_nom in sites|map(attribute='region_nom')|unique %}
<div class="mb-4">
    <h6 class="text-primary border-bottom pb-2">
        <i class="fas fa-map-marker-alt me-2"></i>{{ region_nom }}
        <span class="badge bg-primary ms-2">{{ sites|selectattr('region_nom', 'equalto', region_nom)|list|length }} sites</span>
    </h6>
    
    <div class="table-responsive">
        <table class="table table-sm">
            <thead class="table-light">
                <tr>
                    <th>Site</th>
                    <th>Adresse</th>
                    <th>Contact</th>
                    <th>GPS</th>
                </tr>
            </thead>
            <tbody>
                {% for site in sites|selectattr('region_nom', 'equalto', region_nom) %}
                <tr>
                    <td class="fw-bold">{{ site.nom }}</td>
                    <td>{{ site.adresse or 'Non spécifiée' }}</td>
                    <td>
                        {% if site.responsable %}{{ site.responsable }}{% endif %}
                        {% if site.telephone %}<br><i class="fas fa-phone me-1"></i>{{ site.telephone }}{% endif %}
                    </td>
                    <td>
                        {% if site.gps %}
                            <span class="text-success"><i class="fas fa-check me-1"></i>Oui</span>
                        {% else %}
                            <span class="text-muted"><i class="fas fa-times me-1"></i>Non</span>
                        {% endif %}
                    </td>
                </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endfor %}

<!-- Recommandations -->
<div class="mt-4 p-3 bg-light rounded">
    <h6 class="text-muted mb-2">
        <i class="fas fa-lightbulb me-2"></i>Recommandations
    </h6>
    <ul class="mb-0 text-muted">
        <li>Compléter les coordonnées GPS pour tous les sites pour améliorer la localisation</li>
        <li>Désigner un responsable pour chaque site afin d'optimiser la communication</li>
        <li>Mettre à jour régulièrement les informations de contact</li>
        <li>Organiser les sites par zones géographiques pour optimiser les tournées</li>
        <li>Créer un plan de maintenance préventive basé sur la répartition géographique</li>
    </ul>
</div>
{% endblock %}
