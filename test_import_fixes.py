#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔧 اختبار إصلاحات الاستيراد
Test Import Fixes

هذا الملف يختبر الإصلاحات التي تم تطبيقها على:
1. رسائل الاستيراد باللغة الفرنسية
2. إصلاح مشكلة الشاشة المتعتمة
3. تحسين دوال modal
"""

import os
import re

def test_javascript_fixes():
    """اختبار إصلاحات JavaScript"""

    print("\n🔧 اختبار إصلاحات JavaScript...")
    print("=" * 50)

    if not os.path.exists('static/js/import-excel.js'):
        print("❌ ملف import-excel.js غير موجود")
        return False

    try:
        with open('static/js/import-excel.js', 'r', encoding='utf-8') as f:
            content = f.read()

        # اختبار الرسائل الفرنسية
        french_messages = [
            "Module non supporté",
            "Le fichier est vide ou ne contient pas de données valides",
            "Erreur lors de la lecture du fichier Excel",
            "Aperçu de l'importation Excel",
            "Fichier:",
            "Nombre d'enregistrements:",
            "Annuler",
            "Importer les données",
            "Importation des données en cours...",
            "Erreur lors de l",
            "Erreur de connexion au serveur",
            "Chargement...",
            "Veuillez patienter..."
        ]

        all_french_found = True
        for message in french_messages:
            if message in content:
                print(f"   ✅ Message français trouvé: {message}")
            else:
                print(f"   ❌ Message français manquant: {message}")
                all_french_found = False

        # اختبار إصلاحات الشاشة المتعتمة
        backdrop_fixes = [
            "const backdrops = document.querySelectorAll('.modal-backdrop');",
            "backdrops.forEach(backdrop => backdrop.remove());",
            "document.body.classList.remove('modal-open');",
            "document.body.style.overflow = '';",
            "document.body.style.paddingRight = '';"
        ]

        backdrop_fixes_found = True
        for fix in backdrop_fixes:
            if fix in content:
                print(f"   ✅ إصلاح الشاشة المتعتمة: {fix[:50]}...")
            else:
                print(f"   ❌ إصلاح مفقود: {fix[:50]}...")
                backdrop_fixes_found = False

        # اختبار أسماء النماذج الفرنسية
        module_names = {
            "'marches': 'Marchés'": "الأسواق",
            "'interventions': 'Interventions'": "التدخلات",
            "'reclamations': 'Réclamations'": "الشكاوى",
            "'regions': 'Régions'": "المناطق",
            "'sites': 'Sites'": "المواقع"
        }

        module_names_found = True
        for french_name, arabic_desc in module_names.items():
            if french_name in content:
                print(f"   ✅ اسم النموذج الفرنسي: {arabic_desc}")
            else:
                print(f"   ❌ اسم النموذج مفقود: {arabic_desc}")
                module_names_found = False

        return all_french_found and backdrop_fixes_found and module_names_found

    except Exception as e:
        print(f"❌ خطأ في اختبار JavaScript: {e}")
        return False

def test_python_fixes():
    """اختبار إصلاحات Python"""

    print("\n🐍 اختبار إصلاحات Python...")
    print("=" * 50)

    if not os.path.exists('app.py'):
        print("❌ ملف app.py غير موجود")
        return False

    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # اختبار رسائل الخطأ الفرنسية
        french_error_messages = [
            "Aucun fichier fourni",
            "Aucun fichier sélectionné",
            "Le fichier doit être au format Excel",
            "enregistrements importés avec succès",
            "erreurs."
        ]

        all_errors_found = True
        for message in french_error_messages:
            if message in content:
                print(f"   ✅ رسالة خطأ فرنسية: {message}")
            else:
                print(f"   ❌ رسالة خطأ مفقودة: {message}")
                all_errors_found = False

        # اختبار وجود routes الاستيراد
        import_routes = [
            'api_import_marches',
            'api_import_interventions',
            'api_import_reclamations',
            'api_import_regions',
            'api_import_sites'
        ]

        all_routes_found = True
        for route in import_routes:
            if f"def {route}(" in content:
                print(f"   ✅ Route الاستيراد: {route}")
            else:
                print(f"   ❌ Route مفقود: {route}")
                all_routes_found = False

        return all_errors_found and all_routes_found

    except Exception as e:
        print(f"❌ خطأ في اختبار Python: {e}")
        return False

def test_arabic_removal():
    """اختبار إزالة النصوص العربية"""

    print("\n🔍 اختبار إزالة النصوص العربية...")
    print("=" * 50)

    files_to_check = [
        'static/js/import-excel.js'
    ]

    arabic_patterns = [
        r'نموذج غير مدعوم',
        r'الملف فارغ',
        r'خطأ في قراءة الملف',
        r'معاينة الاستيراد',
        r'الملف:',
        r'عدد السجلات:',
        r'إلغاء',
        r'استيراد البيانات',
        r'جاري استيراد',
        r'خطأ في الاستيراد',
        r'خطأ في الاتصال',
        r'جاري التحميل',
        r'يرجى الانتظار'
    ]

    all_clean = True

    for file_path in files_to_check:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()

                print(f"\n   📁 فحص الملف: {file_path}")

                for pattern in arabic_patterns:
                    if re.search(pattern, content):
                        print(f"   ❌ نص عربي موجود: {pattern}")
                        all_clean = False
                    else:
                        print(f"   ✅ نص عربي محذوف: {pattern}")

            except Exception as e:
                print(f"   ❌ خطأ في فحص {file_path}: {e}")
                all_clean = False
        else:
            print(f"   ❌ الملف غير موجود: {file_path}")
            all_clean = False

    return all_clean

def main():
    """الدالة الرئيسية"""

    print("🎯 اختبار إصلاحات الاستيراد")
    print("=" * 60)
    print("📋 الهدف: التأكد من إصلاح مشاكل الاستيراد")
    print("🔧 المشاكل المحلولة:")
    print("   1. رسائل الموافقة العربية → فرنسية")
    print("   2. مشكلة الشاشة المتعتمة")
    print("   3. تحسين دوال modal")
    print("=" * 60)

    # تشغيل الاختبارات
    js_test = test_javascript_fixes()
    py_test = test_python_fixes()
    arabic_test = test_arabic_removal()

    # النتائج النهائية
    print("\n" + "=" * 60)
    print("📊 النتائج النهائية:")
    print("=" * 60)

    if js_test:
        print("✅ إصلاحات JavaScript: نجحت")
    else:
        print("❌ إصلاحات JavaScript: فشلت")

    if py_test:
        print("✅ إصلاحات Python: نجحت")
    else:
        print("❌ إصلاحات Python: فشلت")

    if arabic_test:
        print("✅ إزالة النصوص العربية: نجحت")
    else:
        print("❌ إزالة النصوص العربية: فشلت")

    if js_test and py_test and arabic_test:
        print("\n🎉 جميع الإصلاحات تمت بنجاح!")
        print("💡 يمكنك الآن اختبار الاستيراد في البرنامج")
        return True
    else:
        print("\n⚠️  بعض الإصلاحات تحتاج مراجعة")
        return False

if __name__ == "__main__":
    main()
