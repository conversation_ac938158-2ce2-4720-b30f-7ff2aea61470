// Attendre que le DOM soit chargé
document.addEventListener('DOMContentLoaded', function() {
    // Ajouter la classe login-page au body si on est sur la page de connexion
    if (window.location.pathname === '/login') {
        document.body.classList.add('login-page');
    }

    // Animation des alertes
    const alerts = document.querySelectorAll('.alert:not(.alert-dismissible)');
    alerts.forEach(alert => {
        // Ajouter un bouton de fermeture
        const closeBtn = document.createElement('span');
        closeBtn.innerHTML = '&times;';
        closeBtn.style.float = 'right';
        closeBtn.style.cursor = 'pointer';
        closeBtn.style.marginLeft = '15px';
        closeBtn.onclick = function() {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.style.display = 'none';
            }, 300);
        };
        alert.prepend(closeBtn);

        // Faire disparaître l'alerte après 5 secondes
        setTimeout(() => {
            alert.style.opacity = '0';
            setTimeout(() => {
                alert.style.display = 'none';
            }, 300);
        }, 5000);
    });

    // Animation des champs de formulaire sur la page de connexion
    const inputs = document.querySelectorAll('.input-group input');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.style.transform = 'scale(1.02)';
            this.parentElement.style.transition = 'transform 0.3s';
        });

        input.addEventListener('blur', function() {
            this.parentElement.style.transform = 'scale(1)';
        });
    });

    // Animation du bouton de connexion
    const loginBtn = document.querySelector('.login-btn');
    if (loginBtn) {
        loginBtn.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
        });

        loginBtn.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    }

    // Toggle sidebar sur mobile
    const sidebarCollapse = document.getElementById('sidebarCollapse');
    if (sidebarCollapse) {
        sidebarCollapse.addEventListener('click', function() {
            document.getElementById('sidebar').classList.toggle('active');
            document.querySelector('.content').classList.toggle('active');
        });
    }

    // Assurer que les sous-menus sont correctement affichés
    const dropdownToggles = document.querySelectorAll('.dropdown-toggle');
    dropdownToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const submenu = this.nextElementSibling;
            if (submenu) {
                submenu.classList.toggle('show');
                // Ajouter une classe active au parent
                this.parentElement.classList.toggle('active');
            }
        });
    });

    // Assurer que le sous-menu actif est visible
    const activeSubmenuItems = document.querySelectorAll('.sidebar ul li ul li.active');
    activeSubmenuItems.forEach(item => {
        const parentCollapse = item.closest('.collapse');
        if (parentCollapse) {
            parentCollapse.classList.add('show');
            const parentLi = parentCollapse.closest('li');
            if (parentLi) {
                parentLi.classList.add('active');
            }
        }
    });

    // Améliorer la gestion des erreurs JavaScript
    window.addEventListener('error', function(e) {
        console.error('Erreur JavaScript détectée:', e.error);
        // Ne pas afficher d'alerte pour éviter de perturber l'utilisateur
    });

    // Améliorer la gestion des promesses rejetées
    window.addEventListener('unhandledrejection', function(e) {
        console.error('Promesse rejetée non gérée:', e.reason);
        e.preventDefault(); // Empêcher l'affichage dans la console
    });

    // Fonctionnalités pour les tableaux
    const tables = document.querySelectorAll('.table');
    tables.forEach(table => {
        // Ajouter la classe table-hover
        table.classList.add('table-hover');

        // Ajouter des fonctionnalités de tri si nécessaire
        const headers = table.querySelectorAll('th');
        headers.forEach(header => {
            if (header.getAttribute('data-sortable') === 'true') {
                header.style.cursor = 'pointer';
                header.addEventListener('click', function() {
                    sortTable(table, Array.from(headers).indexOf(header));
                });
            }
        });
    });

    // Fonctionnalité d'impression améliorée
    const printButtons = document.querySelectorAll('.btn-print, [onclick="window.print()"], button[onclick*="window.print"]');
    printButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();

            // Vérifier si printPage existe, sinon utiliser window.print()
            if (typeof printPage === 'function') {
                printPage();
            } else {
                // Fonction d'impression simple et efficace
                window.print();
            }
        });
    });

    // Ajouter une fonction d'impression globale si elle n'existe pas
    if (typeof window.printPage !== 'function') {
        window.printPage = function() {
            // Préparer la page pour l'impression
            document.body.classList.add('printing');

            // Masquer temporairement les éléments non imprimables
            const elementsToHide = document.querySelectorAll('.sidebar, .navbar, .btn-group, .no-print');
            elementsToHide.forEach(el => el.style.display = 'none');

            // Lancer l'impression
            window.print();

            // Restaurer l'affichage après impression
            setTimeout(() => {
                elementsToHide.forEach(el => el.style.display = '');
                document.body.classList.remove('printing');
            }, 1000);
        };
    }

    // Fonctionnalité de confirmation pour les suppressions (désactivée - utilise delete-modal.js)
    // Cette section est désactivée pour éviter les conflits avec delete-modal.js
    console.log('Delete confirmation handled by delete-modal.js');

    // Améliorer les tooltips
    initializeTooltips();

    // Ajouter des animations aux cartes
    animateCards();

    // Initialiser les fonctionnalités de recherche
    initializeSearch();
});
});

// Fonctions utilitaires améliorées

// Fonction pour afficher une boîte de dialogue de confirmation
function showConfirmDialog(title, message, onConfirm, onCancel = null) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">${title}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <p>${message}</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-danger" id="confirmBtn">Confirmer</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    modal.querySelector('#confirmBtn').addEventListener('click', () => {
        bootstrapModal.hide();
        if (onConfirm) onConfirm();
    });

    modal.addEventListener('hidden.bs.modal', () => {
        modal.remove();
        if (onCancel) onCancel();
    });
}

// Fonction pour initialiser les tooltips
function initializeTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"], [title]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
}

// Fonction pour animer les cartes
function animateCards() {
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Fonction pour initialiser la recherche
function initializeSearch() {
    const searchInputs = document.querySelectorAll('input[type="search"], input[placeholder*="recherche"], input[placeholder*="Recherche"]');

    searchInputs.forEach(input => {
        input.addEventListener('input', function() {
            const searchTerm = this.value.toLowerCase();
            const table = this.closest('.card').querySelector('table tbody');

            if (table) {
                const rows = table.querySelectorAll('tr');
                rows.forEach(row => {
                    const text = row.textContent.toLowerCase();
                    row.style.display = text.includes(searchTerm) ? '' : 'none';
                });
            }
        });
    });
}

// Fonction pour trier les tableaux
function sortTable(table, columnIndex) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const headers = table.querySelectorAll('th');
    const header = headers[columnIndex];

    // Déterminer l'ordre de tri (ascendant ou descendant)
    const currentOrder = header.getAttribute('data-order') || 'asc';
    const newOrder = currentOrder === 'asc' ? 'desc' : 'asc';

    // Mettre à jour l'attribut data-order
    headers.forEach(h => h.removeAttribute('data-order'));
    header.setAttribute('data-order', newOrder);

    // Trier les lignes
    rows.sort((a, b) => {
        const cellA = a.querySelectorAll('td')[columnIndex].textContent.trim();
        const cellB = b.querySelectorAll('td')[columnIndex].textContent.trim();

        // Vérifier si les valeurs sont des nombres
        const numA = parseFloat(cellA);
        const numB = parseFloat(cellB);

        if (!isNaN(numA) && !isNaN(numB)) {
            return newOrder === 'asc' ? numA - numB : numB - numA;
        }

        // Sinon, trier comme des chaînes de caractères
        return newOrder === 'asc'
            ? cellA.localeCompare(cellB, 'fr', { sensitivity: 'base' })
            : cellB.localeCompare(cellA, 'fr', { sensitivity: 'base' });
    });

    // Réorganiser les lignes dans le tableau
    rows.forEach(row => tbody.appendChild(row));

    // Ajouter des indicateurs visuels de tri
    headers.forEach(h => {
        h.classList.remove('sorted-asc', 'sorted-desc');
    });

    header.classList.add(newOrder === 'asc' ? 'sorted-asc' : 'sorted-desc');
}

// Fonction pour filtrer les tableaux
function filterTable(tableId, inputId) {
    const input = document.getElementById(inputId);
    const filter = input.value.toUpperCase();
    const table = document.getElementById(tableId);
    const rows = table.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const cells = row.querySelectorAll('td');
        let found = false;

        cells.forEach(cell => {
            if (cell.textContent.toUpperCase().indexOf(filter) > -1) {
                found = true;
            }
        });

        row.style.display = found ? '' : 'none';
    });
}