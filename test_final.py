#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نهائي للتأكد من أن تحميل الشعار يعمل بشكل صحيح
"""

import sqlite3
import os
import requests
import time

def test_logo_upload():
    """اختبار شامل لوظيفة تحميل الشعار"""
    print("🧪 اختبار نهائي لتحميل الشعار")
    print("=" * 50)
    
    # 1. فحص قاعدة البيانات
    print("1️⃣ فحص قاعدة البيانات...")
    try:
        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()
        
        cursor.execute("SELECT id, nom, logo FROM societe LIMIT 1")
        societe = cursor.fetchone()
        
        if societe:
            print(f"   ✅ بيانات الشركة موجودة: {societe[1]}")
            print(f"   📷 الشعار الحالي: {societe[2] if societe[2] else 'لا يوجد'}")
        else:
            print("   ❌ لا توجد بيانات شركة")
            
        conn.close()
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {e}")
    
    # 2. فحص المجلدات
    print("\n2️⃣ فحص المجلدات...")
    folders = ['static', 'static/uploads', 'static/images']
    for folder in folders:
        if os.path.exists(folder):
            print(f"   ✅ {folder} موجود")
        else:
            print(f"   ❌ {folder} غير موجود")
            os.makedirs(folder, exist_ok=True)
            print(f"   🔧 تم إنشاء {folder}")
    
    # 3. فحص الخادم
    print("\n3️⃣ فحص الخادم...")
    try:
        response = requests.get('http://127.0.0.1:5000', timeout=5)
        if response.status_code == 200:
            print("   ✅ الخادم يعمل بشكل صحيح")
        else:
            print(f"   ⚠️ الخادم يعمل لكن مع رمز حالة: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("   ❌ الخادم غير متاح - تأكد من تشغيل python app.py")
    except Exception as e:
        print(f"   ❌ خطأ في الاتصال بالخادم: {e}")
    
    # 4. فحص صفحة الشركة
    print("\n4️⃣ فحص صفحة معلومات الشركة...")
    try:
        # محاولة الوصول لصفحة الشركة (قد تحتاج تسجيل دخول)
        response = requests.get('http://127.0.0.1:5000/societe', timeout=5)
        if response.status_code == 302:
            print("   ✅ صفحة الشركة تتطلب تسجيل دخول (طبيعي)")
        elif response.status_code == 200:
            print("   ✅ صفحة الشركة متاحة")
        else:
            print(f"   ⚠️ صفحة الشركة ترجع رمز: {response.status_code}")
    except Exception as e:
        print(f"   ❌ خطأ في الوصول لصفحة الشركة: {e}")
    
    # 5. فحص الملفات المحدثة
    print("\n5️⃣ فحص الملفات المحدثة...")
    files_to_check = [
        'templates/societe.html',
        'app.py',
        'reset_logo.py',
        'README_LOGO_UPLOAD.md'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"   ✅ {file_path} ({size} bytes)")
        else:
            print(f"   ❌ {file_path} غير موجود")
    
    # 6. نصائح للاختبار
    print("\n6️⃣ نصائح للاختبار:")
    print("   🌐 افتح المتصفح على: http://127.0.0.1:5000")
    print("   👤 سجل دخول بـ: admin / admin123")
    print("   🏢 انتقل إلى: Informations Société")
    print("   📷 جرب تحميل صورة شعار")
    print("   💾 لا تنس النقر على 'Enregistrer les modifications'")
    
    print("\n🎯 الميزات الجديدة:")
    print("   📏 منطقة شعار أكبر (250x200 بكسل)")
    print("   🖱️ يمكن النقر على كامل منطقة الشعار")
    print("   🎨 تصميم محسن مع حدود منقطة")
    print("   ✨ تأثيرات بصرية محسنة")
    print("   🗑️ تم حذف الشعار الوهمي")
    
    print("\n✅ انتهى الاختبار!")

if __name__ == "__main__":
    test_logo_upload()
