<!DOCTYPE html>
<html>
<head>
    <title>اختبار زر الحذف</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">اختبار زر الحذف الكلي</h1>
                
                <!-- زر الحذف الكلي الكبير -->
                <div class="text-center mb-4">
                    <button type="button" class="btn btn-danger btn-lg px-5 py-3" onclick="showBulkDeleteModal()" style="font-size: 18px; font-weight: bold;">
                        <i class="fas fa-trash-alt me-2"></i>حذف كلي لسجلات الأنشطة
                    </button>
                </div>
                
                <!-- زر صغير -->
                <div class="text-center">
                    <button type="button" class="btn btn-danger btn-sm" onclick="showBulkDeleteModal()">
                        <i class="fas fa-trash-alt me-1"></i>حذف كلي
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal للحذف الكلي -->
    <div class="modal fade" id="bulkDeleteModal" tabindex="-1" aria-labelledby="bulkDeleteModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="bulkDeleteModalLabel">
                        <i class="fas fa-trash-alt me-2"></i>حذف كلي لسجلات الأنشطة
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تحذير:</strong> هذا الإجراء سيحذف السجلات نهائياً ولا يمكن التراجع عنه!
                    </div>
                    
                    <form id="bulkDeleteForm" method="POST" action="/logs/delete-by-date">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="dateType" class="form-label fw-bold">نوع الحذف:</label>
                                <select class="form-select" id="dateType" name="date_type" onchange="changeDateType()" required>
                                    <option value="">اختر نوع الحذف...</option>
                                    <option value="all">حذف جميع السجلات</option>
                                    <option value="before">حذف السجلات قبل تاريخ معين</option>
                                    <option value="after">حذف السجلات بعد تاريخ معين</option>
                                    <option value="specific">حذف السجلات في تاريخ محدد</option>
                                    <option value="between">حذف السجلات بين تاريخين</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- حقل التاريخ الواحد -->
                        <div id="singleDateDiv" style="display: none;">
                            <div class="row">
                                <div class="col-md-12 mb-3">
                                    <label for="dateValue" class="form-label fw-bold">التاريخ:</label>
                                    <input type="date" class="form-control" id="dateValue" name="date_value">
                                </div>
                            </div>
                        </div>
                        
                        <!-- حقول نطاق التاريخ -->
                        <div id="dateRangeDiv" style="display: none;">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="dateStart" class="form-label fw-bold">من تاريخ:</label>
                                    <input type="date" class="form-control" id="dateStart" name="date_start">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="dateEnd" class="form-label fw-bold">إلى تاريخ:</label>
                                    <input type="date" class="form-control" id="dateEnd" name="date_end">
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>إلغاء
                    </button>
                    <button type="button" class="btn btn-danger" onclick="confirmBulkDelete()">
                        <i class="fas fa-trash-alt me-1"></i>تأكيد الحذف
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // دالة إظهار modal الحذف الكلي
        function showBulkDeleteModal() {
            console.log('showBulkDeleteModal called');
            const modal = document.getElementById('bulkDeleteModal');
            if (modal) {
                const bootstrapModal = new bootstrap.Modal(modal);
                bootstrapModal.show();
            } else {
                console.error('Modal not found');
            }
        }

        // دالة تغيير نوع التاريخ
        function changeDateType() {
            const dateType = document.getElementById('dateType').value;
            const singleDateDiv = document.getElementById('singleDateDiv');
            const dateRangeDiv = document.getElementById('dateRangeDiv');
            
            // إخفاء جميع الحقول أولاً
            singleDateDiv.style.display = 'none';
            dateRangeDiv.style.display = 'none';
            
            // إظهار الحقول المناسبة
            if (dateType === 'before' || dateType === 'after' || dateType === 'specific') {
                singleDateDiv.style.display = 'block';
            } else if (dateType === 'between') {
                dateRangeDiv.style.display = 'block';
            }
        }

        // دالة تأكيد الحذف
        function confirmBulkDelete() {
            const dateType = document.getElementById('dateType').value;
            let confirmMessage = '';
            
            switch(dateType) {
                case 'all':
                    confirmMessage = 'هل أنت متأكد من حذف جميع سجلات الأنشطة؟';
                    break;
                case 'before':
                    confirmMessage = 'هل أنت متأكد من حذف جميع السجلات قبل التاريخ المحدد؟';
                    break;
                case 'after':
                    confirmMessage = 'هل أنت متأكد من حذف جميع السجلات بعد التاريخ المحدد؟';
                    break;
                case 'specific':
                    confirmMessage = 'هل أنت متأكد من حذف جميع السجلات في التاريخ المحدد؟';
                    break;
                case 'between':
                    confirmMessage = 'هل أنت متأكد من حذف جميع السجلات بين التاريخين المحددين؟';
                    break;
            }
            
            if (confirm(confirmMessage + '\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
                document.getElementById('bulkDeleteForm').submit();
            }
        }
    </script>
</body>
</html>
