#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح شامل لجميع أزرار الحذف في البرنامج
Comprehensive fix for all delete buttons in the application
"""

import sqlite3
import os
import shutil
from datetime import datetime

def backup_database():
    """عمل نسخة احتياطية من قاعدة البيانات"""
    if os.path.exists('maintenance.db'):
        backup_name = f'maintenance_delete_fix_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        shutil.copy2('maintenance.db', backup_name)
        print(f"✅ تم حفظ نسخة احتياطية: {backup_name}")
        return backup_name
    return None

def test_delete_routes():
    """اختبار جميع routes الحذف"""
    
    print("🧪 اختبار routes الحذف...")
    print("🧪 Testing delete routes...")
    print("=" * 60)
    
    if not os.path.exists('maintenance.db'):
        print("❌ ملف قاعدة البيانات غير موجود")
        return False
    
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # اختبار جدول users (للمستخدمين)
        print("\n👥 اختبار حذف المستخدمين:")
        try:
            cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
            admin_count = cursor.fetchone()[0]
            print(f"   ✅ عدد المديرين: {admin_count}")
            
            cursor.execute("SELECT id, username, role FROM users LIMIT 3")
            users = cursor.fetchall()
            print(f"   ✅ عدد المستخدمين: {len(users)}")
            for user in users:
                print(f"      - {dict(user)}")
        except Exception as e:
            print(f"   ❌ خطأ في جدول users: {e}")
        
        # اختبار جدول marches (للأسواق)
        print("\n📋 اختبار حذف الأسواق:")
        try:
            cursor.execute("SELECT numero_marche, objet_marche FROM marches LIMIT 3")
            marches = cursor.fetchall()
            print(f"   ✅ عدد الأسواق: {len(marches)}")
            for marche in marches:
                print(f"      - {dict(marche)}")
        except Exception as e:
            print(f"   ❌ خطأ في جدول marches: {e}")
        
        # اختبار جدول interventions (للتدخلات)
        print("\n🔧 اختبار حذف التدخلات:")
        try:
            cursor.execute("SELECT id, type_intervention, numero_marche FROM interventions LIMIT 3")
            interventions = cursor.fetchall()
            print(f"   ✅ عدد التدخلات: {len(interventions)}")
            for intervention in interventions:
                print(f"      - {dict(intervention)}")
        except Exception as e:
            print(f"   ❌ خطأ في جدول interventions: {e}")
        
        # اختبار جدول reclamations (للشكاوى)
        print("\n📞 اختبار حذف الشكاوى:")
        try:
            cursor.execute("SELECT id, numero_reclamation, type_reclamation FROM reclamations LIMIT 3")
            reclamations = cursor.fetchall()
            print(f"   ✅ عدد الشكاوى: {len(reclamations)}")
            for reclamation in reclamations:
                print(f"      - {dict(reclamation)}")
        except Exception as e:
            print(f"   ❌ خطأ في جدول reclamations: {e}")
        
        # اختبار جدول regions (للمناطق)
        print("\n🌍 اختبار حذف المناطق:")
        try:
            cursor.execute("SELECT id, nom_region FROM regions LIMIT 3")
            regions = cursor.fetchall()
            print(f"   ✅ عدد المناطق: {len(regions)}")
            for region in regions:
                print(f"      - {dict(region)}")
        except Exception as e:
            print(f"   ❌ خطأ في جدول regions: {e}")
        
        # اختبار جدول sites (للمواقع)
        print("\n📍 اختبار حذف المواقع:")
        try:
            cursor.execute("SELECT id, nom_site, region_id FROM sites LIMIT 3")
            sites = cursor.fetchall()
            print(f"   ✅ عدد المواقع: {len(sites)}")
            for site in sites:
                print(f"      - {dict(site)}")
        except Exception as e:
            print(f"   ❌ خطأ في جدول sites: {e}")
        
        conn.close()
        
        print("\n🎉 انتهى اختبار routes الحذف!")
        print("🎉 Delete routes test completed!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def check_delete_routes_in_app():
    """فحص routes الحذف في ملف app.py"""
    
    print("\n🔍 فحص routes الحذف في app.py:")
    print("🔍 Checking delete routes in app.py:")
    print("=" * 60)
    
    if not os.path.exists('app.py'):
        print("❌ ملف app.py غير موجود")
        return False
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        delete_routes = {
            'supprimer_utilisateur': 'حذف المستخدمين',
            'supprimer_marche': 'حذف الأسواق',
            'supprimer_intervention': 'حذف التدخلات',
            'supprimer_reclamation': 'حذف الشكاوى',
            'supprimer_region': 'حذف المناطق',
            'supprimer_site': 'حذف المواقع',
            'supprimer_detail_intervention': 'حذف تفاصيل التدخلات',
            'supprimer_detail_reclamation': 'حذف تفاصيل الشكاوى'
        }
        
        for route, description in delete_routes.items():
            if f"def {route}(" in content:
                print(f"   ✅ {description}: {route}")
            else:
                print(f"   ❌ {description}: {route} غير موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص app.py: {e}")
        return False

def show_delete_button_examples():
    """عرض أمثلة على أزرار الحذف الصحيحة"""
    
    print("\n📝 أمثلة على أزرار الحذف الصحيحة:")
    print("📝 Examples of correct delete buttons:")
    print("=" * 60)
    
    examples = {
        "المستخدمين": """
<button type="button"
        class="btn btn-sm btn-danger"
        onclick="confirmDelete('{{ user.username }}', '{{ url_for('supprimer_utilisateur', id=user.id) }}')"
        title="Supprimer">
    <i class="fas fa-trash"></i>
</button>""",
        
        "الأسواق": """
<button type="button"
        class="btn btn-sm btn-danger"
        onclick="confirmDelete('{{ marche.numero_marche }}', '{{ url_for('supprimer_marche', numero=marche.numero_marche) }}')"
        title="Supprimer">
    <i class="fas fa-trash"></i>
</button>""",
        
        "التدخلات": """
<button type="button"
        class="btn btn-sm btn-danger"
        onclick="confirmDelete('Intervention #{{ intervention.id }}', '{{ url_for('supprimer_intervention', id=intervention.id) }}')"
        title="Supprimer">
    <i class="fas fa-trash"></i>
</button>""",
        
        "الشكاوى": """
<button type="button"
        class="btn btn-sm btn-danger"
        onclick="confirmDelete('{{ reclamation.numero_reclamation }}', '{{ url_for('supprimer_reclamation', id=reclamation.id) }}')"
        title="Supprimer">
    <i class="fas fa-trash"></i>
</button>""",
        
        "المناطق": """
<button type="button"
        class="btn btn-sm btn-danger"
        onclick="confirmDelete('{{ region.nom_region }}', '{{ url_for('supprimer_region', id=region.id) }}')"
        title="Supprimer">
    <i class="fas fa-trash"></i>
</button>""",
        
        "المواقع": """
<button type="button"
        class="btn btn-sm btn-danger"
        onclick="confirmDelete('{{ site.nom_site }}', '{{ url_for('supprimer_site', id=site.id) }}')"
        title="Supprimer">
    <i class="fas fa-trash"></i>
</button>"""
    }
    
    for module, example in examples.items():
        print(f"\n🔹 {module}:")
        print(example)

def main():
    """الدالة الرئيسية"""
    
    print("🗑️ إصلاح شامل لأزرار الحذف")
    print("🗑️ Comprehensive Delete Buttons Fix")
    print("=" * 60)
    
    # عمل نسخة احتياطية
    backup_file = backup_database()
    
    # فحص routes الحذف في app.py
    if check_delete_routes_in_app():
        print("✅ تم فحص routes الحذف في app.py")
    else:
        print("❌ مشاكل في routes الحذف في app.py")
    
    # اختبار routes الحذف
    if test_delete_routes():
        print("✅ جميع routes الحذف تعمل")
    else:
        print("❌ مشاكل في routes الحذف")
    
    # عرض أمثلة على أزرار الحذف الصحيحة
    show_delete_button_examples()
    
    print("\n🎉 تم الانتهاء من فحص أزرار الحذف!")
    print("🎉 Delete buttons check completed!")
    
    if backup_file:
        print(f"\n💾 النسخة الاحتياطية: {backup_file}")
        print(f"💾 Backup file: {backup_file}")
    
    print("\n📋 الخطوات التالية:")
    print("📋 Next steps:")
    print("   1. تأكد من أن جميع routes الحذف موجودة")
    print("   1. Make sure all delete routes exist")
    print("   2. تأكد من أن أزرار الحذف تستخدم confirmDelete()")
    print("   2. Make sure delete buttons use confirmDelete()")
    print("   3. اختبر أزرار الحذف في المتصفح")
    print("   3. Test delete buttons in browser")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
