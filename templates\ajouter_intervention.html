{% extends "base.html" %}

{% block title %}Ajouter une Intervention - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Ajouter une Intervention{% endblock %}

{% block extra_css %}
<style>
  /* Styles pour les input-group */
  .input-group {
    margin-bottom: 0;
    border-radius: 0.25rem;
    transition: all 0.25s ease-in-out;
  }

  .input-group-prepend {
    display: flex;
  }

  .input-group-text {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: center;
    white-space: nowrap;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    border-radius: 0.25rem 0 0 0.25rem;
    width: 42px; /* Largeur fixe pour tous les input-group-text */
  }

  .input-group-text i {
    font-size: 1rem;
    width: 100%;
    text-align: center;
  }

  .form-select, .form-control {
    border-radius: 0 0.25rem 0.25rem 0;
  }

  /* Style pour le champ Semestre */
  .semestre-field {
    position: relative;
  }

  .semestre-field::after {
    content: "Nouveau";
    position: absolute;
    top: -10px;
    right: 0;
    background-color: #ffc107;
    color: #000;
    font-size: 0.7rem;
    padding: 0.1rem 0.5rem;
    border-radius: 1rem;
    font-weight: bold;
  }

  /* Styles pour améliorer l'apparence des icônes */
  .fas {
    display: inline-block;
    text-align: center;
    width: 1.25em;
  }

  /* Styles pour les états de focus et hover */
  .input-group:focus-within {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
  }

  .input-group:focus-within .input-group-text {
    border-color: #86b7fe;
    background-color: #e9f2ff;
  }

  .input-group:hover .input-group-text {
    background-color: #e2e6ea;
  }

    /* Boutons modernes */
    .btn-modern {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: none;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .btn-modern:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn-modern i {
        font-size: 0.9em;
    }

    /* Couleurs spécifiques */
    .btn-primary.btn-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .btn-success.btn-modern {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .btn-danger.btn-modern {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .btn-warning.btn-modern {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #333;
    }

    .btn-info.btn-modern {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
    }

    .btn-outline-primary.btn-modern {
        border: 2px solid #667eea;
        color: #667eea;
        background: transparent;
    }

    .btn-outline-primary.btn-modern:hover {
        background: #667eea;
        color: white;
    }

</style>
{% endblock %}

{% block content %}
<div class="container mt-4 mb-5">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="text-primary"><i class="fas fa-tools me-2"></i> Ajouter une nouvelle intervention</h4>
    <a href="{{ url_for('interventions') }}" class="btn btn-outline-secondary">
      <i class="fas fa-arrow-left me-1"></i> Retour
    </a>
  </div>

  <!-- Card globale -->
  <div class="card shadow-sm">
    <div class="card-body">
      <form method="POST" action="{{ url_for('ajouter_intervention') }}">
        <!-- Informations du marché -->
        <div class="row mb-4">
          <div class="col-md-6 mb-3">
            <label for="numero_marche" class="form-label fw-bold">N° de marché</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="fas fa-file-contract"></i>
              </span>
              <select class="form-select" id="numero_marche" name="numero_marche" required>
                <option value="">Sélectionner un marché</option>
                {% for marche in marches %}
                <option value="{{ marche.numero }}" data-client="{{ marche.client }}" data-objet="{{ marche.objet }}" data-delai="{{ marche.delai_execution }}" data-domaine="{{ marche.domaine }}" data-periode="{{ marche.periode_interventions }}" data-lieu="{{ marche.lieu }}">
                  {{ marche.numero }} - {{ marche.client }} ({{ marche.domaine }})
                </option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <label for="domaine" class="form-label fw-bold">Domaine</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="fas fa-cogs"></i>
              </span>
              <select class="form-select" id="domaine" name="domaine" required>
                <option value="">Sélectionner un domaine</option>
                <option value="SVS">SVS</option>
                <option value="EXTINCTEUR">EXTINCTEUR</option>
                <option value="SYSTEME D'INCENDIE">SYSTEME D'INCENDIE</option>
                <option value="SYSTEME D'ALARME">SYSTEME D'ALARME</option>
                <option value="SYSTEME TELEPHONIQUE">SYSTEME TELEPHONIQUE</option>
                <option value="L'AFFICHAGE DYNAMIQUE">L'AFFICHAGE DYNAMIQUE</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Informations client et objet -->
        <div class="row mb-4">
          <div class="col-md-6 mb-3">
            <label for="client" class="form-label fw-bold">Client</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="fas fa-user-tie"></i>
              </span>
              <input type="text" class="form-control" id="client" name="client" required>
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <label for="objet_marche" class="form-label fw-bold">Objet de marché</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="fas fa-clipboard-list"></i>
              </span>
              <input type="text" class="form-control" id="objet_marche" name="objet_marche" required>
            </div>
          </div>
        </div>

        <!-- Informations périodes et délais -->
        <div class="row mb-4">
          <div class="col-md-3 mb-3">
            <label for="delai_execution" class="form-label fw-bold">Délai d'exécution</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="fas fa-clock"></i>
              </span>
              <input type="text" class="form-control" id="delai_execution" name="delai_execution">
            </div>
          </div>
          <div class="col-md-3 mb-3">
            <label for="periode_interventions" class="form-label fw-bold">Période d'interventions</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="fas fa-calendar-alt"></i>
              </span>
              <select class="form-select" id="periode_interventions" name="periode_interventions">
                <option value="">Sélectionner une période</option>
                <option value="annuel">Annuel</option>
                <option value="semestriel">Semestriel</option>
                <option value="trimestriel">Trimestriel</option>
                <option value="mensuel">Mensuel</option>
              </select>
            </div>
          </div>
          <div class="col-md-3 mb-3 semestre-field">
            <label for="semestre" class="form-label fw-bold">Semestre</label>
            <div class="input-group">
              <span class="input-group-text bg-warning text-white">
                <i class="fas fa-calendar-check"></i>
              </span>
              <select class="form-select border-warning" id="semestre" name="semestre">
                <option value="">Sélectionner un semestre</option>
                <option value="S1">Semestre 1</option>
                <option value="S2">Semestre 2</option>
                <option value="S3">Semestre 3</option>
                <option value="S4">Semestre 4</option>
              </select>
            </div>
          </div>
          <div class="col-md-3 mb-3">
            <label for="periode_marche" class="form-label fw-bold">Période du marché</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="fas fa-calendar-week"></i>
              </span>
              <input type="text" class="form-control" id="periode_marche" name="periode_marche" placeholder="Ex: 2023-2024">
            </div>
          </div>
        </div>

        <!-- Lieu -->
        <div class="row mb-4">
          <div class="col-md-12 mb-3">
            <label for="lieu" class="form-label fw-bold">Lieu</label>
            <div class="input-group">
              <span class="input-group-text">
                <i class="fas fa-map-marker-alt"></i>
              </span>
              <input type="text" class="form-control" id="lieu" name="lieu">
            </div>
          </div>
        </div>

        <!-- Tableaux spécifiques par domaine -->
        <div class="domain-tables mt-4">
          <!-- SVS -->
          <div id="svs-details" class="domain-details mb-4 card shadow-sm" style="display: none;">
            <div class="card-header bg-primary text-white">
              <h5 class="mb-0"><i class="fas fa-server me-2"></i> Détails de l'intervention SVS</h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead class="table-light">
                    <tr>
                      <th>N°</th>
                      <th>Région</th>
                      <th>Nom de site</th>
                      <th>Nbr de système</th>
                      <th>Date d'intervention</th>
                      <th>Technicien</th>
                      <th>Situation</th>
                      <th>État de matériel</th>
                      <th>Observation</th>
                      <th>Téléphone chef de site</th>
                      <th>Téléphone de sécurité</th>
                      <th>Technicien à contacter</th>
                      <th>GPS</th>
                    </tr>
                  </thead>
                  <tbody id="svs-table-body">
                    <tr>
                      <td colspan="13" class="text-center py-3">
                        <div class="alert alert-info mb-0">
                          <i class="fas fa-info-circle me-2"></i> Vous pourrez ajouter des détails après avoir enregistré l'intervention
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <button type="button" class="btn btn-outline-primary mt-2" disabled>
                <i class="fas fa-plus me-1"></i> Ajouter une ligne
              </button>
            </div>
          </div>

          <!-- SYSTEME D'INCENDIE -->
          <div id="systeme-incendie-details" class="domain-details mb-4 card shadow-sm" style="display: none;">
            <div class="card-header bg-danger text-white">
              <h5 class="mb-0"><i class="fas fa-fire-extinguisher me-2"></i> Détails de l'intervention SYSTÈME D'INCENDIE</h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead class="table-light">
                    <tr>
                      <th>N°</th>
                      <th>Région</th>
                      <th>Nom de site</th>
                      <th>Nbr de système</th>
                      <th>Date d'intervention</th>
                      <th>Technicien</th>
                      <th>Situation</th>
                      <th>État de matériel</th>
                      <th>Observation</th>
                      <th>Téléphone chef de site</th>
                      <th>Téléphone de sécurité</th>
                      <th>Technicien à contacter</th>
                      <th>GPS</th>
                    </tr>
                  </thead>
                  <tbody id="systeme-incendie-table-body">
                    <tr>
                      <td colspan="13" class="text-center py-3">
                        <div class="alert alert-info mb-0">
                          <i class="fas fa-info-circle me-2"></i> Vous pourrez ajouter des détails après avoir enregistré l'intervention
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <button type="button" class="btn btn-outline-danger mt-2" disabled>
                <i class="fas fa-plus me-1"></i> Ajouter une ligne
              </button>
            </div>
          </div>

          <!-- SYSTEME D'ALARME -->
          <div id="systeme-alarme-details" class="domain-details mb-4 card shadow-sm" style="display: none;">
            <div class="card-header bg-warning text-dark">
              <h5 class="mb-0"><i class="fas fa-bell me-2"></i> Détails de l'intervention SYSTÈME D'ALARME</h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead class="table-light">
                    <tr>
                      <th>N°</th>
                      <th>Région</th>
                      <th>Nom de site</th>
                      <th>Nbr de système</th>
                      <th>Date d'intervention</th>
                      <th>Technicien</th>
                      <th>Situation</th>
                      <th>État de matériel</th>
                      <th>Observation</th>
                      <th>Téléphone chef de site</th>
                      <th>Téléphone de sécurité</th>
                      <th>Technicien à contacter</th>
                      <th>GPS</th>
                    </tr>
                  </thead>
                  <tbody id="systeme-alarme-table-body">
                    <tr>
                      <td colspan="13" class="text-center py-3">
                        <div class="alert alert-info mb-0">
                          <i class="fas fa-info-circle me-2"></i> Vous pourrez ajouter des détails après avoir enregistré l'intervention
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <button type="button" class="btn btn-outline-warning mt-2" disabled>
                <i class="fas fa-plus me-1"></i> Ajouter une ligne
              </button>
            </div>
          </div>

          <!-- SYSTEME TELEPHONIQUE -->
          <div id="systeme-telephonique-details" class="domain-details mb-4 card shadow-sm" style="display: none;">
            <div class="card-header bg-info text-white">
              <h5 class="mb-0"><i class="fas fa-phone-alt me-2"></i> Détails de l'intervention SYSTÈME TÉLÉPHONIQUE</h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead class="table-light">
                    <tr>
                      <th>N°</th>
                      <th>Région</th>
                      <th>Nom de site</th>
                      <th>Nbr de système</th>
                      <th>Date d'intervention</th>
                      <th>Technicien</th>
                      <th>Situation</th>
                      <th>État de matériel</th>
                      <th>Observation</th>
                      <th>Téléphone chef de site</th>
                      <th>Téléphone de sécurité</th>
                      <th>Technicien à contacter</th>
                      <th>GPS</th>
                    </tr>
                  </thead>
                  <tbody id="systeme-telephonique-table-body">
                    <tr>
                      <td colspan="13" class="text-center py-3">
                        <div class="alert alert-info mb-0">
                          <i class="fas fa-info-circle me-2"></i> Vous pourrez ajouter des détails après avoir enregistré l'intervention
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <button type="button" class="btn btn-outline-info mt-2" disabled>
                <i class="fas fa-plus me-1"></i> Ajouter une ligne
              </button>
            </div>
          </div>

          <!-- EXTINCTEUR -->
          <div id="extincteur-details" class="domain-details mb-4 card shadow-sm" style="display: none;">
            <div class="card-header bg-success text-white">
              <h5 class="mb-0"><i class="fas fa-fire me-2"></i> Détails de l'intervention EXTINCTEUR</h5>
            </div>
            <div class="card-body">
              <div class="table-responsive">
                <table class="table table-bordered table-hover">
                  <thead class="table-light">
                    <tr>
                      <th>N°</th>
                      <th>Région</th>
                      <th>Nom de site</th>
                      <th>Nbr de système</th>
                      <th>Date d'intervention</th>
                      <th>Technicien</th>
                      <th>Situation</th>
                      <th>État de matériel</th>
                      <th>Observation</th>
                      <th>Téléphone chef de site</th>
                      <th>Téléphone de sécurité</th>
                      <th>Technicien à contacter</th>
                      <th>GPS</th>
                    </tr>
                  </thead>
                  <tbody id="extincteur-table-body">
                    <tr>
                      <td colspan="13" class="text-center py-3">
                        <div class="alert alert-info mb-0">
                          <i class="fas fa-info-circle me-2"></i> Vous pourrez ajouter des détails après avoir enregistré l'intervention
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
              <button type="button" class="btn btn-outline-success mt-2" disabled>
                <i class="fas fa-plus me-1"></i> Ajouter une ligne
              </button>
            </div>
          </div>
        </div>

        <div class="alert alert-info mt-4">
          <i class="fas fa-info-circle me-2"></i> Après avoir enregistré l'intervention, vous pourrez ajouter les détails spécifiques.
        </div>

        <!-- Boutons d'action -->
        <div class="d-flex justify-content-between mt-4">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-1"></i> Enregistrer et continuer
          </button>
          <div>
            <button type="reset" class="btn btn-outline-secondary me-2">
              <i class="fas fa-undo me-1"></i> Réinitialiser
            </button>
            <a href="{{ url_for('interventions') }}" class="btn btn-outline-danger">
              <i class="fas fa-times me-1"></i> Annuler
            </a>
          </div>
        </div>

          <!-- Tableau pour L'AFFICHAGE DYNAMIQUE -->
          <div id="affichage-dynamique-details" class="domain-table" style="display: none;">
            <div class="card mt-4">
              <div class="card-header bg-dark text-white">
                <h5 class="mb-0"><i class="fas fa-desktop me-2"></i>Détails L'AFFICHAGE DYNAMIQUE</h5>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-bordered">
                    <thead class="table-dark">
                      <tr>
                        <th>Région</th>
                        <th>Nom du site</th>
                        <th>GPS</th>
                        <th>Situation</th>
                        <th>État du matériel</th>
                        <th>Type d'affichage</th>
                        <th>Résolution</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody id="affichage-dynamique-tbody">
                      <!-- Les lignes seront ajoutées dynamiquement -->
                    </tbody>
                  </table>
                </div>
                <button type="button" class="btn btn-dark btn-sm" onclick="addAffichageRow()">
                  <i class="fas fa-plus me-1"></i>Ajouter une ligne
                </button>
              </div>
            </div>
          </div></form>
    </div>
  </div>
</div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Fonction pour afficher le tableau correspondant au domaine sélectionné
        function showDomainTable(domaine) {
            // Cacher tous les tableaux
            document.querySelectorAll('.domain-details').forEach(function(table) {
                table.style.display = 'none';
            });

            // Afficher le tableau correspondant au domaine sélectionné
            let tableId = '';
            switch(domaine) {
                case 'SVS':
                    tableId = 'svs-details';
                    break;
                case 'EXTINCTEUR':
                    tableId = 'extincteur-details';
                    break;
                case 'SYSTEME D\'INCENDIE':
                    tableId = 'systeme-incendie-details';
                    break;
                case 'SYSTEME D\'ALARME':
                    tableId = 'systeme-alarme-details';
                    break;
                case 'SYSTEME TELEPHONIQUE':
                    tableId = 'systeme-telephonique-details';
                    break;
                case 'L\'AFFICHAGE DYNAMIQUE':
                    tableId = 'affichage-dynamique-details';
                    break;
                default:
                    return;
            }

            if (tableId) {
                document.getElementById(tableId).style.display = 'block';
            }
        }

        // Fonction pour mettre à jour le champ semestre en fonction de la période d'interventions
        function updateSemestreField() {
            const periodeSelect = document.getElementById('periode_interventions');
            const semestreSelect = document.getElementById('semestre');

            // Le champ semestre est toujours visible, mais on le rend obligatoire uniquement pour la période semestrielle
            if (periodeSelect.value === 'semestriel') {
                semestreSelect.required = true;
                // Ajouter une astérisque à côté du label pour indiquer que c'est obligatoire
                const label = document.querySelector('label[for="semestre"]');
                if (label && !label.querySelector('.text-danger')) {
                    const asterisk = document.createElement('span');
                    asterisk.className = 'text-danger ms-1';
                    asterisk.textContent = '*';
                    label.appendChild(asterisk);
                }
            } else {
                semestreSelect.required = false;
                // Supprimer l'astérisque si elle existe
                const label = document.querySelector('label[for="semestre"]');
                const asterisk = label.querySelector('.text-danger');
                if (asterisk) {
                    label.removeChild(asterisk);
                }
            }
        }

        // Auto-remplir les champs en fonction du marché sélectionné
        document.getElementById('numero_marche').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];

            if (selectedOption.value) {
                document.getElementById('client').value = selectedOption.getAttribute('data-client');
                document.getElementById('objet_marche').value = selectedOption.getAttribute('data-objet');
                document.getElementById('delai_execution').value = selectedOption.getAttribute('data-delai');
                document.getElementById('lieu').value = selectedOption.getAttribute('data-lieu');

                // Sélectionner le domaine
                const domaine = selectedOption.getAttribute('data-domaine');
                const domaineSelect = document.getElementById('domaine');
                for (let i = 0; i < domaineSelect.options.length; i++) {
                    if (domaineSelect.options[i].value === domaine) {
                        domaineSelect.selectedIndex = i;
                        break;
                    }
                }

                // Afficher le tableau correspondant au domaine
                showDomainTable(domaine);

                // Sélectionner la période d'interventions
                const periode = selectedOption.getAttribute('data-periode');
                const periodeSelect = document.getElementById('periode_interventions');
                for (let i = 0; i < periodeSelect.options.length; i++) {
                    if (periodeSelect.options[i].value === periode) {
                        periodeSelect.selectedIndex = i;
                        break;
                    }
                }

                // Mettre à jour le champ semestre
                updateSemestreField();
            }
        });

        // Afficher le tableau correspondant au domaine sélectionné
        document.getElementById('domaine').addEventListener('change', function() {
            const domaine = this.value;
            showDomainTable(domaine);
        });

        // Mettre à jour le champ semestre lorsque la période d'interventions change
        document.getElementById('periode_interventions').addEventListener('change', updateSemestreField);

        // Pré-sélectionner un marché si spécifié dans l'URL
        const urlParams = new URLSearchParams(window.location.search);
        const marcheParam = urlParams.get('marche');

        if (marcheParam) {
            const marcheSelect = document.getElementById('numero_marche');
            for (let i = 0; i < marcheSelect.options.length; i++) {
                if (marcheSelect.options[i].value === marcheParam) {
                    marcheSelect.selectedIndex = i;
                    // Déclencher l'événement change pour remplir les autres champs
                    marcheSelect.dispatchEvent(new Event('change'));
                    break;
                }
            }
        }

        // Initialiser l'état du champ semestre au chargement
        updateSemestreField();

        // Ajouter des effets visuels pour améliorer l'expérience utilisateur
        const formInputs = document.querySelectorAll('input, select');
        formInputs.forEach(input => {
            // Ajouter une classe lors du focus
            input.addEventListener('focus', function() {
                const inputGroup = this.closest('.input-group');
                if (inputGroup) {
                    inputGroup.style.borderColor = '#0d6efd';
                    inputGroup.style.boxShadow = '0 0 0 0.25rem rgba(13, 110, 253, 0.25)';
                    const icon = inputGroup.querySelector('.input-group-text');
                    if (icon) {
                        icon.style.backgroundColor = '#e9ecef';
                        icon.style.borderColor = '#0d6efd';
                    }
                }
            });

            // Retirer la classe lors de la perte du focus
            input.addEventListener('blur', function() {
                const inputGroup = this.closest('.input-group');
                if (inputGroup) {
                    inputGroup.style.borderColor = '';
                    inputGroup.style.boxShadow = '';
                    const icon = inputGroup.querySelector('.input-group-text');
                    if (icon) {
                        icon.style.backgroundColor = '';
                        icon.style.borderColor = '';
                    }
                }
            });
        });
    });

    // Function pour ajouter une ligne L'AFFICHAGE DYNAMIQUE
    function addAffichageRow() {
        const tbody = document.getElementById('affichage-dynamique-tbody');
        const rowCount = tbody.children.length;

        const row = document.createElement('tr');
        row.innerHTML = `
            <td><input type="text" class="form-control form-control-sm" name="affichage_region_${rowCount}" placeholder="Région"></td>
            <td><input type="text" class="form-control form-control-sm" name="affichage_site_${rowCount}" placeholder="Nom du site"></td>
            <td><input type="text" class="form-control form-control-sm" name="affichage_gps_${rowCount}" placeholder="GPS"></td>
            <td><input type="text" class="form-control form-control-sm" name="affichage_situation_${rowCount}" placeholder="Situation"></td>
            <td>
                <select class="form-select form-select-sm" name="affichage_etat_${rowCount}">
                    <option value="">État</option>
                    <option value="Bon">Bon</option>
                    <option value="Moyen">Moyen</option>
                    <option value="Mauvais">Mauvais</option>
                </select>
            </td>
            <td>
                <select class="form-select form-select-sm" name="affichage_type_${rowCount}">
                    <option value="">Type</option>
                    <option value="LED">LED</option>
                    <option value="LCD">LCD</option>
                    <option value="OLED">OLED</option>
                </select>
            </td>
            <td><input type="text" class="form-control form-control-sm" name="affichage_resolution_${rowCount}" placeholder="1920x1080"></td>
            <td>
                <button type="button" class="btn btn-danger btn-sm" onclick="this.closest('tr').remove()">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;

        tbody.appendChild(row);
    }
</script>
{% endblock %}
