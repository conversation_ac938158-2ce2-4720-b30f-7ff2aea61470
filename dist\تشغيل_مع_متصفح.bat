@echo off
title نظام إدارة الصيانة - تشغيل محسن
color 0A
echo.
echo ========================================
echo    نظام إدارة الصيانة
echo    Maintenance Management System
echo    تشغيل محسن مع فتح المتصفح
echo ========================================
echo.

echo 🚀 بدء تشغيل النظام...
echo 🚀 Starting the system...
echo.

REM تشغيل الملف التنفيذي في الخلفية
echo 📡 تشغيل الخادم...
start /B "" "Maintenance_Management_System.exe"

echo ⏰ انتظار تشغيل الخادم...
echo ⏰ Waiting for server to start...
timeout /t 5 /nobreak >nul

echo.
echo 🌐 محاولة فتح المتصفح...
echo 🌐 Attempting to open browser...
echo.

REM محاولة فتح المتصفح بمنافذ مختلفة
echo 🔗 جاري فتح الروابط المحتملة...
start "" "http://127.0.0.1:5000"
timeout /t 2 /nobreak >nul

start "" "http://127.0.0.1:8000"
timeout /t 2 /nobreak >nul

start "" "http://127.0.0.1:8080"
timeout /t 2 /nobreak >nul

start "" "http://127.0.0.1:3000"
timeout /t 2 /nobreak >nul

echo.
echo ✅ تم تشغيل النظام!
echo ✅ System started successfully!
echo.
echo 📋 معلومات مهمة:
echo 📋 Important information:
echo.
echo 🌐 الروابط المحتملة:
echo    • http://127.0.0.1:5000
echo    • http://127.0.0.1:8000
echo    • http://127.0.0.1:8080
echo    • http://127.0.0.1:3000
echo.
echo 💡 إذا لم يفتح المتصفح تلقائياً:
echo 💡 If browser doesn't open automatically:
echo    1. افتح المتصفح يدوياً
echo    2. انسخ أحد الروابط أعلاه
echo    3. الصقه في شريط العنوان
echo.
echo 🌍 للوصول من أجهزة أخرى:
echo 🌍 To access from other devices:
echo    1. اعرف IP الجهاز: ipconfig
echo    2. استخدم: http://[IP]:PORT
echo.
echo 📝 للإيقاف: أغلق هذه النافذة أو اضغط Ctrl+C
echo 📝 To stop: Close this window or press Ctrl+C
echo.
echo ========================================
echo    النظام يعمل الآن!
echo    System is now running!
echo ========================================
echo.

REM إبقاء النافذة مفتوحة
:loop
timeout /t 30 /nobreak >nul
echo 💓 النظام يعمل... %date% %time%
goto loop
