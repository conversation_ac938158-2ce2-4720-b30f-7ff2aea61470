/* CSS للحوار المحسن للحذف */

.delete-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(5px);
    z-index: 9999;
    display: none;
    align-items: center;
    justify-content: center;
    animation: fadeIn 0.3s ease;
}

.delete-modal.show {
    display: flex;
}

.delete-modal-content {
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    max-width: 450px;
    width: 90%;
    overflow: hidden;
    transform: scale(0.8);
    animation: modalSlideIn 0.3s ease forwards;
}

.delete-modal-header {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 20px 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.delete-modal-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
}

.delete-modal-title {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    margin: 0;
    z-index: 1;
}

.delete-modal-title i {
    font-size: 20px;
    margin-right: 10px;
    animation: pulse 2s infinite;
}

.delete-modal-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
    z-index: 1;
}

.delete-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.delete-modal-body {
    padding: 30px 25px;
    text-align: center;
}

.delete-modal-message {
    font-size: 16px;
    color: #2c3e50;
    margin-bottom: 20px;
    line-height: 1.6;
}

.delete-modal-item {
    font-weight: 600;
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
    padding: 8px 15px;
    border-radius: 8px;
    display: inline-block;
    margin: 10px 0;
}

.delete-modal-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border: 1px solid #ffc107;
    border-radius: 10px;
    padding: 15px;
    margin: 20px 0;
    display: flex;
    align-items: center;
    color: #856404;
}

.delete-modal-warning i {
    font-size: 20px;
    margin-right: 10px;
    color: #f39c12;
    animation: bounce 2s infinite;
}

.delete-modal-footer {
    padding: 20px 25px;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    gap: 15px;
}

.delete-modal-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.delete-modal-btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.delete-modal-btn:hover::before {
    width: 300px;
    height: 300px;
}

.delete-modal-btn-cancel {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
    color: white;
    box-shadow: 0 4px 15px rgba(149, 165, 166, 0.3);
}

.delete-modal-btn-cancel:hover {
    background: linear-gradient(135deg, #7f8c8d, #95a5a6);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(149, 165, 166, 0.4);
}

.delete-modal-btn-delete {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
}

.delete-modal-btn-delete:hover {
    background: linear-gradient(135deg, #c0392b, #e74c3c);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
}

.delete-modal-btn:active {
    transform: translateY(0);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes modalSlideIn {
    from {
        transform: scale(0.8) translateY(-50px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-5px); }
    60% { transform: translateY(-3px); }
}

/* Responsive */
@media (max-width: 768px) {
    .delete-modal-content {
        max-width: 95%;
        margin: 20px;
    }
    
    .delete-modal-header,
    .delete-modal-body,
    .delete-modal-footer {
        padding: 20px;
    }
    
    .delete-modal-footer {
        flex-direction: column;
    }
    
    .delete-modal-btn {
        margin-bottom: 10px;
    }
}
