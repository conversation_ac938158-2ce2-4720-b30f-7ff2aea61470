// حل مباشر وبسيط لأزرار الحذف
// Direct and simple solution for delete buttons

console.log('🔥 تحميل حل الحذف المباشر...');

// دالة الحذف المباشرة
window.confirmDelete = function(itemName, deleteUrl) {
    console.log('🔥 confirmDelete called:', itemName, deleteUrl);
    
    // حوار تأكيد بسيط ومباشر
    const confirmed = confirm(`هل تريد حذف: ${itemName}؟\n\nهذا الإجراء لا يمكن التراجع عنه!`);
    
    if (confirmed) {
        console.log('🔥 User confirmed deletion, redirecting to:', deleteUrl);
        
        // إظهار رسالة تحميل
        document.body.innerHTML += `
            <div id="deleteLoading" style="
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.8); z-index: 99999;
                display: flex; align-items: center; justify-content: center;
                color: white; font-size: 18px;
            ">
                <div style="text-align: center;">
                    <div style="font-size: 48px; margin-bottom: 20px;">⏳</div>
                    <div>جاري الحذف...</div>
                </div>
            </div>
        `;
        
        // الانتقال إلى رابط الحذف
        setTimeout(() => {
            window.location.href = deleteUrl;
        }, 500);
    } else {
        console.log('🔥 User cancelled deletion');
    }
};

// التأكد من أن الدالة متاحة عالمياً
window.addEventListener('load', function() {
    console.log('🔥 Page loaded, confirmDelete available:', typeof window.confirmDelete);
    
    // البحث عن جميع أزرار الحذف وإصلاحها
    const deleteButtons = document.querySelectorAll('button[onclick*="confirmDelete"], a[onclick*="confirmDelete"]');
    console.log('🔥 Found delete buttons:', deleteButtons.length);
    
    deleteButtons.forEach((button, index) => {
        console.log(`🔥 Processing button ${index + 1}:`, button.onclick);
        
        // التأكد من أن onclick يعمل
        if (button.onclick) {
            const originalOnclick = button.onclick.toString();
            console.log(`🔥 Original onclick: ${originalOnclick}`);
            
            // استخراج المعاملات
            const match = originalOnclick.match(/confirmDelete\(['"`]([^'"`]*)['"`],\s*['"`]([^'"`]*)['"`]\)/);
            if (match) {
                const itemName = match[1];
                const deleteUrl = match[2];
                
                console.log(`🔥 Extracted: itemName="${itemName}", deleteUrl="${deleteUrl}"`);
                
                // إعادة تعيين onclick
                button.onclick = function(e) {
                    e.preventDefault();
                    console.log('🔥 Button clicked, calling confirmDelete');
                    window.confirmDelete(itemName, deleteUrl);
                    return false;
                };
                
                console.log(`🔥 Button ${index + 1} fixed successfully`);
            }
        }
    });
    
    // إضافة مستمع للنقرات العامة
    document.addEventListener('click', function(e) {
        const button = e.target.closest('button[onclick*="confirmDelete"], a[onclick*="confirmDelete"]');
        if (button) {
            console.log('🔥 Delete button clicked via event listener');
            e.preventDefault();
            e.stopPropagation();
            
            // استخراج البيانات من onclick
            if (button.onclick) {
                const onclickStr = button.onclick.toString();
                const match = onclickStr.match(/confirmDelete\(['"`]([^'"`]*)['"`],\s*['"`]([^'"`]*)['"`]\)/);
                if (match) {
                    const itemName = match[1];
                    const deleteUrl = match[2];
                    console.log('🔥 Calling confirmDelete from event listener');
                    window.confirmDelete(itemName, deleteUrl);
                }
            }
            return false;
        }
    });
});

// تسجيل أن الملف تم تحميله
console.log('🔥 direct-delete.js loaded successfully');
console.log('🔥 confirmDelete function created:', typeof window.confirmDelete);
