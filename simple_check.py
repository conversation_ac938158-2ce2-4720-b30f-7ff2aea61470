#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص بسيط للتحقق من الوضع الفعلي
"""

import os

def simple_check():
    """فحص بسيط"""
    
    templates = [
        'templates/ajouter_marche.html',
        'templates/modifier_marche.html',
        'templates/ajouter_intervention.html',
        'templates/ajouter_reclamation.html',
        'templates/interventions.html'
    ]
    
    for template in templates:
        if os.path.exists(template):
            print(f"\n📄 {template}:")
            with open(template, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            for i, line in enumerate(lines, 1):
                if 'AFFICHAGE' in line:
                    print(f"   السطر {i}: {line.strip()}")

if __name__ == "__main__":
    simple_check()
