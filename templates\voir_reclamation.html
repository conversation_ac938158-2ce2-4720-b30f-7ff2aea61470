{% extends "base.html" %}

{% block title %}Détails de la Réclamation - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Détails de la Réclamation{% endblock %}

{% block content %}
<div class="container mt-4 mb-5">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="text-primary"><i class="fas fa-exclamation-circle me-2"></i> Détails de la réclamation #{{ reclamation.id }}</h4>
    <div>
      <a href="{{ url_for('reclamations') }}" class="btn btn-outline-secondary me-2">
        <i class="fas fa-arrow-left me-1"></i> Retour à la liste
      </a>
      <button type="button" class="btn btn-primary" onclick="window.print()">
        <i class="fas fa-print me-1"></i> Imprimer
      </button>
    </div>
  </div>

  <!-- Informations générales -->
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-light">
      <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> Informations générales</h5>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-6">
          <table class="table table-borderless">
            <tr>
              <th style="width: 40%">N° de marché:</th>
              <td><strong>{{ reclamation.numero_marche }}</strong></td>
            </tr>
            <tr>
              <th>Client:</th>
              <td>{{ reclamation.client }}</td>
            </tr>
            <tr>
              <th>Objet de marché:</th>
              <td>{{ reclamation.objet_marche }}</td>
            </tr>
            <tr>
              <th>Délai d'exécution:</th>
              <td>{{ reclamation.delai_execution }}</td>
            </tr>
          </table>
        </div>
        <div class="col-md-6">
          <table class="table table-borderless">
            <tr>
              <th style="width: 40%">Domaine:</th>
              <td>
                {% if reclamation.domaine == 'SVS' %}
                <span class="badge bg-primary">SVS</span>
                {% elif reclamation.domaine == 'EXTINCTEUR' %}
                <span class="badge bg-danger">EXTINCTEUR</span>
                {% elif reclamation.domaine == 'SYSTEME D\'INCENDIE' %}
                <span class="badge bg-warning">SYSTEME D'INCENDIE</span>
                {% elif reclamation.domaine == 'SYSTEME D\'ALARME' %}
                <span class="badge bg-info">SYSTEME D'ALARME</span>
                {% elif reclamation.domaine == 'SYSTEME TELEPHONIQUE' %}
                <span class="badge bg-success">SYSTEME TELEPHONIQUE</span>
                {% elif reclamation.domaine == "L'AFFICHAGE DYNAMIQUE" %}
                <span class="badge bg-dark">L'AFFICHAGE DYNAMIQUE</span>
                {% else %}
                <span class="badge bg-secondary">{{ reclamation.domaine }}</span>
                {% endif %}
              </td>
            </tr>
            <tr>
              <th>Période d'interventions:</th>
              <td>{{ reclamation.periode_interventions }}</td>
            </tr>
            <tr>
              <th>Période du marché:</th>
              <td>{{ reclamation.periode_marche }}</td>
            </tr>
            <tr>
              <th>Lieu:</th>
              <td>{{ reclamation.lieu }}</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Détails de la réclamation -->
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
      <h5 class="mb-0"><i class="fas fa-list-alt me-2"></i> Détails de la réclamation</h5>
      <a href="{{ url_for('ajouter_detail_reclamation', id=reclamation.id) }}" class="btn btn-sm btn-success">
        <i class="fas fa-plus me-1"></i> Ajouter un détail
      </a>
    </div>
    <div class="card-body">
      {% if details %}
      <div class="table-responsive">
        <table class="table table-striped table-hover">
          <thead>
            <tr>
              <th>ID</th>
              <th>Région</th>
              <th>Site</th>
              <th>Date réclamation</th>
              <th>Date intervention</th>
              <th>Technicien</th>
              <th>Situation</th>
              <th>État matériel</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for detail in details %}
            <tr>
              <td>{{ detail.id }}</td>
              <td>{{ detail.region }}</td>
              <td>{{ detail.nom_site }}</td>
              <td>{{ detail.date_reclamation }}</td>
              <td>{{ detail.date_intervention }}</td>
              <td>{{ detail.technicien }}</td>
              <td>
                {% if detail.situation == 'Réglé' %}
                <span class="badge bg-success">Réglé</span>
                {% elif detail.situation == 'Pas encore' %}
                <span class="badge bg-warning text-dark">En attente</span>
                {% elif detail.situation == 'Problème' %}
                <span class="badge bg-danger">Problème</span>
                {% else %}
                <span class="badge bg-secondary">{{ detail.situation }}</span>
                {% endif %}
              </td>
              <td>{{ detail.etat_materiel }}</td>
              <td>
                <div class="btn-group" role="group">
                  <button type="button" class="btn btn-xs btn-info" data-bs-toggle="modal" data-bs-target="#detailModal{{ detail.id }}" title="Voir les détails" style="padding: 2px 6px; font-size: 10px;">
                    <i class="fas fa-eye" style="font-size: 10px;"></i>
                  </button>
                  <a href="{{ url_for('modifier_detail_reclamation', id=detail.id) }}" class="btn btn-xs btn-warning" title="Modifier" style="padding: 2px 6px; font-size: 10px;">
                    <i class="fas fa-edit" style="font-size: 10px;"></i>
                  </a>
                  <button type="button" class="btn btn-xs btn-danger" onclick="confirmDelete('ce détail de réclamation', '{{ url_for('supprimer_detail_reclamation', id=detail.id) }}')" title="Supprimer" style="padding: 2px 6px; font-size: 10px;">
                    <i class="fas fa-trash" style="font-size: 10px;"></i>
                  </button>
                </div>
              </td>
            </tr>

            <!-- Modal pour afficher les détails complets -->
            <div class="modal fade" id="detailModal{{ detail.id }}" tabindex="-1" aria-labelledby="detailModalLabel{{ detail.id }}" aria-hidden="true">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="detailModalLabel{{ detail.id }}">Détail #{{ detail.id }} - {{ detail.nom_site }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <div class="row">
                      <div class="col-md-6">
                        <p><strong>Région:</strong> {{ detail.region }}</p>
                        <p><strong>Site:</strong> {{ detail.nom_site }}</p>
                        <p><strong>Nombre de systèmes:</strong> {{ detail.nbr_systeme }}</p>
                        <p><strong>Date de réclamation:</strong> {{ detail.date_reclamation }}</p>
                        <p><strong>Date d'intervention:</strong> {{ detail.date_intervention }}</p>
                        <p><strong>Technicien:</strong> {{ detail.technicien }}</p>
                      </div>
                      <div class="col-md-6">
                        <p><strong>Situation:</strong> {{ detail.situation }}</p>
                        <p><strong>État du matériel:</strong> {{ detail.etat_materiel }}</p>
                        <p><strong>Téléphone chef de site:</strong> {{ detail.telephone_chef_site }}</p>
                        <p><strong>Téléphone sécurité:</strong> {{ detail.telephone_securite }}</p>
                        <p><strong>Technicien à contacter:</strong> {{ detail.technicien_contact }}</p>
                        <p><strong>GPS:</strong> {{ detail.gps }}</p>
                      </div>
                    </div>
                    <div class="row mt-3">
                      <div class="col-12">
                        <h6>Observation:</h6>
                        <p class="border p-2 rounded bg-light">{{ detail.observation }}</p>
                      </div>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <a href="{{ url_for('modifier_detail_reclamation', id=detail.id) }}" class="btn btn-warning">
                      <i class="fas fa-edit"></i> Modifier
                    </a>
                  </div>
                </div>
              </div>
            </div>
            {% endfor %}
          </tbody>
        </table>
      </div>
      {% else %}
      <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> Aucun détail n'a encore été ajouté à cette réclamation.
        <a href="{{ url_for('ajouter_detail_reclamation', id=reclamation.id) }}" class="alert-link">Ajouter un détail maintenant</a>.
      </div>
      {% endif %}
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Fonction de confirmation de suppression
    function confirmDelete(itemName, deleteUrl) {
        if (confirm('Êtes-vous sûr de vouloir supprimer ' + itemName + ' ?')) {
            window.location.href = deleteUrl;
        }
    }
</script>
{% endblock %}
