{% extends "base.html" %}

{% block title %}Détails de la Réclamation - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Détails de la Réclamation{% endblock %}

{% block extra_head %}
<!-- مكتبة XLSX لتصدير Excel -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
{% endblock %}

{% block content %}
<div class="container mt-4 mb-5">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="text-primary"><i class="fas fa-exclamation-circle me-2"></i> Détails de la réclamation #{{ reclamation.id }}</h4>
    <div>
      <a href="{{ url_for('reclamations') }}" class="btn btn-outline-secondary me-2">
        <i class="fas fa-arrow-left me-1"></i> Retour à la liste
      </a>
    </div>
  </div>

  <!-- Informations générales -->
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
      <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> Informations générales</h5>
      <a href="{{ url_for('modifier_reclamation', id=reclamation.id) }}" class="btn btn-sm btn-warning">
        <i class="fas fa-edit me-1"></i> Modifier
      </a>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-6">
          <table class="table table-borderless">
            <tr>
              <th style="width: 40%">N° de marché:</th>
              <td><strong>{{ reclamation.numero_marche }}</strong></td>
            </tr>
            <tr>
              <th>Client:</th>
              <td>{{ reclamation.client }}</td>
            </tr>
            <tr>
              <th>Objet de marché:</th>
              <td>{{ reclamation.objet_marche }}</td>
            </tr>
            <tr>
              <th>Délai d'exécution:</th>
              <td>{{ reclamation.delai_execution }}</td>
            </tr>
          </table>
        </div>
        <div class="col-md-6">
          <table class="table table-borderless">
            <tr>
              <th style="width: 40%">Domaine:</th>
              <td>
                {% if reclamation.domaine == 'SVS' %}
                <span class="badge bg-primary">SVS</span>
                {% elif reclamation.domaine == 'EXTINCTEUR' %}
                <span class="badge bg-danger">EXTINCTEUR</span>
                {% elif reclamation.domaine == 'SYSTEME D\'INCENDIE' %}
                <span class="badge bg-warning">SYSTEME D'INCENDIE</span>
                {% elif reclamation.domaine == 'SYSTEME D\'ALARME' %}
                <span class="badge bg-info">SYSTEME D'ALARME</span>
                {% elif reclamation.domaine == 'SYSTEME TELEPHONIQUE' %}
                <span class="badge bg-success">SYSTEME TELEPHONIQUE</span>
                {% elif reclamation.domaine == "L'AFFICHAGE DYNAMIQUE" %}
                <span class="badge bg-dark">L'AFFICHAGE DYNAMIQUE</span>
                {% else %}
                <span class="badge bg-secondary">{{ reclamation.domaine }}</span>
                {% endif %}
              </td>
            </tr>
            <tr>
              <th>Période d'interventions:</th>
              <td>{{ reclamation.periode_interventions }}</td>
            </tr>
            <tr>
              <th>Période du marché:</th>
              <td>{{ reclamation.periode_marche }}</td>
            </tr>
            <tr>
              <th>Lieu:</th>
              <td>{{ reclamation.lieu }}</td>
            </tr>
          </table>
        </div>
      </div>
    </div>
  </div>

  <!-- Détails de la réclamation -->
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-light d-flex justify-content-between align-items-center">
      <h5 class="mb-0"><i class="fas fa-list-alt me-2"></i> Détails de la réclamation</h5>
      <div class="btn-group" role="group">
        <a href="{{ url_for('ajouter_detail_reclamation', id=reclamation.id) }}" class="btn btn-sm btn-success">
          <i class="fas fa-plus me-1"></i> Ajouter un détail
        </a>
        <button type="button" class="btn btn-sm btn-primary" id="exportDetailsExcel">
          <i class="fas fa-file-excel"></i> Exporter Excel
        </button>
        <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#importDetailsModal">
          <i class="fas fa-file-import"></i> Importer Excel
        </button>
      </div>
    </div>
    <div class="card-body">
      {% if details %}
      <div class="table-responsive">
        <table class="table table-striped table-hover">
          <thead>
            <tr>
              <th>ID</th>
              <th>Région</th>
              <th>Site</th>
              <th>Date réclamation</th>
              <th>Date intervention</th>
              <th>Technicien</th>
              <th>Situation</th>
              <th>État matériel</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for detail in details %}
            <tr>
              <td>{{ detail.id }}</td>
              <td>{{ detail.region }}</td>
              <td>{{ detail.nom_site }}</td>
              <td>{{ detail.date_reclamation }}</td>
              <td>{{ detail.date_intervention }}</td>
              <td>{{ detail.technicien }}</td>
              <td>
                {% if detail.situation == 'Réglé' %}
                <span class="badge bg-success">Réglé</span>
                {% elif detail.situation == 'Pas encore' %}
                <span class="badge bg-warning text-dark">En attente</span>
                {% elif detail.situation == 'Problème' %}
                <span class="badge bg-danger">Problème</span>
                {% else %}
                <span class="badge bg-secondary">{{ detail.situation }}</span>
                {% endif %}
              </td>
              <td>{{ detail.etat_materiel }}</td>
              <td>
                <div class="btn-group" role="group">
                  <button type="button" class="btn btn-xs btn-info" data-bs-toggle="modal" data-bs-target="#detailModal{{ detail.id }}" title="Voir les détails" style="padding: 2px 6px; font-size: 10px;">
                    <i class="fas fa-eye" style="font-size: 10px;"></i>
                  </button>
                  <a href="{{ url_for('modifier_detail_reclamation', id=detail.id) }}" class="btn btn-xs btn-warning" title="Modifier" style="padding: 2px 6px; font-size: 10px;">
                    <i class="fas fa-edit" style="font-size: 10px;"></i>
                  </a>
                  <button type="button" class="btn btn-xs btn-danger" onclick="if(confirm('Êtes-vous sûr de vouloir supprimer ce détail de réclamation ?\n\nCette action est irréversible !')) { window.location.href='{{ url_for('supprimer_detail_reclamation', id=detail.id) }}'; }" title="Supprimer" style="padding: 2px 6px; font-size: 10px;">
                    <i class="fas fa-trash" style="font-size: 10px;"></i>
                  </button>
                </div>
              </td>
            </tr>

            <!-- Modal pour afficher les détails complets -->
            <div class="modal fade" id="detailModal{{ detail.id }}" tabindex="-1" aria-labelledby="detailModalLabel{{ detail.id }}" aria-hidden="true">
              <div class="modal-dialog modal-lg">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title" id="detailModalLabel{{ detail.id }}">Détail #{{ detail.id }} - {{ detail.nom_site }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <div class="row">
                      <div class="col-md-6">
                        <p><strong>Région:</strong> {{ detail.region }}</p>
                        <p><strong>Site:</strong> {{ detail.nom_site }}</p>
                        <p><strong>Nombre de systèmes:</strong> {{ detail.nbr_systeme }}</p>
                        <p><strong>Date de réclamation:</strong> {{ detail.date_reclamation }}</p>
                        <p><strong>Date d'intervention:</strong> {{ detail.date_intervention }}</p>
                        <p><strong>Technicien:</strong> {{ detail.technicien }}</p>
                      </div>
                      <div class="col-md-6">
                        <p><strong>Situation:</strong> {{ detail.situation }}</p>
                        <p><strong>État du matériel:</strong> {{ detail.etat_materiel }}</p>
                        <p><strong>Téléphone chef de site:</strong> {{ detail.telephone_chef_site }}</p>
                        <p><strong>Téléphone sécurité:</strong> {{ detail.telephone_securite }}</p>
                        <p><strong>Technicien à contacter:</strong> {{ detail.technicien_contact }}</p>
                        <p><strong>GPS:</strong> {{ detail.gps }}</p>
                      </div>
                    </div>
                    <div class="row mt-3">
                      <div class="col-12">
                        <h6>Observation:</h6>
                        <p class="border p-2 rounded bg-light">{{ detail.observation }}</p>
                      </div>
                    </div>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <a href="{{ url_for('modifier_detail_reclamation', id=detail.id) }}" class="btn btn-warning">
                      <i class="fas fa-edit"></i> Modifier
                    </a>
                  </div>
                </div>
              </div>
            </div>
            {% endfor %}
          </tbody>
        </table>
      </div>
      {% else %}
      <div class="alert alert-info">
        <i class="fas fa-info-circle me-2"></i> Aucun détail n'a encore été ajouté à cette réclamation.
        <a href="{{ url_for('ajouter_detail_reclamation', id=reclamation.id) }}" class="alert-link">Ajouter un détail maintenant</a>.
      </div>
      {% endif %}
    </div>
  </div>
</div>

<!-- Modal pour importer des détails -->
<div class="modal fade" id="importDetailsModal" tabindex="-1" aria-labelledby="importDetailsModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="importDetailsModalLabel">Importer des détails de réclamation</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="importDetailsForm" enctype="multipart/form-data">
          <div class="mb-3">
            <label for="detailsFile" class="form-label">Fichier Excel</label>
            <input type="file" class="form-control" id="detailsFile" accept=".xlsx,.xls" required>
            <div class="form-text">Formats acceptés: .xlsx, .xls</div>
          </div>
          <div class="mb-3">
            <div class="alert alert-info">
              <strong>Format attendu:</strong><br>
              Colonnes: Région, Site, Date réclamation, Date intervention, Technicien, Situation, État matériel, Observation
            </div>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
        <button type="button" class="btn btn-primary" id="importDetailsBtn">Importer</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Export Excel pour les détails
    document.getElementById('exportDetailsExcel').addEventListener('click', function() {
      exportDetailsToExcel();
    });

    // Import Excel pour les détails
    document.getElementById('importDetailsBtn').addEventListener('click', function() {
      importDetailsFromExcel();
    });

    // Fonction d'export Excel pour les détails
    function exportDetailsToExcel() {
      const table = document.querySelector('.table-striped');
      if (!table) {
        alert('Aucune donnée à exporter');
        return;
      }

      const visibleRows = Array.from(table.querySelectorAll('tbody tr'));
      if (visibleRows.length === 0) {
        alert('Aucune donnée à exporter');
        return;
      }

      // إنشاء workbook جديد
      const wb = XLSX.utils.book_new();

      // إنشاء البيانات للجدول
      const data = [];

      // إضافة رؤوس الأعمدة
      data.push(['ID', 'Région', 'Site', 'Date réclamation', 'Date intervention', 'Technicien', 'Situation', 'État matériel']);

      // إضافة البيانات
      visibleRows.forEach(row => {
        const cells = row.querySelectorAll('td');
        if (cells.length >= 8) {
          const rowData = [
            cells[0]?.textContent.trim() || '',
            cells[1]?.textContent.trim() || '',
            cells[2]?.textContent.trim() || '',
            cells[3]?.textContent.trim() || '',
            cells[4]?.textContent.trim() || '',
            cells[5]?.textContent.trim() || '',
            cells[6]?.textContent.trim() || '',
            cells[7]?.textContent.trim() || ''
          ];
          data.push(rowData);
        }
      });

      // إنشاء worksheet من البيانات
      const ws = XLSX.utils.aoa_to_sheet(data);

      // تحسين عرض الأعمدة
      const colWidths = [
        { wch: 10 }, // ID
        { wch: 20 }, // Région
        { wch: 25 }, // Site
        { wch: 18 }, // Date réclamation
        { wch: 18 }, // Date intervention
        { wch: 20 }, // Technicien
        { wch: 15 }, // Situation
        { wch: 20 }  // État matériel
      ];
      ws['!cols'] = colWidths;

      // إضافة worksheet إلى workbook
      XLSX.utils.book_append_sheet(wb, ws, 'Détails Réclamation');

      // تصدير الملف
      const currentDate = new Date().toISOString().split('T')[0];
      XLSX.writeFile(wb, `details_reclamation_${currentDate}.xlsx`);
    }

    // Fonction d'import Excel pour les détails
    function importDetailsFromExcel() {
      const fileInput = document.getElementById('detailsFile');
      const file = fileInput.files[0];

      if (!file) {
        alert('Veuillez sélectionner un fichier');
        return;
      }

      const reader = new FileReader();
      reader.onload = function(e) {
        try {
          const data = new Uint8Array(e.target.result);
          const workbook = XLSX.read(data, { type: 'array' });
          const firstSheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[firstSheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

          if (jsonData.length < 2) {
            alert('Le fichier doit contenir au moins une ligne de données');
            return;
          }

          // Traitement des données importées
          console.log('Données importées:', jsonData);
          alert('Import réussi! ' + (jsonData.length - 1) + ' lignes importées.');

          // Fermer le modal
          const modal = bootstrap.Modal.getInstance(document.getElementById('importDetailsModal'));
          modal.hide();

          // Recharger la page pour voir les nouvelles données
          location.reload();

        } catch (error) {
          console.error('Erreur lors de l\'import:', error);
          alert('Erreur lors de l\'import du fichier: ' + error.message);
        }
      };
      reader.readAsArrayBuffer(file);
    }
  });
</script>
{% endblock %}
