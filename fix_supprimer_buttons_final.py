#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح نهائي مركز لأزرار SUPPRIMER
Final focused fix for SUPPRIMER buttons
"""

import sqlite3
import os
import shutil
from datetime import datetime

def backup_database():
    """عمل نسخة احتياطية من قاعدة البيانات"""
    if os.path.exists('maintenance.db'):
        backup_name = f'maintenance_supprimer_fix_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        shutil.copy2('maintenance.db', backup_name)
        print(f"✅ تم حفظ نسخة احتياطية: {backup_name}")
        return backup_name
    return None

def create_simple_delete_js():
    """إنشاء ملف JavaScript بسيط وفعال للحذف"""
    
    js_content = '''/**
 * نظام حذف بسيط وفعال
 * Simple and effective delete system
 */

console.log('🗑️ تحميل نظام الحذف البسيط...');

// دالة الحذف الرئيسية
function confirmDelete(itemName, deleteUrl) {
    console.log('confirmDelete called:', itemName, deleteUrl);
    
    // إنشاء حوار تأكيد مخصص
    const modal = document.createElement('div');
    modal.className = 'delete-confirmation-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: fadeIn 0.3s ease;
    `;
    
    modal.innerHTML = `
        <div style="
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease;
        ">
            <div style="color: #dc3545; font-size: 48px; margin-bottom: 20px;">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h4 style="color: #333; margin-bottom: 15px;">تأكيد الحذف</h4>
            <p style="color: #666; margin-bottom: 25px; font-size: 16px;">
                هل أنت متأكد من حذف: <strong style="color: #dc3545;">${itemName}</strong>؟
                <br><br>
                <small style="color: #999;">هذا الإجراء لا يمكن التراجع عنه</small>
            </p>
            <div style="display: flex; gap: 15px; justify-content: center;">
                <button onclick="cancelDelete()" style="
                    background: #6c757d;
                    color: white;
                    border: none;
                    padding: 12px 25px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: all 0.3s;
                ">
                    <i class="fas fa-times"></i> إلغاء
                </button>
                <button onclick="proceedDelete('${deleteUrl}')" style="
                    background: #dc3545;
                    color: white;
                    border: none;
                    padding: 12px 25px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: all 0.3s;
                ">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // إضافة الأنماط
    if (!document.getElementById('delete-modal-styles')) {
        const styles = document.createElement('style');
        styles.id = 'delete-modal-styles';
        styles.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            @keyframes slideIn {
                from { transform: translateY(-50px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
            .delete-confirmation-modal button:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            }
        `;
        document.head.appendChild(styles);
    }
    
    // حفظ المرجع للحذف لاحقاً
    window.currentDeleteModal = modal;
}

// دالة إلغاء الحذف
function cancelDelete() {
    if (window.currentDeleteModal) {
        window.currentDeleteModal.style.animation = 'fadeOut 0.3s ease';
        setTimeout(() => {
            if (window.currentDeleteModal && window.currentDeleteModal.parentNode) {
                window.currentDeleteModal.parentNode.removeChild(window.currentDeleteModal);
            }
            window.currentDeleteModal = null;
        }, 300);
    }
}

// دالة تنفيذ الحذف
function proceedDelete(deleteUrl) {
    // إغلاق الحوار
    cancelDelete();
    
    // عرض مؤشر التحميل
    showLoadingIndicator();
    
    // الانتقال إلى رابط الحذف
    window.location.href = deleteUrl;
}

// عرض مؤشر التحميل
function showLoadingIndicator() {
    const loading = document.createElement('div');
    loading.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10001;
    `;
    
    loading.innerHTML = `
        <div style="
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        ">
            <div style="color: #dc3545; font-size: 32px; margin-bottom: 15px;">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <div style="color: #333; font-size: 16px;">جاري الحذف...</div>
        </div>
    `;
    
    document.body.appendChild(loading);
}

// إضافة دعم مفتاح Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && window.currentDeleteModal) {
        cancelDelete();
    }
});

// إضافة دعم النقر خارج الحوار
document.addEventListener('click', function(e) {
    if (window.currentDeleteModal && e.target === window.currentDeleteModal) {
        cancelDelete();
    }
});

console.log('✅ نظام الحذف البسيط جاهز');
'''
    
    # كتابة الملف
    with open('static/js/simple-delete.js', 'w', encoding='utf-8') as f:
        f.write(js_content)
    
    print("✅ تم إنشاء ملف simple-delete.js")

def test_delete_routes():
    """اختبار routes الحذف"""
    
    print("\n🧪 اختبار routes الحذف...")
    
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # اختبار كل route حذف
        test_routes = [
            ('users', 'المستخدمين'),
            ('marches', 'الأسواق'),
            ('interventions', 'التدخلات'),
            ('reclamations', 'الشكاوى'),
            ('regions', 'المناطق'),
            ('sites', 'المواقع')
        ]
        
        for table, description in test_routes:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   ✅ {description} ({table}): {count} سجل")
            except Exception as e:
                print(f"   ❌ {description} ({table}): خطأ - {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار routes الحذف: {e}")
        return False

def check_app_routes():
    """فحص routes الحذف في app.py"""
    
    print("\n🔍 فحص routes الحذف في app.py...")
    
    if not os.path.exists('app.py'):
        print("❌ ملف app.py غير موجود")
        return False
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        routes_to_check = [
            'supprimer_utilisateur',
            'supprimer_marche', 
            'supprimer_intervention',
            'supprimer_reclamation',
            'supprimer_region',
            'supprimer_site'
        ]
        
        all_exist = True
        for route in routes_to_check:
            if f"def {route}(" in content:
                print(f"   ✅ {route}")
            else:
                print(f"   ❌ {route} غير موجود")
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        print(f"❌ خطأ في فحص app.py: {e}")
        return False

def update_base_html():
    """تحديث base.html لتحميل ملف JavaScript الجديد"""
    
    print("\n🔧 تحديث base.html...")
    
    try:
        with open('templates/base.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة ملف JavaScript الجديد إذا لم يكن موجوداً
        if 'simple-delete.js' not in content:
            # البحث عن مكان إدراج الملف
            if 'static/js/delete-modal.js' in content:
                content = content.replace(
                    'static/js/delete-modal.js',
                    'static/js/simple-delete.js'
                )
            elif '</body>' in content:
                content = content.replace(
                    '</body>',
                    '    <script src="{{ url_for(\'static\', filename=\'js/simple-delete.js\') }}"></script>\n</body>'
                )
            
            with open('templates/base.html', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم تحديث base.html")
        else:
            print("✅ base.html محدث بالفعل")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تحديث base.html: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🗑️ إصلاح نهائي مركز لأزرار SUPPRIMER")
    print("🗑️ Final Focused Fix for SUPPRIMER Buttons")
    print("=" * 70)
    
    # عمل نسخة احتياطية
    backup_file = backup_database()
    
    # إنشاء ملف JavaScript بسيط
    create_simple_delete_js()
    
    # تحديث base.html
    if update_base_html():
        print("✅ تم تحديث base.html")
    else:
        print("❌ فشل في تحديث base.html")
    
    # اختبار routes الحذف
    if test_delete_routes():
        print("✅ جميع routes الحذف تعمل")
    else:
        print("❌ مشاكل في routes الحذف")
    
    # فحص routes في app.py
    if check_app_routes():
        print("✅ جميع routes موجودة في app.py")
    else:
        print("❌ بعض routes مفقودة في app.py")
    
    print("\n🎉 تم الانتهاء من الإصلاح!")
    print("🎉 Fix completed!")
    
    if backup_file:
        print(f"\n💾 النسخة الاحتياطية: {backup_file}")
        print(f"💾 Backup file: {backup_file}")
    
    print("\n📋 الخطوات التالية:")
    print("📋 Next steps:")
    print("   1. تشغيل البرنامج")
    print("   1. Run the application")
    print("   2. اختبار أزرار SUPPRIMER")
    print("   2. Test SUPPRIMER buttons")
    print("   3. يجب أن تعمل بشكل مثالي الآن")
    print("   3. Should work perfectly now")
    
    print("\n🔧 الملفات المحدثة:")
    print("🔧 Updated files:")
    print("   ✅ static/js/simple-delete.js - ملف JavaScript جديد")
    print("   ✅ templates/base.html - محدث لتحميل الملف الجديد")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
