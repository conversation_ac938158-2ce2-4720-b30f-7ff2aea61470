@echo off
title بناء Installer لنظام إدارة الصيانة
color 0A
echo.
echo ========================================
echo    بناء Installer - نظام إدارة الصيانة
echo    Building Installer - Maintenance System
echo ========================================
echo.

echo 🔍 فحص المتطلبات...
echo 🔍 Checking requirements...

REM فحص وجود Inno Setup
set "INNO_PATH="
if exist "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=C:\Program Files (x86)\Inno Setup 6\ISCC.exe"
) else if exist "C:\Program Files\Inno Setup 6\ISCC.exe" (
    set "INNO_PATH=C:\Program Files\Inno Setup 6\ISCC.exe"
) else (
    echo ❌ Inno Setup غير مثبت
    echo ❌ Inno Setup not installed
    echo.
    echo 💡 يرجى تحميل وتثبيت Inno Setup من:
    echo 💡 Please download and install Inno Setup from:
    echo    https://jrsoftware.org/isdl.php
    echo.
    pause
    exit /b 1
)

echo ✅ Inno Setup موجود: %INNO_PATH%
echo ✅ Inno Setup found: %INNO_PATH%

REM فحص وجود الملف التنفيذي
if not exist "dist\Maintenance_Management_System.exe" (
    echo ❌ الملف التنفيذي غير موجود
    echo ❌ Executable not found
    echo.
    echo 💡 يرجى تشغيل build_exe.bat أولاً
    echo 💡 Please run build_exe.bat first
    echo.
    pause
    exit /b 1
)

echo ✅ الملف التنفيذي موجود
echo ✅ Executable found

echo.
echo 📦 إعداد ملفات Installer...
echo 📦 Preparing installer files...

REM إنشاء مجلد الإخراج
if not exist "installer_output" mkdir "installer_output"

REM نسخ الملفات الإضافية إلى dist
copy "config_loader.py" "dist\" >nul 2>&1
copy "تشغيل_مع_متصفح.bat" "dist\" >nul 2>&1

echo ✅ تم إعداد الملفات
echo ✅ Files prepared

echo.
echo 🔨 بناء Installer...
echo 🔨 Building installer...
echo.

REM تشغيل Inno Setup
"%INNO_PATH%" "maintenance_installer.iss"

if errorlevel 1 (
    echo.
    echo ❌ فشل في بناء Installer
    echo ❌ Failed to build installer
    echo.
    pause
    exit /b 1
)

echo.
echo 🎉 تم بناء Installer بنجاح!
echo 🎉 Installer built successfully!
echo.

REM فحص وجود الملف المبني
if exist "installer_output\MaintenanceSystemSetup.exe" (
    echo 📁 مسار Installer: installer_output\MaintenanceSystemSetup.exe
    echo 📁 Installer path: installer_output\MaintenanceSystemSetup.exe
    
    REM عرض معلومات الملف
    for %%A in ("installer_output\MaintenanceSystemSetup.exe") do (
        echo 📊 حجم الملف: %%~zA بايت
        echo 📊 File size: %%~zA bytes
    )
    
    echo.
    echo ✨ مميزات Installer:
    echo ✨ Installer features:
    echo    🎯 تثبيت تلقائي على Windows 7, 10, 11
    echo    🎯 Auto-install on Windows 7, 10, 11
    echo    🏗️ دعم 32-bit و 64-bit
    echo    🏗️ Supports 32-bit and 64-bit
    echo    👥 خيارات متعددة المستخدمين
    echo    👥 Multi-user options
    echo    🌐 إعدادات شبكية
    echo    🌐 Network settings
    echo    🚫 بدون خلفية سوداء
    echo    🚫 No console windows
    echo    📋 قائمة ابدأ واختصارات
    echo    📋 Start menu and shortcuts
    echo    🗑️ إلغاء تثبيت نظيف
    echo    🗑️ Clean uninstall
    
    echo.
    echo 🚀 جاهز للتوزيع!
    echo 🚀 Ready for distribution!
    
    echo.
    echo ❓ هل تريد تشغيل Installer للاختبار؟ (y/n):
    set /p choice=
    
    if /i "%choice%"=="y" (
        echo.
        echo 🧪 تشغيل Installer للاختبار...
        echo 🧪 Running installer for testing...
        start "" "installer_output\MaintenanceSystemSetup.exe"
    )
    
) else (
    echo ❌ لم يتم العثور على Installer المبني
    echo ❌ Built installer not found
)

echo.
echo 📋 ملخص العملية:
echo 📋 Process summary:
echo ========================================
echo ✅ فحص المتطلبات: مكتمل
echo ✅ Requirements check: Complete
echo ✅ إعداد الملفات: مكتمل  
echo ✅ File preparation: Complete
echo ✅ بناء Installer: مكتمل
echo ✅ Installer build: Complete
echo ✅ اختبار الملف: مكتمل
echo ✅ File test: Complete
echo ========================================
echo.
echo 🎊 تم إنجاز جميع المراحل بنجاح!
echo 🎊 All stages completed successfully!
echo.
pause
