#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محمل إعدادات التطبيق
"""

import os
import sys
import configparser
from pathlib import Path

class ConfigLoader:
    """فئة تحميل الإعدادات"""

    def __init__(self):
        self.config = configparser.ConfigParser()
        self.config_file = self._get_config_file()
        self.load_config()

    def _get_config_file(self):
        """الحصول على مسار ملف الإعدادات"""
        if getattr(sys, 'frozen', False):
            # Running as executable
            app_dir = os.path.dirname(sys.executable)
        else:
            # Running as script
            app_dir = os.path.dirname(os.path.abspath(__file__))

        return os.path.join(app_dir, 'config.ini')

    def load_config(self):
        """تحميل الإعدادات"""
        if os.path.exists(self.config_file):
            try:
                self.config.read(self.config_file, encoding='utf-8')
            except Exception as e:
                print(f"خطأ في تحميل الإعدادات: {e}")
                self._create_default_config()
        else:
            self._create_default_config()

    def _create_default_config(self):
        """إنشاء إعدادات افتراضية"""
        self.config['Settings'] = {
            'InstallationType': '1',  # افتراضي: شبكي
            'Port': '5000',
            'MultiUser': 'True',      # افتراضي: متعدد المستخدمين
            'ServerMode': 'True',     # افتراضي: وضع الخادم
            'Version': '1.0.0'
        }

        self.config['Network'] = {
            'Host': '0.0.0.0',        # افتراضي: قبول جميع الاتصالات
            'AllowExternalAccess': 'True'  # افتراضي: السماح بالوصول الخارجي
        }

        self.config['Database'] = {
            'Path': 'maintenance.db',
            'BackupEnabled': 'True',
            'BackupInterval': '24'
        }

        self.save_config()

    def save_config(self):
        """حفظ الإعدادات"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                self.config.write(f)
        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {e}")

    def get_port(self):
        """الحصول على المنفذ"""
        try:
            port = self.config.getint('Settings', 'Port', fallback=5000)
            return port if 1024 <= port <= 65535 else 5000
        except:
            return 5000

    def get_host(self):
        """الحصول على المضيف"""
        if self.is_multi_user():
            return '0.0.0.0'  # للسماح بالوصول من أجهزة أخرى
        else:
            return '127.0.0.1'  # محلي فقط

    def is_multi_user(self):
        """فحص إذا كان النظام متعدد المستخدمين"""
        return self.config.getboolean('Settings', 'MultiUser', fallback=True)  # افتراضي: True

    def is_server_mode(self):
        """فحص إذا كان في وضع الخادم"""
        return self.config.getboolean('Settings', 'ServerMode', fallback=True)  # افتراضي: True

    def get_installation_type(self):
        """الحصول على نوع التثبيت"""
        return self.config.getint('Settings', 'InstallationType', fallback=1)  # افتراضي: شبكي

    def get_database_path(self):
        """الحصول على مسار قاعدة البيانات"""
        if getattr(sys, 'frozen', False):
            app_dir = os.path.dirname(sys.executable)
        else:
            app_dir = os.path.dirname(os.path.abspath(__file__))

        db_name = self.config.get('Database', 'Path', fallback='maintenance.db')
        return os.path.join(app_dir, db_name)

    def update_setting(self, section, key, value):
        """تحديث إعداد"""
        if section not in self.config:
            self.config[section] = {}

        self.config[section][key] = str(value)
        self.save_config()

    def get_app_info(self):
        """الحصول على معلومات التطبيق"""
        return {
            'version': self.config.get('Settings', 'Version', fallback='1.0.0'),
            'install_date': self.config.get('Settings', 'InstallDate', fallback='غير محدد'),
            'installation_type': self.get_installation_type(),
            'multi_user': self.is_multi_user(),
            'server_mode': self.is_server_mode(),
            'port': self.get_port(),
            'host': self.get_host()
        }

# إنشاء مثيل عام
config_loader = ConfigLoader()
