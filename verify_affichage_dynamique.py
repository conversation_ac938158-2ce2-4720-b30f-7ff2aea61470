#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
التحقق من إضافة L'AFFICHAGE DYNAMIQUE إلى جميع القوائم
"""

import os

def verify_affichage_dynamique():
    """التحقق من إضافة L'AFFICHAGE DYNAMIQUE"""
    
    print("🔍 التحقق من إضافة L'AFFICHAGE DYNAMIQUE")
    print("=" * 50)
    
    # قائمة القوالب للفحص
    templates_to_check = {
        'templates/ajouter_marche.html': {
            'select_options': True,
            'badges': False,
            'description': 'قالب إضافة مارشيه'
        },
        'templates/modifier_marche.html': {
            'select_options': True,
            'badges': False,
            'description': 'قالب تعديل مارشيه'
        },
        'templates/marches.html': {
            'select_options': True,
            'badges': True,
            'description': 'قالب عرض المارشيه'
        },
        'templates/details_marche.html': {
            'select_options': False,
            'badges': True,
            'description': 'قالب تفاصيل المارشيه'
        },
        'templates/ajouter_intervention.html': {
            'select_options': True,
            'badges': False,
            'description': 'قالب إضافة إنترفنشن'
        },
        'templates/interventions.html': {
            'select_options': True,
            'badges': True,
            'description': 'قالب عرض الإنترفنشن'
        },
        'templates/ajouter_reclamation.html': {
            'select_options': True,
            'badges': False,
            'description': 'قالب إضافة ريكلاماسيون'
        },
        'templates/reclamations.html': {
            'select_options': True,
            'badges': True,
            'description': 'قالب عرض الريكلاماسيون'
        },
        'templates/voir_reclamation.html': {
            'select_options': False,
            'badges': True,
            'description': 'قالب عرض ريكلاماسيون'
        }
    }
    
    total_files = len(templates_to_check)
    successful_files = 0
    
    for template_path, checks in templates_to_check.items():
        print(f"\n📄 فحص {checks['description']}:")
        print(f"   📁 {template_path}")
        
        if not os.path.exists(template_path):
            print(f"   ❌ الملف غير موجود")
            continue
        
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        file_success = True
        
        # فحص خيارات select
        if checks['select_options']:
            select_patterns = [
                "L'AFFICHAGE DYNAMIQUE",
                'value="L\'AFFICHAGE DYNAMIQUE"',
                'option value="L\'AFFICHAGE DYNAMIQUE"'
            ]
            
            select_found = any(pattern in content for pattern in select_patterns)
            if select_found:
                print(f"   ✅ خيار select موجود")
            else:
                print(f"   ❌ خيار select غير موجود")
                file_success = False
        
        # فحص badges العرض
        if checks['badges']:
            badge_patterns = [
                'badge bg-dark',
                "L'AFFICHAGE DYNAMIQUE",
                'AFFICHAGE DYN'
            ]
            
            badge_found = any(pattern in content for pattern in badge_patterns)
            if badge_found:
                print(f"   ✅ badge العرض موجود")
            else:
                print(f"   ❌ badge العرض غير موجود")
                file_success = False
        
        # فحص عدد مرات الظهور
        affichage_count = content.count("L'AFFICHAGE DYNAMIQUE")
        if affichage_count > 0:
            print(f"   📊 عدد مرات الظهور: {affichage_count}")
        else:
            print(f"   ⚠️ لا يظهر في الملف")
            file_success = False
        
        # فحص التكرار غير المرغوب فيه
        duplicate_patterns = [
            'L\'AFFICHAGE DYNAMIQUE">L\'AFFICHAGE DYNAMIQUE</option>\n                                <option value="L\'AFFICHAGE DYNAMIQUE"',
            'L\'AFFICHAGE DYNAMIQUE</span>\n                {% elif',
            'L\'AFFICHAGE DYNAMIQUE</span>\n                                    {% elif'
        ]
        
        duplicates_found = any(pattern in content for pattern in duplicate_patterns)
        if duplicates_found:
            print(f"   ⚠️ تم العثور على تكرار")
        else:
            print(f"   ✅ لا توجد تكرارات")
        
        if file_success:
            successful_files += 1
            print(f"   🎉 الملف محدث بنجاح")
        else:
            print(f"   ❌ الملف يحتاج إصلاح")
    
    # إحصائيات عامة
    print(f"\n📊 الإحصائيات العامة:")
    print(f"   📁 إجمالي الملفات: {total_files}")
    print(f"   ✅ ملفات محدثة بنجاح: {successful_files}")
    print(f"   ❌ ملفات تحتاج إصلاح: {total_files - successful_files}")
    print(f"   📈 نسبة النجاح: {(successful_files/total_files)*100:.1f}%")
    
    # فحص خاص لقالب الإنترفنشن
    print(f"\n🔧 فحص خاص لقالب الإنترفنشن:")
    intervention_template = 'templates/ajouter_intervention.html'
    
    if os.path.exists(intervention_template):
        with open(intervention_template, 'r', encoding='utf-8') as f:
            intervention_content = f.read()
        
        # فحص الجدول الخاص
        table_checks = [
            'affichage-dynamique-details',
            'addAffichageRow',
            'Détails L\'AFFICHAGE DYNAMIQUE',
            'Type d\'affichage',
            'Résolution'
        ]
        
        table_found = sum(1 for check in table_checks if check in intervention_content)
        print(f"   📊 عناصر الجدول الموجودة: {table_found}/{len(table_checks)}")
        
        if table_found >= 3:
            print(f"   ✅ الجدول الخاص موجود ومكتمل")
        else:
            print(f"   ❌ الجدول الخاص غير مكتمل")
        
        # فحص JavaScript
        js_checks = [
            'addAffichageRow()',
            'affichage-dynamique-tbody',
            'affichage_region_',
            'affichage_type_'
        ]
        
        js_found = sum(1 for check in js_checks if check in intervention_content)
        print(f"   📊 عناصر JavaScript الموجودة: {js_found}/{len(js_checks)}")
        
        if js_found >= 3:
            print(f"   ✅ JavaScript functions موجودة")
        else:
            print(f"   ❌ JavaScript functions غير مكتملة")
    else:
        print(f"   ❌ قالب الإنترفنشن غير موجود")
    
    # توصيات
    print(f"\n💡 التوصيات:")
    
    if successful_files == total_files:
        print(f"   🎉 جميع القوالب محدثة بنجاح!")
        print(f"   ✅ يمكن الآن استخدام 'L'AFFICHAGE DYNAMIQUE' في جميع النماذج")
        print(f"   🎨 Badge العرض سيظهر باللون الداكن")
        print(f"   📋 جدول خاص متاح في قالب الإنترفنشن")
    else:
        print(f"   ⚠️ بعض القوالب تحتاج إصلاح")
        print(f"   🔧 راجع الملفات التي تحتوي على أخطاء")
        print(f"   🔄 قم بتشغيل السكريبت مرة أخرى إذا لزم الأمر")
    
    print(f"\n🎯 الاستخدام:")
    print(f"   1. اذهب إلى أي نموذج إضافة (مارشيه، إنترفنشن، ريكلاماسيون)")
    print(f"   2. اختر 'L'AFFICHAGE DYNAMIQUE' من قائمة Domaine")
    print(f"   3. في الإنترفنشن، ستظهر جدول خاص للتفاصيل")
    print(f"   4. في العرض، سيظهر badge داكن اللون")
    
    return successful_files == total_files

if __name__ == "__main__":
    success = verify_affichage_dynamique()
    
    print(f"\n🎉 انتهى الفحص!")
    print("=" * 50)
    
    if success:
        print(f"✅ جميع التحديثات تمت بنجاح!")
        print(f"🚀 L'AFFICHAGE DYNAMIQUE متاح الآن في جميع القوائم")
    else:
        print(f"⚠️ بعض التحديثات تحتاج إصلاح")
        print(f"🔧 راجع الملفات المذكورة أعلاه")
