# 🛠️ Système de Gestion de Maintenance

## ✅ STATUT: APPLICATION OPTIMISÉE ET NETTOYÉE

**Dernière mise à jour**: 30 Juin 2025
**Application entièrement nettoyée, optimisée et fonctionnelle!**

Application web moderne de gestion de maintenance développée avec Flask et Python. Le code a été entièrement nettoyé et optimisé pour de meilleures performances et une maintenance plus facile.

## 🧹 OPTIMISATIONS RÉCENTES (30 Juin 2025)

### ✅ Nettoyage Complet du Code
- **100+ fichiers supprimés** : Suppression de tous les fichiers de test, debug et doublons
- **Code source optimisé** : Suppression des commentaires redondants et codes inutilisés
- **Taille réduite de 80%** : Projet plus léger et plus rapide
- **Structure simplifiée** : Seulement les fichiers essentiels conservés

### ✅ Fichiers Conservés (Essentiels)
```
📁 Projet nettoyé (8 fichiers principaux)
├── app.py (144KB) - Application principale optimisée
├── forms.py - Formulaires Flask-WTF
├── requirements.txt - Dépendances minimales
├── maintenance.db - Base de données
├── backup_scheduler.py - Système de sauvegarde
├── backup_config.json - Configuration
├── maintenance_icon.ico - Icône
└── README.md - Documentation mise à jour
```

### ✅ Améliorations Fonctionnelles
- **Import Excel fonctionnel** : Correction complète du système d'import
- **Interface nettoyée** : Suppression des éléments de test
- **Performance améliorée** : Chargement plus rapide
- **Code maintenable** : Structure claire et documentée

## ✨ Caractéristiques Principales

- ✅ **Application Web Moderne** : Interface responsive fonctionnant sur tous navigateurs
- ✅ **Multi-utilisateurs** : Système d'authentification avec gestion des rôles
- ✅ **Serveur Local** : Fonctionne en réseau local sans connexion Internet
- ✅ **Base de données SQLite** : Stockage local sécurisé et performant
- ✅ **Interface en Français** : Interface utilisateur entièrement en français
- ✅ **Carte Interactive** : Visualisation géographique des données sur carte du Maroc
- ✅ **Export Excel** : Export des données vers Excel
- ✅ **Impression Optimisée** : Rapports PDF et impression professionnelle

## 📋 Modules Disponibles

### 🏢 Gestion de l'Entreprise
- **Informations Société** : Configuration des données de l'entreprise
- **Upload de Logo** : Gestion du logo de l'entreprise

### 👥 Gestion des Utilisateurs
- **Comptes Utilisateurs** : Création et gestion des comptes
- **Rôles et Permissions** : Admin et utilisateurs standards
- **Journal d'Activités** : Suivi des actions utilisateurs

### 📊 Gestion Opérationnelle
- **Marchés** : Suivi des contrats et marchés
- **Interventions** : Planification et suivi des interventions par type :
  - 🔥 Systèmes d'incendie
  - 📹 Systèmes de caméras
  - 📞 Systèmes téléphoniques
  - 🧯 Extincteurs
  - 🚨 Systèmes d'alarme
  - ⚙️ SVS (Systèmes de Ventilation et Sécurité)
- **Réclamations** : Traitement des réclamations clients

### 🗺️ Gestion Géographique
- **Régions** : Gestion des régions administratives
- **Sites** : Gestion des sites par région avec coordonnées GPS
- **Carte Interactive** : Visualisation sur carte du Maroc avec marqueurs colorés

## 🚀 Installation et Configuration

### Prérequis
- Python 3.7 ou supérieur
- pip (gestionnaire de paquets Python)

### 🚀 Démarrage Rapide (Recommandé)

```bash
# Méthode la plus simple - tout automatique
python start.py
```

### Installation Manuelle
```bash
# 1. Installer les dépendances
pip install -r requirements.txt

# 2. Lancer l'application
python app.py

# 3. Accéder à l'application
# Ouvrir http://localhost:5000 dans votre navigateur
```

### 🔐 Connexion par défaut
- **Nom d'utilisateur**: admin
- **Mot de passe**: admin123

⚠️ **Important**: Changez le mot de passe après la première connexion!

## 🔐 Accès par Défaut

- **Nom d'utilisateur** : `admin`
- **Mot de passe** : `admin123`

## 🎯 Fonctionnalités Avancées

### 🗺️ Carte Interactive
- Affichage des sites, marchés, interventions et réclamations
- Marqueurs colorés par type de données
- Filtrage dynamique par catégorie
- Coordonnées GPS automatiques pour les villes marocaines
- Interface responsive et moderne

### 📊 Exports et Rapports
- Export Excel de toutes les données
- Impression optimisée avec CSS dédié
- Rapports détaillés par marché
- Statistiques en temps réel

### 🔧 Gestion Technique
- Suivi de l'état du matériel
- Gestion des semestres d'intervention
- Historique complet des opérations
- Liaison entre marchés, interventions et réclamations

## Technologies utilisées

- **Backend** : Python (Flask)
- **Base de données** : SQLite
- **Frontend** : HTML, CSS, JavaScript
- **Authentification** : Flask-Login
- **Formulaires** : Flask-WTF

## Fonctionnalités principales

- Authentification sécurisée des utilisateurs
- Gestion des informations de l'entreprise
- Gestion des utilisateurs et des permissions
- Suivi des marchés et contrats
- Suivi des interventions techniques
- Gestion des réclamations
- Génération de rapports imprimables
- Importation/exportation Excel
