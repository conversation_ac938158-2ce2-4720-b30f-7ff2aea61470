# 🛠️ Système de Gestion de Maintenance

Application web moderne de gestion de maintenance développée avec Flask et Python, offrant une interface intuitive et des fonctionnalités avancées pour la gestion complète des opérations de maintenance.

## ✨ Caractéristiques Principales

- ✅ **Application Web Moderne** : Interface responsive fonctionnant sur tous navigateurs
- ✅ **Multi-utilisateurs** : Système d'authentification avec gestion des rôles
- ✅ **Serveur Local** : Fonctionne en réseau local sans connexion Internet
- ✅ **Base de données SQLite** : Stockage local sécurisé et performant
- ✅ **Interface en Français** : Interface utilisateur entièrement en français
- ✅ **Carte Interactive** : Visualisation géographique des données sur carte du Maroc
- ✅ **Export Excel** : Export des données vers Excel
- ✅ **Impression Optimisée** : Rapports PDF et impression professionnelle

## 📋 Modules Disponibles

### 🏢 Gestion de l'Entreprise
- **Informations Société** : Configuration des données de l'entreprise
- **Upload de Logo** : Gestion du logo de l'entreprise

### 👥 Gestion des Utilisateurs
- **Comptes Utilisateurs** : Création et gestion des comptes
- **Rôles et Permissions** : Admin et utilisateurs standards
- **Journal d'Activités** : Suivi des actions utilisateurs

### 📊 Gestion Opérationnelle
- **Marchés** : Suivi des contrats et marchés
- **Interventions** : Planification et suivi des interventions par type :
  - 🔥 Systèmes d'incendie
  - 📹 Systèmes de caméras
  - 📞 Systèmes téléphoniques
  - 🧯 Extincteurs
  - 🚨 Systèmes d'alarme
  - ⚙️ SVS (Systèmes de Ventilation et Sécurité)
- **Réclamations** : Traitement des réclamations clients

### 🗺️ Gestion Géographique
- **Régions** : Gestion des régions administratives
- **Sites** : Gestion des sites par région avec coordonnées GPS
- **Carte Interactive** : Visualisation sur carte du Maroc avec marqueurs colorés

## 🚀 Installation et Configuration

### Prérequis
- Python 3.7 ou supérieur
- pip (gestionnaire de paquets Python)

### Installation
```bash
# 1. Cloner le repository
git clone [url-du-repository]
cd "App Gestion De Maintenance"

# 2. Installer les dépendances
pip install -r requirements.txt

# 3. Lancer l'application
python app.py

# 4. Accéder à l'application
# Ouvrir http://localhost:5000 dans votre navigateur
```

## 🔐 Accès par Défaut

- **Nom d'utilisateur** : `admin`
- **Mot de passe** : `admin123`

## 🎯 Fonctionnalités Avancées

### 🗺️ Carte Interactive
- Affichage des sites, marchés, interventions et réclamations
- Marqueurs colorés par type de données
- Filtrage dynamique par catégorie
- Coordonnées GPS automatiques pour les villes marocaines
- Interface responsive et moderne

### 📊 Exports et Rapports
- Export Excel de toutes les données
- Impression optimisée avec CSS dédié
- Rapports détaillés par marché
- Statistiques en temps réel

### 🔧 Gestion Technique
- Suivi de l'état du matériel
- Gestion des semestres d'intervention
- Historique complet des opérations
- Liaison entre marchés, interventions et réclamations

## Technologies utilisées

- **Backend** : Python (Flask)
- **Base de données** : SQLite
- **Frontend** : HTML, CSS, JavaScript
- **Authentification** : Flask-Login
- **Formulaires** : Flask-WTF

## Fonctionnalités principales

- Authentification sécurisée des utilisateurs
- Gestion des informations de l'entreprise
- Gestion des utilisateurs et des permissions
- Suivi des marchés et contrats
- Suivi des interventions techniques
- Gestion des réclamations
- Génération de rapports imprimables
- Importation/exportation Excel
