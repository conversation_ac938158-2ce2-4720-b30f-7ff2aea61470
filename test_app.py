#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🔍 اختبار البرنامج...")

try:
    import flask
    print("✅ Flask متوفر")
except ImportError as e:
    print(f"❌ Flask غير متوفر: {e}")

try:
    import sqlite3
    print("✅ SQLite متوفر")
except ImportError as e:
    print(f"❌ SQLite غير متوفر: {e}")

try:
    import pandas
    print("✅ Pandas متوفر")
except ImportError as e:
    print(f"❌ Pandas غير متوفر: {e}")

try:
    from werkzeug.security import generate_password_hash
    print("✅ Werkzeug.security متوفر")
except ImportError as e:
    print(f"❌ Werkzeug.security غير متوفر: {e}")

print("\n🔍 اختبار قاعدة البيانات...")
try:
    import sqlite3
    conn = sqlite3.connect('maintenance.db')
    cursor = conn.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
    tables = cursor.fetchall()
    print(f"✅ قاعدة البيانات تحتوي على {len(tables)} جدول")
    conn.close()
except Exception as e:
    print(f"❌ مشكلة في قاعدة البيانات: {e}")

print("\n🔍 اختبار استيراد app.py...")
try:
    import app
    print("✅ app.py تم استيراده بنجاح")
except Exception as e:
    print(f"❌ مشكلة في app.py: {e}")

print("\n🎯 انتهى الاختبار!")
