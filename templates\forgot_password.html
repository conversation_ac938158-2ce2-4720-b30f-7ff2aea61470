{% extends "base.html" %}

{% block title %}Mot de passe oublié - Système de Gestion de Maintenance{% endblock %}

{% block auth_content %}
<style>
    body {
        background: linear-gradient(135deg, #3498db, #8e44ad);
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        position: relative;
    }

    body::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url("{{ url_for('static', filename='images/pattern.png') }}");
        opacity: 0.05;
        z-index: -1;
    }

    .forgot-container {
        width: 100%;
        max-width: 450px;
        padding: 20px;
        animation: fadeIn 0.8s ease-in-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .forgot-box {
        background-color: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 40px 30px;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .forgot-box::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, #3498db, #8e44ad);
    }

    .forgot-icon {
        font-size: 60px;
        color: #3498db;
        margin-bottom: 20px;
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.1); }
        100% { transform: scale(1); }
    }

    .forgot-title {
        color: #2c3e50;
        font-size: 24px;
        margin-bottom: 10px;
        font-weight: 600;
    }

    .forgot-subtitle {
        color: #7f8c8d;
        font-size: 14px;
        margin-bottom: 30px;
        line-height: 1.5;
    }

    .input-group {
        position: relative;
        margin-bottom: 25px;
        text-align: left;
    }

    .input-group label {
        display: block;
        margin-bottom: 8px;
        color: #2c3e50;
        font-weight: 500;
        font-size: 14px;
    }

    .input-group input {
        width: 100%;
        padding: 12px 15px 12px 45px;
        border: 2px solid #ecf0f1;
        border-radius: 30px;
        background-color: #f8f9fa;
        font-size: 15px;
        color: #333;
        transition: all 0.3s;
        box-sizing: border-box;
    }

    .input-group input:focus {
        outline: none;
        border-color: #3498db;
        background-color: #fff;
        box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.1);
    }

    .input-group i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #7f8c8d;
        font-size: 18px;
        margin-top: 12px;
    }

    .reset-btn {
        width: 100%;
        padding: 14px;
        border: none;
        border-radius: 30px;
        background: linear-gradient(90deg, #3498db, #8e44ad);
        color: white;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        margin-top: 10px;
    }

    .reset-btn:hover {
        background: linear-gradient(90deg, #2980b9, #8e44ad);
        box-shadow: 0 7px 20px rgba(52, 152, 219, 0.6);
        transform: translateY(-2px);
    }

    .reset-btn:active {
        transform: translateY(0);
        box-shadow: 0 3px 10px rgba(52, 152, 219, 0.4);
    }

    .back-link {
        margin-top: 25px;
        text-align: center;
    }

    .back-link a {
        color: #7f8c8d;
        text-decoration: none;
        font-size: 14px;
        transition: color 0.3s;
        display: inline-flex;
        align-items: center;
        gap: 8px;
    }

    .back-link a:hover {
        color: #3498db;
    }

    .alert {
        padding: 12px 15px;
        margin-bottom: 20px;
        border-radius: 8px;
        font-size: 14px;
        text-align: left;
    }

    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border-left: 4px solid #28a745;
    }

    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    .alert-info {
        background-color: #d1ecf1;
        color: #0c5460;
        border-left: 4px solid #17a2b8;
    }

    .info-box {
        background: rgba(52, 152, 219, 0.1);
        border: 1px solid rgba(52, 152, 219, 0.2);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        text-align: left;
    }

    .info-box h5 {
        color: #2c3e50;
        margin-bottom: 10px;
        font-size: 16px;
        font-weight: 600;
    }

    .info-box ul {
        margin: 0;
        padding-left: 20px;
        color: #555;
    }

    .info-box li {
        margin-bottom: 5px;
        font-size: 13px;
    }
</style>

<div class="forgot-container">
    <div class="forgot-box">
        <div class="forgot-icon">
            <i class="fas fa-key"></i>
        </div>
        
        <h1 class="forgot-title">Mot de passe oublié?</h1>
        <p class="forgot-subtitle">
            Entrez votre adresse email et nous générerons un nouveau mot de passe temporaire pour vous.
        </p>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <div class="info-box">
            <h5><i class="fas fa-info-circle"></i> Instructions:</h5>
            <ul>
                <li>Entrez l'adresse email associée à votre compte</li>
                <li>Un nouveau mot de passe temporaire sera généré</li>
                <li>Connectez-vous avec ce mot de passe temporaire</li>
                <li>Changez immédiatement votre mot de passe</li>
            </ul>
        </div>

        <form method="POST" action="{{ url_for('forgot_password') }}">
            <div class="input-group">
                <label for="email">Adresse email:</label>
                <i class="fas fa-envelope"></i>
                <input type="email" id="email" name="email" placeholder="<EMAIL>" required autofocus>
            </div>

            <button type="submit" class="reset-btn">
                <i class="fas fa-paper-plane"></i> Générer nouveau mot de passe
            </button>
        </form>

        <div class="back-link">
            <a href="{{ url_for('login') }}">
                <i class="fas fa-arrow-left"></i> Retour à la connexion
            </a>
        </div>
    </div>
</div>
{% endblock %}
