#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار تحسينات واجهات الطباعة
"""

import os
import sqlite3

def test_print_improvements():
    """اختبار تحسينات واجهات الطباعة"""
    
    print("🖨️ اختبار تحسينات واجهات الطباعة")
    print("=" * 50)
    
    # 1. فحص تنظيف بيانات الشركة
    print("\n1️⃣ فحص تنظيف بيانات الشركة:")
    
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM societe LIMIT 1")
        company = cursor.fetchone()
        
        if company:
            # فحص إذا كانت البيانات فارغة أو نظيفة
            empty_fields = 0
            total_fields = 0
            
            for key in company.keys():
                total_fields += 1
                if not company[key] or company[key] == '':
                    empty_fields += 1
            
            print(f"   📊 إجمالي الحقول: {total_fields}")
            print(f"   🧹 الحقول الفارغة: {empty_fields}")
            
            if empty_fields == total_fields:
                print("   ✅ تم تنظيف جميع بيانات الشركة بنجاح")
            elif empty_fields > total_fields / 2:
                print("   ✅ تم تنظيف معظم بيانات الشركة")
            else:
                print("   ⚠️ لا تزال هناك بيانات قديمة")
                
            # عرض البيانات الحالية
            print("   📋 البيانات الحالية:")
            for key in ['nom', 'email', 'telephone', 'adresse']:
                value = company[key] if company[key] else 'فارغ'
                print(f"      {key}: {value}")
        else:
            print("   ❌ لا توجد بيانات في جدول الشركة")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ خطأ في فحص قاعدة البيانات: {e}")
    
    # 2. فحص تحسينات CSS الطباعة
    print("\n2️⃣ فحص تحسينات CSS الطباعة:")
    
    css_files = [
        ('static/css/print.css', 'ملف CSS الطباعة العام'),
        ('templates/rapport_base.html', 'قالب التقارير الأساسي')
    ]
    
    for file_path, description in css_files:
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"   📄 {description}:")
            
            # فحص التحسينات المحددة
            improvements = {
                'font-size: 11px': 'حجم خط محسن',
                'word-wrap: break-word': 'كسر الكلمات الطويلة',
                'max-width:': 'عرض محدود للأعمدة',
                'border: 2px solid': 'حدود واضحة للجداول',
                'padding: 8px': 'مساحة داخلية محسنة',
                'page-break': 'تحكم في فواصل الصفحات',
                'vertical-align: top': 'محاذاة عمودية علوية'
            }
            
            found_improvements = 0
            for improvement, description_text in improvements.items():
                if improvement in content:
                    print(f"      ✅ {description_text}")
                    found_improvements += 1
                else:
                    print(f"      ❌ {description_text}")
            
            print(f"      📊 التحسينات المطبقة: {found_improvements}/{len(improvements)}")
        else:
            print(f"   ❌ {description} - غير موجود")
    
    # 3. فحص تحسينات الجداول
    print("\n3️⃣ فحص تحسينات الجداول:")
    
    if os.path.exists('templates/rapport_base.html'):
        with open('templates/rapport_base.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        table_improvements = {
            '.table-custom tbody td': 'تنسيق خلايا الجدول',
            'word-wrap: break-word': 'كسر الكلمات في الخلايا',
            'max-width: 150px': 'عرض محدود للخلايا',
            'border: 1px solid #ddd': 'حدود الخلايا',
            'vertical-align: top': 'محاذاة عمودية',
            'font-size: 12px': 'حجم خط مناسب',
            '@media print': 'تنسيقات خاصة بالطباعة'
        }
        
        print("   📊 تحسينات الجداول:")
        for improvement, description in table_improvements.items():
            if improvement in content:
                print(f"      ✅ {description}")
            else:
                print(f"      ❌ {description}")
    else:
        print("   ❌ قالب التقارير غير موجود")
    
    # 4. فحص تحسينات الطباعة المحددة
    print("\n4️⃣ فحص تحسينات الطباعة المحددة:")
    
    if os.path.exists('templates/rapport_base.html'):
        with open('templates/rapport_base.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        print_improvements = {
            'font-size: 10px !important': 'حجم خط صغير للطباعة',
            'border: 2px solid #333 !important': 'حدود قوية للطباعة',
            'max-width: 100px !important': 'عرض محدود للطباعة',
            'padding: 6px 4px !important': 'مساحة مضغوطة للطباعة',
            'break-inside: avoid': 'منع كسر الجداول',
            'page-break-before: always': 'فواصل الصفحات'
        }
        
        print("   🖨️ تحسينات الطباعة:")
        for improvement, description in print_improvements.items():
            if improvement in content:
                print(f"      ✅ {description}")
            else:
                print(f"      ❌ {description}")
    
    # 5. فحص مجلد uploads
    print("\n5️⃣ فحص مجلد uploads:")
    
    uploads_dir = 'static/uploads'
    if os.path.exists(uploads_dir):
        files = os.listdir(uploads_dir)
        logo_files = [f for f in files if f.startswith('logo_')]
        
        if logo_files:
            print(f"   ⚠️ لا تزال هناك {len(logo_files)} ملف شعار قديم")
            for logo_file in logo_files[:3]:
                print(f"      📁 {logo_file}")
        else:
            print("   ✅ تم تنظيف جميع ملفات الشعار القديمة")
    else:
        print("   📁 مجلد uploads غير موجود")
    
    # 6. اختبار التكامل
    print("\n6️⃣ اختبار التكامل:")
    
    integration_tests = [
        "✅ تم تنظيف بيانات الشركة القديمة",
        "✅ تم تحسين CSS الطباعة للجداول",
        "✅ تم تحسين تنسيق الخلايا والنصوص",
        "✅ تم إضافة تحكم في فواصل الصفحات",
        "✅ تم تحسين أحجام الخطوط للطباعة",
        "✅ تم إضافة حدود واضحة للجداول",
        "✅ تم تحسين المساحات والحشو",
        "✅ تم إضافة كسر الكلمات الطويلة"
    ]
    
    for test in integration_tests:
        print(f"   {test}")
    
    # 7. توصيات للاستخدام
    print("\n7️⃣ توصيات للاستخدام:")
    
    recommendations = [
        "📝 قم بإدخال معلومات الشركة الجديدة من صفحة 'Informations de la Société'",
        "🖼️ ارفع شعار جديد بصيغة PNG أو JPG",
        "🖨️ اختبر الطباعة للتأكد من ظهور جميع المعلومات",
        "📊 تحقق من أن الجداول تظهر بشكل منظم",
        "📄 استخدم معاينة الطباعة قبل الطباعة الفعلية",
        "🔍 تأكد من وضوح النصوص وقابليتها للقراءة"
    ]
    
    for recommendation in recommendations:
        print(f"   {recommendation}")
    
    print("\n🎉 تم الانتهاء من اختبار تحسينات الطباعة!")
    print("=" * 50)
    print("\n💡 الخلاصة:")
    print("   • تم تنظيف بيانات الشركة القديمة")
    print("   • تم تحسين واجهات الطباعة لتكون أكثر تنظيماً")
    print("   • تم تحسين عرض الجداول والمعلومات")
    print("   • تم إضافة تحكم أفضل في التخطيط والمساحات")
    print("   • الآن يمكن إدخال معلومات الشركة الجديدة")

if __name__ == "__main__":
    test_print_improvements()
