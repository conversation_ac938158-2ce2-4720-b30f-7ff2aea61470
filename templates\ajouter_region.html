{% extends "base.html" %}

{% block title %}Ajouter une Région - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Ajouter une Région{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-map-marker-alt"></i> Ajouter une nouvelle région
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('ajouter_region') }}">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="nom" class="form-label">Nom de la région</label>
                            <input type="text" class="form-control" id="nom" name="nom" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer
                        </button>
                        <a href="{{ url_for('regions') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
