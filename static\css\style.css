/* Styles généraux */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', sans-serif;
}

/* تحسين عرض الأيقونات */
.fas, .far, .fab, .fal, .fad, .fa {
    font-family: "Font Awesome 6 Free", "Font Awesome 6 Pro", "Font Awesome 5 Free", "Font Awesome 5 Pro", "FontAwesome" !important;
    font-weight: 900 !important;
    font-style: normal !important;
    font-variant: normal !important;
    text-transform: none !important;
    line-height: 1 !important;
    -webkit-font-smoothing: antialiased !important;
    -moz-osx-font-smoothing: grayscale !important;
}

/* إصلاح الأيقونات في حالة عدم التحميل */
i[class*="fa-"]:before {
    font-family: "Font Awesome 6 Free" !important;
    font-weight: 900 !important;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

/* Style spécifique pour la page de connexion */
body.login-page {
    background: linear-gradient(135deg, #3498db, #8e44ad);
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    position: relative;
}

body.login-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../images/pattern.png');
    opacity: 0.05;
    z-index: -1;
}

.container {
    width: 100%;
    max-width: 1200px;
    padding: 0 15px;
    margin: 0 auto;
}

/* Styles pour la sidebar */
.sidebar {
    min-height: 100vh;
    background: #2c3e50;
    color: white;
    position: fixed;
    width: 250px;
    transition: all 0.3s;
    z-index: 1000;
    overflow-y: auto;
    height: 100vh;
    scrollbar-width: thin;
    scrollbar-color: #3498db #1a2530;
    display: flex;
    flex-direction: column;
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: #1a2530;
}

.sidebar::-webkit-scrollbar-thumb {
    background-color: #3498db;
    border-radius: 6px;
    border: 2px solid #1a2530;
}

.sidebar-header {
    padding: 20px;
    background: #1a2530;
    text-align: center;
}

.sidebar-header img {
    width: 60px;
    height: 60px;
    margin-bottom: 10px;
}

.sidebar-header h3 {
    font-size: 18px;
    margin: 0;
    color: white;
}

.sidebar ul.components {
    padding: 20px 0;
    border-bottom: 1px solid #3c5166;
    max-height: calc(100vh - 120px);
    overflow-y: auto;
    overflow-x: hidden;
    scrollbar-width: thin;
    scrollbar-color: #3498db #1a2530;
    flex: 1;
    margin-bottom: 0;
}

.sidebar ul.components::-webkit-scrollbar {
    width: 6px;
}

.sidebar ul.components::-webkit-scrollbar-track {
    background: #1a2530;
}

.sidebar ul.components::-webkit-scrollbar-thumb {
    background-color: #3498db;
    border-radius: 6px;
    border: 2px solid #1a2530;
}

.sidebar ul li a {
    padding: 12px 20px;
    font-size: 15px;
    display: block;
    color: #ecf0f1;
    text-decoration: none;
    transition: all 0.3s;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.sidebar ul li ul {
    background: #1a2530;
    padding: 0;
    margin: 0;
    list-style: none;
}

.sidebar ul li ul li a {
    padding: 10px 40px;
    font-size: 14px;
    border-left: 3px solid transparent;
}

.sidebar ul li ul li a:hover {
    border-left-color: #3498db;
    background: #2c3e50;
}

.sidebar ul li ul li.active a {
    border-left-color: #3498db;
    background: #3498db;
    color: white;
}

.sidebar ul li a:hover {
    background: #3498db;
    color: white;
}

.sidebar ul li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar ul li.active > a {
    background: #3498db;
    color: white;
}

.content {
    width: calc(100% - 250px);
    margin-left: 250px;
    transition: all 0.3s;
    padding: 20px;
}

/* Styles pour la page de connexion */
.login-container {
    width: 100%;
    max-width: 420px;
    padding: 20px;
    animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.login-box {
    background-color: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 40px 30px;
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
    text-align: center;
    position: relative;
    overflow: hidden;
}

.login-box::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 5px;
    background: linear-gradient(90deg, #3498db, #8e44ad);
}

.logo-container {
    margin-bottom: 30px;
}

.logo-container .logo {
    width: 100px;
    height: 100px;
    object-fit: contain;
    margin-bottom: 15px;
}

.logo-container h1 {
    color: #2c3e50;
    font-size: 24px;
    margin-bottom: 5px;
    font-weight: 600;
}

.logo-container p {
    color: #7f8c8d;
    font-size: 14px;
    margin-bottom: 20px;
}

.input-group {
    position: relative;
    margin-bottom: 25px;
}

.input-group input {
    width: 100%;
    padding: 12px 15px 12px 45px;
    border: none;
    border-radius: 30px;
    background-color: #f5f5f5;
    font-size: 15px;
    color: #333;
    transition: all 0.3s;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.input-group input:focus {
    outline: none;
    background-color: #fff;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
}

.input-group i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #7f8c8d;
    font-size: 18px;
}

.login-btn {
    width: 100%;
    padding: 14px;
    border: none;
    border-radius: 30px;
    background: linear-gradient(90deg, #3498db, #8e44ad);
    color: white;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
    margin-top: 10px;
}

.login-btn:hover {
    background: linear-gradient(90deg, #2980b9, #8e44ad);
    box-shadow: 0 7px 20px rgba(52, 152, 219, 0.6);
    transform: translateY(-2px);
}

.login-btn:active {
    transform: translateY(0);
    box-shadow: 0 3px 10px rgba(52, 152, 219, 0.4);
}

.links {
    margin-top: 25px;
    display: flex;
    justify-content: space-between;
}

.links a {
    color: #7f8c8d;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s;
}

.links a:hover {
    color: #3498db;
}

.alert {
    padding: 12px 15px;
    margin-bottom: 20px;
    border-radius: 8px;
    font-size: 14px;
    animation: slideDown 0.4s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

/* Styles pour les tableaux */
.table {
    width: 100%;
    margin-bottom: 1rem;
    color: #212529;
    vertical-align: middle;
    border-collapse: collapse;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
    border-bottom: 2px solid #dee2e6;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
    padding: 12px;
}

.table td {
    vertical-align: middle;
    padding: 12px;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover tbody tr:hover {
    background-color: rgba(52, 152, 219, 0.05);
}

.table-bordered {
    border: 1px solid #dee2e6;
}

.table-bordered th,
.table-bordered td {
    border: 1px solid #dee2e6;
}

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Styles pour les cartes */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
    margin-bottom: 25px;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid rgba(255,255,255,0.8);
    backdrop-filter: blur(10px);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 15px 20px;
    font-weight: 600;
    color: #2c3e50;
    border-top-left-radius: 10px;
    border-top-right-radius: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.card-header h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.card-header .btn-group {
    margin-left: auto;
}

.card-body {
    padding: 20px;
}

/* Styles pour les boutons */
.btn {
    border-radius: 5px;
    padding: 8px 15px;
    font-weight: 500;
    transition: all 0.3s;
    cursor: pointer;
}

.btn-primary {
    background-color: #3498db;
    border-color: #3498db;
    color: white;
}

.btn-primary:hover {
    background-color: #2980b9;
    border-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(52, 152, 219, 0.2);
}

.btn-success {
    background-color: #2ecc71;
    border-color: #2ecc71;
    color: white;
}

.btn-success:hover {
    background-color: #27ae60;
    border-color: #27ae60;
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(46, 204, 113, 0.2);
}

.btn-danger {
    background-color: #e74c3c;
    border-color: #e74c3c;
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
    border-color: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(231, 76, 60, 0.2);
}

.btn-warning {
    background-color: #f39c12;
    border-color: #f39c12;
    color: white;
}

.btn-warning:hover {
    background-color: #d35400;
    border-color: #d35400;
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(243, 156, 18, 0.2);
}

.btn-info {
    background-color: #3498db;
    border-color: #3498db;
    color: white;
}

.btn-info:hover {
    background-color: #2980b9;
    border-color: #2980b9;
    transform: translateY(-2px);
    box-shadow: 0 5px 10px rgba(52, 152, 219, 0.2);
}

.btn-outline-primary {
    color: #3498db;
    border-color: #3498db;
    background-color: transparent;
}

.btn-outline-primary:hover, .btn-outline-primary.active {
    background-color: #3498db;
    color: white;
    border-color: #3498db;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 0.875rem;
}

/* Styles pour les formulaires */
.form-control, .form-select {
    border-radius: 5px;
    padding: 10px 15px;
    border: 1px solid #ced4da;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.form-control:focus, .form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 8px;
}

/* Styles pour l'impression */
@media print {
    .sidebar, .navbar, .btn, .no-print {
        display: none !important;
    }

    .content {
        margin-left: 0 !important;
        width: 100% !important;
    }

    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }

    body {
        background-color: white !important;
    }
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        margin-left: -250px;
    }

    .content {
        width: 100%;
        margin-left: 0;
    }

    .sidebar.active {
        margin-left: 0;
    }

    .content.active {
        margin-left: 250px;
        width: calc(100% - 250px);
    }
}

@media (max-width: 480px) {
    .login-box {
        padding: 30px 20px;
    }

    .logo-container .logo {
        width: 80px;
        height: 80px;
    }
}



/* تحسينات بسيطة وعملية */

/* تحسين الأزرار */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border: none;
    padding: 10px 20px;
    font-size: 14px;
    position: relative;
    overflow: hidden;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.btn:active {
    transform: translateY(0);
}

/* تحسين مجموعات الأزرار */
.btn-group .btn {
    margin-right: 8px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* ألوان محسنة للأزرار */
.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838, #1ea080);
}

.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
}

.btn-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    border: none;
}

.btn-info:hover {
    background: linear-gradient(135deg, #138496, #0f6674);
}

.btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    border: none;
    color: #212529;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800, #d39e00);
    color: #212529;
}

.btn-danger {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border: none;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333, #bd2130);
}

/* تحسين الجداول */
.table {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    margin-bottom: 0;
}

.table thead th {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 15px 12px;
    font-size: 14px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table tbody td {
    padding: 12px;
    vertical-align: middle;
    border-bottom: 1px solid #f1f3f4;
}

.table tbody tr {
    transition: all 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
    transform: scale(1.01);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* تحسين الكروت */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    overflow: hidden;
    background: #ffffff;
}

.card:hover {
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    padding: 20px;
    border-radius: 15px 15px 0 0;
}

.card-body {
    padding: 25px;
}

/* تحسين الشارات */
.badge {
    font-weight: 500;
    padding: 8px 12px;
    border-radius: 20px;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* تحسين القائمة الجانبية */
.sidebar {
    background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
}

.sidebar ul li a {
    border-radius: 8px;
    margin: 3px 12px;
    padding: 12px 15px;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.sidebar ul li a::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.sidebar ul li a:hover::before {
    left: 100%;
}

.sidebar ul li a:hover {
    background: linear-gradient(135deg, #3498db, #2980b9) !important;
    color: white !important;
    transform: translateX(8px);
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.sidebar ul li.active > a {
    background: linear-gradient(135deg, #3498db, #2980b9) !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(52, 152, 219, 0.4);
    transform: translateX(5px);
}

/* تحسين النماذج */
.form-control {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
    font-size: 14px;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
    transform: translateY(-1px);
}

.form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 8px;
    font-size: 14px;
}

/* تحسين التنبيهات */
.alert {
    border: none;
    border-radius: 12px;
    font-weight: 500;
    padding: 15px 20px;
    margin-bottom: 20px;
    border-left: 4px solid;
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.alert-success {
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    border-left-color: #28a745;
    color: #155724;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da, #f1b0b7);
    border-left-color: #dc3545;
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1, #bee5eb);
    border-left-color: #17a2b8;
    color: #0c5460;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd, #ffeaa7);
    border-left-color: #ffc107;
    color: #856404;
}

/* تحسين أزرار الأكشن في الجداول */
.table .btn-sm {
    padding: 6px 10px;
    margin: 0 2px;
    border-radius: 6px;
    font-size: 12px;
}

/* تحسين العناوين */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 15px;
}

/* تحسين الأيقونات */
.fas, .far, .fab {
    margin-right: 6px;
}

/* تحسين التخطيط العام */
.content {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    min-height: 100vh;
}

/* تحسين الفلاتر */
.card .form-label {
    color: #6c757d;
    font-size: 13px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* تحسين الاستجابة للأجهزة المحمولة */
@media (max-width: 768px) {
    .btn-group {
        flex-direction: column;
        align-items: stretch;
    }

    .btn-group .btn {
        margin-right: 0;
        margin-bottom: 8px;
        width: 100%;
    }

    .table-responsive {
        font-size: 12px;
    }

    .card-body {
        padding: 15px;
    }
}

/* تحسين الطباعة */
@media print {
    .sidebar, .navbar, .btn, .no-print {
        display: none !important;
    }

    .content {
        width: 100% !important;
        margin: 0 !important;
    }

    .table {
        font-size: 12px;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
    }
}

/* تحسين الاستجابة */
@media (max-width: 768px) {
    .table-responsive {
        font-size: 14px;
    }

    .btn {
        font-size: 14px;
        padding: 8px 12px;
    }

    .card-body {
        padding: 15px;
    }
}

/* تحسين القوائم المنسدلة المتقدم */
.form-select {
    background: #ffffff;
    border: 1px solid #ced4da;
    border-radius: 5px;
    padding: 10px 15px;
    font-size: 14px;
    font-weight: 400;
    color: #495057;
    transition: all 0.3s ease;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m1 6 7 7 7-7'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right 12px center;
    background-size: 16px 12px;
    appearance: none;
    cursor: pointer;
}

.form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
    outline: 0;
}

.form-select:hover {
    border-color: #adb5bd;
}

.form-select option {
    padding: 12px;
    background: #ffffff;
    color: #495057;
    font-weight: 500;
}

.form-select option:hover {
    background: #f8f9fa;
}

.form-select option:checked {
    background: #0d6efd;
    color: white;
}

/* تحسين تسميات الحقول */
.form-label {
    font-weight: 500;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 14px;
}

/* تحسين النماذج في المودال */
.modal .form-select {
    background: #ffffff;
    border: 1px solid #ced4da;
}

.modal .form-select:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 0.25rem rgba(52, 152, 219, 0.25);
}

/* تحسين الأزرار في النماذج */
.modal-footer .btn {
    min-width: 100px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* تحسين رسائل الحذف */
.delete-confirmation-modal .modal-content {
    border: none;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.delete-confirmation-modal .modal-header {
    background-color: #dc3545;
    color: white;
    border-radius: 10px 10px 0 0;
    border: none;
}

.delete-confirmation-modal .modal-body {
    padding: 20px;
    text-align: center;
}

.delete-confirmation-modal .warning-icon {
    font-size: 48px;
    color: #ffc107;
    margin-bottom: 15px;
}