#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع لأزرار SUPPRIMER
Quick test for SUPPRIMER buttons
"""

import sqlite3
import os
from datetime import datetime

def test_javascript_files():
    """اختبار ملفات JavaScript"""
    
    print("💻 اختبار ملفات JavaScript...")
    print("=" * 50)
    
    js_files = {
        'static/js/simple-delete.js': '🗑️ ملف الحذف البسيط الجديد',
        'static/js/delete-modal.js': '🗑️ ملف حوار الحذف القديم',
        'static/js/script.js': '⚙️ ملف JavaScript الرئيسي'
    }
    
    for file_path, description in js_files.items():
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"   ✅ {description} ({size} bytes)")
        else:
            print(f"   ❌ {description} - غير موجود")

def test_base_html():
    """اختبار تحميل ملفات JavaScript في base.html"""
    
    print("\n🎨 اختبار base.html...")
    print("=" * 50)
    
    if not os.path.exists('templates/base.html'):
        print("❌ ملف base.html غير موجود")
        return False
    
    try:
        with open('templates/base.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        js_files_to_check = [
            'simple-delete.js',
            'script.js',
            'import-excel.js'
        ]
        
        for js_file in js_files_to_check:
            if js_file in content:
                print(f"   ✅ {js_file} محمل في base.html")
            else:
                print(f"   ❌ {js_file} غير محمل في base.html")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص base.html: {e}")
        return False

def test_template_buttons():
    """اختبار أزرار الحذف في القوالب"""
    
    print("\n🎨 اختبار أزرار الحذف في القوالب...")
    print("=" * 50)
    
    templates_to_check = {
        'templates/utilisateurs.html': '👥 قالب المستخدمين',
        'templates/marches.html': '📋 قالب الأسواق',
        'templates/interventions.html': '🔧 قالب التدخلات',
        'templates/reclamations.html': '📞 قالب الشكاوى',
        'templates/regions.html': '🌍 قالب المناطق',
        'templates/sites.html': '📍 قالب المواقع'
    }
    
    all_good = True
    
    for template_path, description in templates_to_check.items():
        if os.path.exists(template_path):
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # فحص أزرار الحذف
            has_delete_button = 'confirmDelete(' in content or 'btn-danger' in content
            has_supprimer_url = 'supprimer' in content
            
            print(f"\n   📄 {description}:")
            if has_delete_button:
                print(f"      ✅ يحتوي على أزرار حذف")
            else:
                print(f"      ❌ لا يحتوي على أزرار حذف")
                all_good = False
            
            if has_supprimer_url:
                print(f"      ✅ يحتوي على روابط supprimer")
            else:
                print(f"      ❌ لا يحتوي على روابط supprimer")
                all_good = False
        else:
            print(f"   ❌ {description} - غير موجود")
            all_good = False
    
    return all_good

def test_app_routes():
    """اختبار routes الحذف في app.py"""
    
    print("\n🔧 اختبار routes الحذف في app.py...")
    print("=" * 50)
    
    if not os.path.exists('app.py'):
        print("❌ ملف app.py غير موجود")
        return False
    
    try:
        with open('app.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        routes_to_check = {
            'supprimer_utilisateur': '👥 حذف المستخدمين',
            'supprimer_marche': '📋 حذف الأسواق',
            'supprimer_intervention': '🔧 حذف التدخلات',
            'supprimer_reclamation': '📞 حذف الشكاوى',
            'supprimer_region': '🌍 حذف المناطق',
            'supprimer_site': '📍 حذف المواقع'
        }
        
        all_exist = True
        for route, description in routes_to_check.items():
            if f"def {route}(" in content:
                print(f"   ✅ {description}: {route}")
            else:
                print(f"   ❌ {description}: {route} غير موجود")
                all_exist = False
        
        return all_exist
        
    except Exception as e:
        print(f"❌ خطأ في فحص app.py: {e}")
        return False

def test_database():
    """اختبار قاعدة البيانات"""
    
    print("\n🗄️ اختبار قاعدة البيانات...")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # اختبار الجداول
        tables = ['users', 'marches', 'interventions', 'reclamations', 'regions', 'sites']
        
        all_good = True
        for table in tables:
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"   ✅ جدول {table}: {count} سجل")
            except Exception as e:
                print(f"   ❌ جدول {table}: خطأ - {e}")
                all_good = False
        
        conn.close()
        return all_good
        
    except Exception as e:
        print(f"❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def create_test_summary():
    """إنشاء ملخص الاختبار"""
    
    print("\n📊 ملخص اختبار أزرار SUPPRIMER:")
    print("📊 SUPPRIMER Buttons Test Summary:")
    print("=" * 60)
    
    # تشغيل جميع الاختبارات
    js_test = test_javascript_files()
    base_test = test_base_html()
    template_test = test_template_buttons()
    app_test = test_app_routes()
    db_test = test_database()
    
    print(f"\n🎯 النتائج:")
    print(f"   ✅ ملفات JavaScript")
    print(f"   {'✅' if base_test else '❌'} تحميل في base.html")
    print(f"   {'✅' if template_test else '❌'} أزرار في القوالب")
    print(f"   {'✅' if app_test else '❌'} routes في app.py")
    print(f"   {'✅' if db_test else '❌'} قاعدة البيانات")
    
    all_tests_passed = base_test and template_test and app_test and db_test
    
    if all_tests_passed:
        print("\n🎉 جميع الاختبارات نجحت!")
        print("🎉 All tests passed!")
        print("\n✅ أزرار SUPPRIMER جاهزة للاستخدام:")
        print("✅ SUPPRIMER buttons ready for use:")
        print("   1. تشغيل البرنامج")
        print("   1. Run the application")
        print("   2. اختبار أزرار الحذف")
        print("   2. Test delete buttons")
        print("   3. يجب أن تظهر حوارات تأكيد جميلة")
        print("   3. Should show beautiful confirmation dialogs")
    else:
        print("\n❌ بعض الاختبارات فشلت")
        print("❌ Some tests failed")
        print("💡 راجع الأخطاء أعلاه")
        print("💡 Review the errors above")
    
    return all_tests_passed

def main():
    """الدالة الرئيسية"""
    
    print("🧪 اختبار سريع لأزرار SUPPRIMER")
    print("🧪 Quick Test for SUPPRIMER Buttons")
    print("=" * 70)
    
    # تشغيل الاختبارات
    all_tests_passed = create_test_summary()
    
    if all_tests_passed:
        print("\n🚀 للبدء:")
        print("🚀 To start:")
        print("   1. تشغيل البرنامج")
        print("   1. Run the application")
        print("   2. اختبار أزرار SUPPRIMER")
        print("   2. Test SUPPRIMER buttons")
        print("   3. يجب أن تعمل بشكل مثالي")
        print("   3. Should work perfectly")
        
        print("\n💡 كيفية الاختبار:")
        print("💡 How to test:")
        print("   1. اذهب إلى أي صفحة (الأسواق، التدخلات، إلخ)")
        print("   1. Go to any page (markets, interventions, etc.)")
        print("   2. انقر على زر الحذف الأحمر")
        print("   2. Click the red delete button")
        print("   3. يجب أن يظهر حوار تأكيد جميل")
        print("   3. Should show a beautiful confirmation dialog")
        print("   4. انقر 'حذف' للتأكيد أو 'إلغاء' للإلغاء")
        print("   4. Click 'حذف' to confirm or 'إلغاء' to cancel")
    else:
        print("\n🔧 للإصلاح:")
        print("🔧 To fix:")
        print("   1. راجع الأخطاء أعلاه")
        print("   1. Review errors above")
        print("   2. شغل أداة الإصلاح مرة أخرى")
        print("   2. Run the fix tool again")
        print("   3. python fix_supprimer_buttons_final.py")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
