@echo off
title تشغيل شبكي مباشر - نظام إدارة الصيانة
color 0A
echo.
echo ========================================
echo    تشغيل شبكي مباشر - بدون تعقيدات
echo    Direct Network Mode - Simple
echo ========================================
echo.

echo 🌐 تشغيل النظام في الوضع الشبكي...
echo 🌐 Starting system in network mode...
echo.

REM الحصول على عنوان IP المحلي
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    set "ip=%%a"
    goto :found_ip
)
:found_ip
set ip=%ip: =%

echo 📊 معلومات الاتصال:
echo 📊 Connection information:
echo    🏠 محلي / Local: http://127.0.0.1:5000
echo    🌐 شبكي / Network: http://%ip%:5000
echo    📱 هاتف / Mobile: http://%ip%:5000
echo.
echo 📋 للوصول من أجهزة أخرى:
echo 📋 To access from other devices:
echo    1. تأكد من اتصال الجهاز بنفس الشبكة
echo    1. Make sure device is on same network
echo    2. افتح المتصفح واذهب إلى: http://%ip%:5000
echo    2. Open browser and go to: http://%ip%:5000
echo    3. استخدم admin/admin123 لتسجيل الدخول
echo    3. Use admin/admin123 to login
echo.

REM تشغيل البرنامج مباشرة
echo 🚀 تشغيل النظام...
echo 🚀 Starting system...
echo.

if exist "start_network.py" (
    python start_network.py
) else if exist "Maintenance_Management_System.exe" (
    start /B Maintenance_Management_System.exe
) else if exist "run_app.py" (
    python run_app.py
) else (
    echo ❌ لم يتم العثور على ملفات التشغيل
    echo ❌ No executable files found
    echo.
    echo 💡 تأكد من وجود أحد الملفات التالية:
    echo 💡 Make sure one of these files exists:
    echo    - start_network.py
    echo    - Maintenance_Management_System.exe
    echo    - run_app.py
    echo.
    pause
    exit /b 1
)

echo.
echo 🎯 انتهى التشغيل
echo 🎯 Execution finished
pause
