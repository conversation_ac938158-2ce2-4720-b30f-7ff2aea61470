#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مباشر للتطبيق مع فتح المتصفح
"""

import os
import sys
import time
import threading
import webbrowser
import socket
import subprocess
from contextlib import closing

# إضافة مسار التطبيق
if getattr(sys, 'frozen', False):
    app_dir = os.path.dirname(sys.executable)
else:
    app_dir = os.path.dirname(os.path.abspath(__file__))

sys.path.insert(0, app_dir)

try:
    from config_loader import config_loader
except ImportError:
    config_loader = None

def find_free_port():
    """البحث عن منفذ متاح"""
    with closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as s:
        s.bind(('', 0))
        s.listen(1)
        port = s.getsockname()[1]
    return port

def open_browser_multiple_ways(url):
    """فتح المتصفح بطرق متعددة"""
    methods = [
        lambda: os.startfile(url),  # Windows
        lambda: subprocess.run(['start', url], shell=True),  # Windows cmd
        lambda: subprocess.run(['cmd', '/c', 'start', url]),  # Windows cmd alternative
        lambda: webbrowser.open(url),  # Python webbrowser
        lambda: webbrowser.open_new_tab(url),  # Python webbrowser new tab
        lambda: subprocess.run(['explorer', url]),  # Windows explorer
    ]

    for method in methods:
        try:
            method()
            print(f"✅ تم فتح المتصفح بنجاح")
            return True
        except Exception as e:
            print(f"⚠️ فشل في طريقة: {e}")
            continue

    print(f"❌ فشل في فتح المتصفح تلقائياً")
    print(f"🌐 افتح المتصفح يدوياً واذهب إلى: {url}")
    return False

def wait_for_server(port, max_attempts=30):
    """انتظار حتى يصبح الخادم جاهز"""
    for i in range(max_attempts):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.settimeout(1)
                result = s.connect_ex(('127.0.0.1', port))
                if result == 0:
                    print(f"✅ الخادم جاهز على المنفذ {port}")
                    return True
        except:
            pass

        print(f"⏳ انتظار الخادم... ({i+1}/{max_attempts})")
        time.sleep(1)

    return False

def open_browser_when_ready(port):
    """فتح المتصفح عندما يصبح الخادم جاهز"""
    print(f"🔄 انتظار تشغيل الخادم...")

    if wait_for_server(port):
        url = f'http://127.0.0.1:{port}'
        print(f"🌐 فتح المتصفح: {url}")
        open_browser_multiple_ways(url)
    else:
        print(f"❌ انتهت مهلة انتظار الخادم")

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل نظام إدارة الصيانة")
    print("=" * 50)

    try:
        # استيراد التطبيق
        from app import app

        # إعدادات شبكية افتراضية (بدون تعقيدات)
        port = 5000
        host = '0.0.0.0'  # للسماح بالوصول من أي جهاز

        # الحصول على عنوان IP المحلي
        try:
            import socket
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
        except:
            local_ip = "*************"

        print(f"📋 إعدادات التشغيل:")
        print(f"   🔧 نوع التشغيل: شبكي")
        print(f"   👥 متعدد المستخدمين: نعم")
        print(f"   🌐 وضع الخادم: نعم")

        print(f"📡 المنفذ المختار: {port}")
        print(f"🌐 الرابط المحلي: http://127.0.0.1:{port}")
        print(f"🌍 للوصول من أجهزة أخرى: http://{local_ip}:{port}")
        print(f"📱 من الهاتف المحمول: http://{local_ip}:{port}")

        print("=" * 50)

        # تشغيل المتصفح في thread منفصل
        browser_thread = threading.Thread(target=open_browser_when_ready, args=(port,))
        browser_thread.daemon = True
        browser_thread.start()

        # تشغيل التطبيق
        print(f"🔥 تشغيل الخادم...")
        app.run(
            host=host,
            port=port,
            debug=False,
            threaded=True,
            use_reloader=False
        )

    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        input("اضغط Enter للخروج...")

if __name__ == '__main__':
    main()
