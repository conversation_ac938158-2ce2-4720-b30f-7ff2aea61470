{% extends "base.html" %}

{% block title %}Informations Société - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Informations Société{% endblock %}

{% block content %}
<div class="container-fluid mt-4 mb-5">
  <!-- Header moderne -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-lg border-0">
        <div class="card-header bg-gradient-primary text-white py-4">
          <div class="row align-items-center">
            <div class="col-md-8">
              <div class="d-flex align-items-center">
                <div class="icon-circle bg-white bg-opacity-20 me-3">
                  <i class="fas fa-building text-white"></i>
                </div>
                <div>
                  <h3 class="mb-0 fw-bold">Informations de votre société</h3>
                  <small class="opacity-75"><PERSON><PERSON><PERSON> les informations de votre entreprise</small>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="header-right">
                <div class="btn-group" role="group">
                  <a href="{{ url_for('dashboard') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-arrow-left"></i> Retour
                  </a>
                  <button type="button" class="btn btn-light btn-sm" onclick="window.print()">
                    <i class="fas fa-print"></i> Imprimer
                  </button>
                  <button type="button" class="btn btn-info btn-sm" onclick="location.reload()">
                    <i class="fas fa-sync-alt"></i> Actualiser
                  </button>
                  <a href="{{ url_for('logout') }}" class="btn btn-success btn-sm">
                    <i class="fas fa-sign-out-alt"></i> Déconnexion
                  </a>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Formulaire principal -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow-lg border-0">
        <div class="card-body p-4">
      <form method="POST" action="{{ url_for('societe') }}" enctype="multipart/form-data" id="societeForm">
        <!-- Section Logo -->
        <div class="row mb-5">
          <div class="col-12">
            <div class="card border-0 bg-light">
              <div class="card-header bg-gradient-info text-white">
                <h5 class="mb-0"><i class="fas fa-image me-2"></i>Logo de l'entreprise</h5>
              </div>
              <div class="card-body text-center py-5">
                <div class="row justify-content-center">
                  <div class="col-md-8 col-lg-6">
                    <div class="mb-4">
                      <div class="position-relative d-inline-block" id="logoContainer">
                        {% if societe and societe.logo %}
                        <img id="logoPreview" src="{{ url_for('uploaded_file', filename=societe.logo) }}" alt="Logo de la société" class="img-fluid rounded-3 border border-4 border-primary" style="width: 250px; height: 200px; object-fit: contain; box-shadow: 0 8px 25px rgba(0,0,0,0.15); background: #ffffff;">
                        {% else %}
                        <div id="logoPreview" class="d-flex align-items-center justify-content-center rounded-3 border border-4 border-dashed border-secondary" style="width: 250px; height: 200px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); box-shadow: 0 8px 25px rgba(0,0,0,0.1);">
                          <div class="text-center">
                            <i class="fas fa-image fa-4x text-muted mb-3"></i>
                            <h5 class="text-muted mb-2">Aucun logo</h5>
                            <p class="text-muted small mb-0">Cliquez pour ajouter un logo</p>
                          </div>
                        </div>
                        {% endif %}
                        <div class="position-absolute bottom-0 end-0" style="margin: -10px;">
                          <label for="logo" class="btn btn-primary btn-lg rounded-circle d-flex align-items-center justify-content-center logo-upload-btn shadow-lg" style="width: 60px; height: 60px; cursor: pointer;" title="Changer le logo">
                            <i class="fas fa-camera fa-lg"></i>
                          </label>
                        </div>
                        <!-- Overlay pour drag & drop -->
                        <div class="position-absolute top-0 start-0 w-100 h-100 d-none" id="dragOverlay" style="background: rgba(0,123,255,0.2); border: 3px dashed #007bff; border-radius: 12px; z-index: 10;">
                          <div class="d-flex flex-column align-items-center justify-content-center h-100">
                            <i class="fas fa-cloud-upload-alt fa-3x text-primary mb-2"></i>
                            <p class="text-primary fw-bold mb-0">Déposez votre image ici</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <input type="file" class="form-control d-none" id="logo" name="logo" accept="image/*">
                    <div class="text-center">
                      <small class="text-muted d-block">
                        <i class="fas fa-info-circle me-1"></i>Cliquez sur l'icône ou glissez-déposez une image
                      </small>
                      <small class="text-muted">
                        <i class="fas fa-file-image me-1"></i>PNG, JPG, GIF, BMP, WebP ou SVG, max 10MB
                      </small>
                    </div>
                    <div id="logo-error" class="alert alert-danger mt-2" style="display: none;" role="alert"></div>
                    <div id="logo-success" class="alert alert-success mt-2" style="display: none;" role="alert"></div>
                    <div id="upload-progress" class="mt-2" style="display: none;">
                      <div class="progress">
                        <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Section Informations générales -->
        <div class="row mb-5">
          <div class="col-12">
            <div class="card border-0 bg-light">
              <div class="card-header bg-gradient-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Informations générales</h5>
              </div>
              <div class="card-body">
                <div class="row g-4">
                  <div class="col-md-6">
                    <div class="form-floating">
                      <input type="text" class="form-control" id="nom" name="nom" value="{{ societe.nom }}" placeholder="Nom de la société" required>
                      <label for="nom"><i class="fas fa-building me-2"></i>Nom de la société *</label>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-floating">
                      <input type="text" class="form-control" id="telephone" name="telephone" value="{{ societe.telephone }}" placeholder="Téléphone">
                      <label for="telephone"><i class="fas fa-phone me-2"></i>Téléphone</label>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-floating">
                      <input type="text" class="form-control" id="responsable" name="responsable" value="{{ societe.responsable }}" placeholder="Responsable">
                      <label for="responsable"><i class="fas fa-user me-2"></i>Responsable</label>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-floating">
                      <input type="email" class="form-control" id="email" name="email" value="{{ societe.email }}" placeholder="Email">
                      <label for="email"><i class="fas fa-envelope me-2"></i>Email</label>
                    </div>
                  </div>
                  <div class="col-12">
                    <div class="form-floating">
                      <textarea class="form-control" id="adresse" name="adresse" placeholder="Adresse" style="height: 100px">{{ societe.adresse }}</textarea>
                      <label for="adresse"><i class="fas fa-map-marker-alt me-2"></i>Adresse</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Section Informations fiscales et administratives -->
        <div class="row mb-5">
          <div class="col-12">
            <div class="card border-0 bg-light">
              <div class="card-header bg-gradient-warning text-dark">
                <h5 class="mb-0"><i class="fas fa-folder me-2"></i>Informations fiscales et administratives</h5>
              </div>
              <div class="card-body">
                <div class="row g-4">
                  <div class="col-md-4">
                    <div class="form-floating">
                      <input type="text" class="form-control" id="if_fiscal" name="if_fiscal" value="{{ societe.if_fiscal }}" placeholder="IF">
                      <label for="if_fiscal"><i class="fas fa-file-invoice me-2"></i>Identifiant Fiscal (IF)</label>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-floating">
                      <input type="text" class="form-control" id="rc" name="rc" value="{{ societe.rc }}" placeholder="RC">
                      <label for="rc"><i class="fas fa-certificate me-2"></i>Registre de Commerce (RC)</label>
                    </div>
                  </div>
                  <div class="col-md-4">
                    <div class="form-floating">
                      <input type="text" class="form-control" id="patente" name="patente" value="{{ societe.patente }}" placeholder="Patente">
                      <label for="patente"><i class="fas fa-stamp me-2"></i>Patente</label>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-floating">
                      <input type="text" class="form-control" id="ice" name="ice" value="{{ societe.ice }}" placeholder="ICE">
                      <label for="ice"><i class="fas fa-id-card me-2"></i>ICE</label>
                    </div>
                  </div>
                  <div class="col-md-6">
                    <div class="form-floating">
                      <input type="text" class="form-control" id="cnss" name="cnss" value="{{ societe.cnss }}" placeholder="CNSS">
                      <label for="cnss"><i class="fas fa-shield-alt me-2"></i>CNSS</label>
                    </div>
                  </div>
                  <div class="col-12">
                    <div class="form-floating">
                      <input type="text" class="form-control" id="pied_page" name="pied_page" value="{{ societe.pied_page }}" placeholder="Pied de page">
                      <label for="pied_page"><i class="fas fa-file-alt me-2"></i>Pied de page (pour les documents)</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Section Boutons d'action -->
        <div class="row">
          <div class="col-12">
            <div class="card border-0 bg-gradient-light">
              <div class="card-body text-center py-4">
                <div class="d-flex justify-content-center gap-3 flex-wrap">
                  <button type="submit" class="btn btn-success btn-lg btn-modern px-4" id="saveBtn">
                    <i class="fas fa-save me-2"></i> Enregistrer les modifications
                  </button>
                  <button type="button" class="btn btn-warning btn-lg btn-modern px-4" id="resetBtn">
                    <i class="fas fa-undo me-2"></i> Réinitialiser
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const logoInput = document.getElementById('logo');
        const logoPreview = document.getElementById('logoPreview');
        const errorDiv = document.getElementById('logo-error');
        const successDiv = document.getElementById('logo-success');
        const progressDiv = document.getElementById('upload-progress');
        const progressBar = progressDiv.querySelector('.progress-bar');
        const logoContainer = document.getElementById('logoContainer');

        // Fonction pour afficher les messages
        function showMessage(type, message) {
            errorDiv.style.display = 'none';
            successDiv.style.display = 'none';
            progressDiv.style.display = 'none';

            if (type === 'error') {
                errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle me-2"></i>' + message;
                errorDiv.style.display = 'block';
                setTimeout(() => {
                    errorDiv.style.display = 'none';
                }, 5000);
            } else if (type === 'success') {
                successDiv.innerHTML = '<i class="fas fa-check-circle me-2"></i>' + message;
                successDiv.style.display = 'block';
                setTimeout(() => {
                    successDiv.style.display = 'none';
                }, 3000);
            } else if (type === 'progress') {
                progressDiv.style.display = 'block';
                progressBar.style.width = message + '%';
                progressBar.textContent = message + '%';
            }
        }

        // Fonction pour valider le fichier
        function validateFile(file) {
            console.log('Validation du fichier:', file.name, 'Type:', file.type, 'Taille:', file.size);

            // Vérifier le type de fichier
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp', 'image/svg+xml'];
            const fileExtension = file.name.toLowerCase().split('.').pop();
            const allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'];

            if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
                return 'Format non supporté. Utilisez JPG, PNG, GIF, BMP, WebP ou SVG.';
            }

            // Vérifier la taille du fichier (max 10MB)
            if (file.size > 10 * 1024 * 1024) {
                return 'Fichier trop volumineux. Maximum 10MB.';
            }

            // Vérifier que le fichier n'est pas vide
            if (file.size === 0) {
                return 'Le fichier est vide.';
            }

            return null; // Pas d'erreur
        }

        // Fonction pour prévisualiser l'image
        function previewImage(file) {
            const reader = new FileReader();

            reader.onload = function(event) {
                // Vérifier si logoPreview est un div ou une img
                if (logoPreview.tagName === 'DIV') {
                    // Remplacer le div par une img
                    const newImg = document.createElement('img');
                    newImg.id = 'logoPreview';
                    newImg.className = 'img-fluid rounded-3 border border-4 border-primary';
                    newImg.style.cssText = 'width: 250px; height: 200px; object-fit: contain; box-shadow: 0 8px 25px rgba(0,0,0,0.15); background: #ffffff; transition: all 0.3s ease;';
                    newImg.alt = 'Logo de la société';
                    newImg.src = event.target.result;

                    logoPreview.parentNode.replaceChild(newImg, logoPreview);
                    // Mettre à jour la référence
                    window.logoPreview = newImg;
                } else {
                    // C'est déjà une img, juste changer la source
                    logoPreview.src = event.target.result;
                }

                logoPreview.style.transform = 'scale(1.05)';
                showMessage('success', 'Image sélectionnée avec succès! N\'oubliez pas de sauvegarder.');

                // Animation de retour
                setTimeout(() => {
                    logoPreview.style.transform = 'scale(1)';
                }, 300);
            };

            reader.onerror = function() {
                showMessage('error', 'Erreur lors de la lecture du fichier.');
            };

            reader.readAsDataURL(file);
        }

        // Prévisualisation de l'image avec validation améliorée
        logoInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            console.log('Fichier sélectionné:', file);

            // Cacher les messages précédents
            showMessage('', '');

            if (file) {
                // Valider le fichier
                const error = validateFile(file);
                if (error) {
                    showMessage('error', error);
                    logoInput.value = '';
                    return;
                }

                // Prévisualiser l'image
                previewImage(file);
            }
        });

        // Permettre le clic sur toute la zone du logo pour ouvrir le sélecteur de fichier
        logoContainer.addEventListener('click', function(e) {
            // Éviter le double déclenchement si on clique sur le bouton
            if (!e.target.closest('.logo-upload-btn')) {
                logoInput.click();
            }
        });

        // Ajouter un effet visuel au survol
        logoContainer.addEventListener('mouseenter', function() {
            this.classList.add('logo-drop-active');
        });

        logoContainer.addEventListener('mouseleave', function() {
            this.classList.remove('logo-drop-active');
        });

        // Drag and drop functionality amélioré
        const dragOverlay = document.getElementById('dragOverlay');

        logoContainer.addEventListener('dragover', function(e) {
            e.preventDefault();
            e.stopPropagation();
            dragOverlay.classList.remove('d-none');
        });

        logoContainer.addEventListener('dragleave', function(e) {
            e.preventDefault();
            e.stopPropagation();
            // Vérifier si on quitte vraiment le conteneur
            if (!logoContainer.contains(e.relatedTarget)) {
                dragOverlay.classList.add('d-none');
            }
        });

        logoContainer.addEventListener('drop', function(e) {
            e.preventDefault();
            e.stopPropagation();
            dragOverlay.classList.add('d-none');

            const files = e.dataTransfer.files;
            if (files.length > 0) {
                const file = files[0];

                // Valider le fichier
                const error = validateFile(file);
                if (error) {
                    showMessage('error', error);
                    return;
                }

                // Créer un nouvel objet FileList
                const dt = new DataTransfer();
                dt.items.add(file);
                logoInput.files = dt.files;

                // Prévisualiser l'image
                previewImage(file);
            }
        });

        // Empêcher le comportement par défaut du drag & drop sur toute la page
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            document.addEventListener(eventName, function(e) {
                e.preventDefault();
                e.stopPropagation();
            });
        });

        // Validation du formulaire avant soumission avec debug
        const saveBtn = document.getElementById('saveBtn');
        if (saveBtn) {
            saveBtn.addEventListener('click', function(e) {
                const nom = document.getElementById('nom').value.trim();
                console.log('Validation du formulaire - Nom:', nom);

                if (!nom) {
                    e.preventDefault();
                    showMessage('error', 'Le nom de la société est obligatoire.');
                    document.getElementById('nom').focus();
                    return false;
                }

                // Vérifier si un fichier est sélectionné
                const logoFile = logoInput.files[0];
                if (logoFile) {
                    console.log('Fichier logo à envoyer:', logoFile.name, logoFile.size);
                }

                // Ajouter un indicateur de chargement
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i> Enregistrement...';
                this.disabled = true;

                // Réactiver le bouton après 15 secondes (au cas où il y aurait une erreur)
                setTimeout(() => {
                    this.innerHTML = '<i class="fas fa-save me-2"></i> Enregistrer les modifications';
                    this.disabled = false;
                }, 15000);
            });
        }

        // Fonction de réinitialisation améliorée
        const resetBtn = document.getElementById('resetBtn');
        if (resetBtn) {
            resetBtn.addEventListener('click', function(e) {
                e.preventDefault();

                if (confirm('Êtes-vous sûr de vouloir réinitialiser tous les champs? Toutes les modifications non sauvegardées seront perdues.')) {
                    // Réinitialiser tous les champs de texte
                    document.querySelectorAll('input[type="text"], input[type="email"], textarea').forEach(field => {
                        field.value = '';
                        field.classList.remove('border-primary');
                    });

                    // Réinitialiser le fichier logo
                    logoInput.value = '';

                    // Remettre l'affichage par défaut
                    if (logoPreview.tagName === 'IMG') {
                        // Remplacer l'img par un div
                        const defaultDiv = document.createElement('div');
                        defaultDiv.id = 'logoPreview';
                        defaultDiv.className = 'd-flex align-items-center justify-content-center rounded-3 border border-4 border-dashed border-secondary';
                        defaultDiv.style.cssText = 'width: 250px; height: 200px; background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%); box-shadow: 0 8px 25px rgba(0,0,0,0.1);';
                        defaultDiv.innerHTML = `
                            <div class="text-center">
                                <i class="fas fa-image fa-4x text-muted mb-3"></i>
                                <h5 class="text-muted mb-2">Aucun logo</h5>
                                <p class="text-muted small mb-0">Cliquez pour ajouter un logo</p>
                            </div>
                        `;

                        logoPreview.parentNode.replaceChild(defaultDiv, logoPreview);
                        window.logoPreview = defaultDiv;
                    }

                    // Cacher les messages
                    showMessage('', '');

                    // Afficher un message de confirmation
                    showMessage('success', 'Formulaire réinitialisé avec succès!');

                    // Faire défiler vers le haut
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                }
            });
        }

        // Ajouter des événements pour améliorer l'expérience utilisateur
        document.querySelectorAll('input, textarea').forEach(input => {
            input.addEventListener('focus', function() {
                this.classList.add('border-primary');
            });

            input.addEventListener('blur', function() {
                this.classList.remove('border-primary');
            });
        });

        // Ajouter des tooltips informatifs
        const logoLabel = document.querySelector('label[for="logo"]');
        if (logoLabel) {
            logoLabel.setAttribute('data-bs-toggle', 'tooltip');
            logoLabel.setAttribute('data-bs-placement', 'top');
            logoLabel.setAttribute('title', 'Cliquez pour changer le logo ou glissez-déposez une image');
        }

        // Initialiser les tooltips si Bootstrap est disponible
        if (typeof bootstrap !== 'undefined') {
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });
        }

        // Ajouter un gestionnaire d'événements pour le formulaire
        const form = document.getElementById('societeForm');
        if (form) {
            form.addEventListener('submit', function(e) {
                console.log('Soumission du formulaire...');

                // Vérifier si un fichier est sélectionné
                const logoFile = logoInput.files[0];
                if (logoFile) {
                    console.log('Fichier à envoyer:', {
                        name: logoFile.name,
                        size: logoFile.size,
                        type: logoFile.type
                    });
                }
            });
        }

        console.log('Script d\'initialisation terminé');
    });
</script>

<style>
    /* Styles généraux */
    body {
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
    }

    /* Icônes circulaires */
    .icon-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    /* Boutons modernes */
    .btn-modern {
        border-radius: 12px;
        padding: 12px 24px;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    /* Gradients */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .bg-gradient-info {
        background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    }

    .bg-gradient-warning {
        background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    }

    .bg-gradient-light {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    /* Cartes */
    .card {
        border: none;
        border-radius: 20px;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        overflow: hidden;
    }

    .card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
    }

    .card-header {
        border: none;
        border-radius: 20px 20px 0 0 !important;
    }

    /* Form floating amélioré */
    .form-floating {
        position: relative;
    }

    .form-floating > .form-control {
        border-radius: 12px;
        border: 2px solid #e9ecef;
        padding: 1rem 0.75rem;
        height: calc(3.5rem + 2px);
        transition: all 0.3s ease;
    }

    .form-floating > .form-control:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.25rem rgba(102, 126, 234, 0.25);
        transform: translateY(-2px);
    }

    .form-floating > label {
        padding: 1rem 0.75rem;
        color: #6c757d;
        font-weight: 500;
    }

    .form-floating > .form-control:focus ~ label,
    .form-floating > .form-control:not(:placeholder-shown) ~ label {
        opacity: 0.65;
        transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        color: #667eea;
    }

    /* Logo styles */
    #logoPreview {
        transition: all 0.3s ease;
        cursor: pointer;
        min-height: 200px;
        min-width: 250px;
    }

    #logoPreview:hover {
        transform: scale(1.02);
        box-shadow: 0 15px 35px rgba(0,0,0,0.2) !important;
    }

    /* Style pour le conteneur de logo par défaut */
    #logoPreview.d-flex {
        border-style: dashed !important;
        border-width: 3px !important;
        transition: all 0.3s ease;
    }

    #logoPreview.d-flex:hover {
        border-color: #007bff !important;
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%) !important;
    }

    /* Style pour l'image du logo */
    #logoPreview.img-fluid {
        border-style: solid !important;
        border-width: 4px !important;
        border-color: #007bff !important;
    }

    .logo-upload-btn {
        transition: all 0.3s ease;
        box-shadow: 0 6px 20px rgba(0,123,255,0.3);
        border: 3px solid #ffffff;
    }

    .logo-upload-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 8px 25px rgba(0,123,255,0.5);
        background-color: #0056b3 !important;
    }

    #dragOverlay {
        transition: all 0.3s ease;
        backdrop-filter: blur(3px);
    }

    /* Animation pour la zone de drop */
    .logo-drop-active {
        animation: pulse 1s infinite;
    }

    @keyframes pulse {
        0% { transform: scale(1); }
        50% { transform: scale(1.02); }
        100% { transform: scale(1); }
    }

    /* Style pour le conteneur du logo */
    #logoContainer {
        position: relative;
        display: inline-block;
        margin: 20px auto;
    }

    #logoContainer::before {
        content: '';
        position: absolute;
        top: -10px;
        left: -10px;
        right: -10px;
        bottom: -10px;
        background: linear-gradient(45deg, #007bff, #28a745, #ffc107, #dc3545);
        border-radius: 20px;
        z-index: -1;
        opacity: 0;
        transition: opacity 0.3s ease;
    }

    #logoContainer:hover::before {
        opacity: 0.1;
    }

    /* Animations */
    #logo-error, #logo-success {
        animation: fadeInUp 0.3s ease;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Responsive */
    @media (max-width: 768px) {
        .btn-group {
            flex-direction: column;
            gap: 10px;
        }

        .btn-modern {
            width: 100%;
        }

        .icon-circle {
            width: 40px;
            height: 40px;
            font-size: 1rem;
        }
    }

    /* Effets de survol pour les sections */
    .card-body {
        position: relative;
        overflow: hidden;
    }

    .card-body::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
        transition: left 0.5s;
    }

    .card:hover .card-body::before {
        left: 100%;
    }
</style>
{% endblock %}
