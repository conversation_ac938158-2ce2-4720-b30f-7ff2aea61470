# دليل التثبيت والاستخدام
# Installation and Usage Guide

## نظام إدارة الصيانة - Maintenance Management System

---

## 📋 متطلبات النظام / System Requirements

### الحد الأدنى / Minimum Requirements:
- **نظام التشغيل / OS**: Windows 7, 8, 10, 11
- **المعمارية / Architecture**: 32-bit أو 64-bit
- **الذاكرة / RAM**: 2 GB (الحد الأدنى / minimum)
- **مساحة القرص / Disk Space**: 200 MB
- **الشبكة / Network**: اختياري للوصول من أجهزة متعددة / Optional for multi-device access

### الموصى به / Recommended:
- **الذاكرة / RAM**: 4 GB أو أكثر
- **مساحة القرص / Disk Space**: 500 MB أو أكثر
- **اتصال شبكة / Network Connection**: للوصول من أجهزة متعددة

---

## 🔨 بناء الملف التنفيذي / Building the Executable

### الطريقة الأولى: استخدام الملفات الجاهزة / Method 1: Using Ready Files

1. **تثبيت المتطلبات / Install Requirements:**
   ```bash
   # شغل الملف / Run the file:
   install_requirements.bat
   ```

2. **بناء الملف التنفيذي / Build Executable:**
   ```bash
   # شغل الملف / Run the file:
   build_exe.bat
   ```

### الطريقة الثانية: يدوياً / Method 2: Manual

1. **تثبيت Python (إذا لم يكن مثبت) / Install Python (if not installed):**
   - حمل Python من python.org
   - تأكد من إضافة Python إلى PATH

2. **تثبيت المتطلبات / Install Requirements:**
   ```bash
   pip install -r requirements.txt
   ```

3. **بناء الملف التنفيذي / Build Executable:**
   ```bash
   python build_exe.py
   ```

---

## 📦 التثبيت / Installation

### بعد بناء الملف التنفيذي / After Building the Executable:

1. **انتقل إلى مجلد dist / Navigate to dist folder**
2. **انسخ جميع الملفات إلى المكان المطلوب / Copy all files to desired location**
3. **شغل الملف التنفيذي / Run the executable:**
   - `Maintenance_Management_System.exe`
   - أو استخدم / Or use: `تشغيل_النظام.bat`

---

## 🚀 التشغيل / Running the System

### التشغيل المحلي / Local Usage:
1. شغل `Maintenance_Management_System.exe`
2. سيفتح المتصفح تلقائياً / Browser will open automatically
3. الرابط المحلي / Local URL: `http://127.0.0.1:[PORT]`

### الوصول من أجهزة أخرى / Multi-Device Access:
1. تأكد من أن الجهاز متصل بالشبكة / Ensure device is connected to network
2. اعرف عنوان IP للجهاز / Find the device IP address:
   ```cmd
   ipconfig
   ```
3. من الأجهزة الأخرى، استخدم / From other devices, use:
   ```
   http://[IP_ADDRESS]:[PORT]
   ```

---

## 👥 الاستخدام متعدد المستخدمين / Multi-User Usage

### الميزات / Features:
- ✅ **عدة مستخدمين في نفس الوقت / Multiple simultaneous users**
- ✅ **قاعدة بيانات مشتركة / Shared database**
- ✅ **تحديث فوري للبيانات / Real-time data updates**
- ✅ **أمان البيانات / Data security**

### كيفية الاستخدام / How to Use:
1. **شغل النظام على جهاز واحد (الخادم) / Run system on one device (server)**
2. **شارك عنوان IP مع المستخدمين الآخرين / Share IP address with other users**
3. **كل مستخدم يفتح المتصفح ويدخل الرابط / Each user opens browser and enters URL**

---

## 🔧 استكشاف الأخطاء / Troubleshooting

### مشاكل شائعة / Common Issues:

#### 1. **لا يفتح المتصفح تلقائياً / Browser doesn't open automatically**
- **الحل / Solution**: افتح المتصفح يدوياً واذهب إلى الرابط المعروض

#### 2. **خطأ في المنفذ / Port Error**
- **الحل / Solution**: النظام يختار منفذ متاح تلقائياً

#### 3. **لا يمكن الوصول من أجهزة أخرى / Cannot access from other devices**
- **تحقق من / Check**:
  - جدار الحماية / Firewall settings
  - اتصال الشبكة / Network connection
  - عنوان IP صحيح / Correct IP address

#### 4. **بطء في الأداء / Slow Performance**
- **الحلول / Solutions**:
  - أغلق البرامج غير الضرورية / Close unnecessary programs
  - تأكد من توفر ذاكرة كافية / Ensure sufficient RAM
  - استخدم SSD بدلاً من HDD / Use SSD instead of HDD

---

## 📊 إدارة البيانات / Data Management

### قاعدة البيانات / Database:
- **الملف / File**: `maintenance.db`
- **النوع / Type**: SQLite
- **الموقع / Location**: نفس مجلد التطبيق / Same folder as application

### النسخ الاحتياطي / Backup:
1. **انسخ ملف `maintenance.db` / Copy `maintenance.db` file**
2. **احفظه في مكان آمن / Save in secure location**
3. **للاستعادة: استبدل الملف / To restore: replace the file**

---

## 🔒 الأمان / Security

### توصيات الأمان / Security Recommendations:
- ✅ **استخدم كلمات مرور قوية / Use strong passwords**
- ✅ **قم بنسخ احتياطي منتظم / Regular backups**
- ✅ **حدد الوصول للشبكة حسب الحاجة / Limit network access as needed**
- ✅ **حدث النظام بانتظام / Update system regularly**

---

## 📞 الدعم الفني / Technical Support

### للمساعدة / For Help:
- **الوثائق / Documentation**: راجع هذا الدليل
- **المشاكل الشائعة / Common Issues**: راجع قسم استكشاف الأخطاء
- **الدعم المتقدم / Advanced Support**: تواصل مع فريق التطوير

---

## 📝 ملاحظات مهمة / Important Notes

### التوافق / Compatibility:
- ✅ **Windows 7, 8, 10, 11**
- ✅ **32-bit و 64-bit**
- ✅ **جميع المتصفحات الحديثة / All modern browsers**

### الأداء / Performance:
- **أفضل أداء / Best Performance**: Windows 10/11 مع 4GB RAM
- **مقبول / Acceptable**: Windows 7 مع 2GB RAM
- **عدد المستخدمين / User Count**: حتى 50 مستخدم متزامن / Up to 50 concurrent users

### التحديثات / Updates:
- **تحقق من التحديثات بانتظام / Check for updates regularly**
- **احتفظ بنسخة احتياطية قبل التحديث / Backup before updating**

---

## 🎯 الخلاصة / Summary

نظام إدارة الصيانة هو حل شامل ومتكامل لإدارة أعمال الصيانة، مصمم ليعمل على جميع إصدارات Windows ويدعم عدة مستخدمين في نفس الوقت. النظام سهل التثبيت والاستخدام ولا يتطلب خبرة تقنية متقدمة.

The Maintenance Management System is a comprehensive and integrated solution for managing maintenance operations, designed to work on all Windows versions and support multiple simultaneous users. The system is easy to install and use and does not require advanced technical expertise.
