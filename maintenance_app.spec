# -*- mode: python ; coding: utf-8 -*-

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# Get the current directory
current_dir = os.path.dirname(os.path.abspath(SPEC))

# Collect Flask templates and static files
datas = []
datas += collect_data_files('flask')
datas += [(os.path.join(current_dir, 'templates'), 'templates')]
datas += [(os.path.join(current_dir, 'static'), 'static')]
datas += [(os.path.join(current_dir, 'uploads'), 'uploads')]

# Add database file if it exists
if os.path.exists(os.path.join(current_dir, 'maintenance.db')):
    datas += [(os.path.join(current_dir, 'maintenance.db'), '.')]

# Collect hidden imports
hiddenimports = []
hiddenimports += collect_submodules('flask')
hiddenimports += collect_submodules('werkzeug')
hiddenimports += collect_submodules('jinja2')
hiddenimports += collect_submodules('flask_login')
hiddenimports += [
    'sqlite3',
    'datetime',
    'os',
    'hashlib',
    'secrets',
    'threading',
    'webbrowser',
    'socket',
    'time',
    'configparser',
    'pathlib'
]

# Add config loader
datas += [(os.path.join(current_dir, 'config_loader.py'), '.')]

block_cipher = None

a = Analysis(
    ['run_app.py'],
    pathex=[current_dir],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='Maintenance_Management_System',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # No console window
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='maintenance_icon.ico',  # Custom maintenance icon
    version_file=None,
)
