{% extends "base.html" %}

{% block title %}Gestion des Régions - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Gestion des Régions{% endblock %}

{% block content %}
<div class="container-fluid mt-4 mb-5">
  <!-- Header moderne -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-lg border-0">
        <div class="card-header bg-gradient-info text-white py-4">
          <div class="row align-items-center">
            <div class="col-md-8">
              <div class="d-flex align-items-center">
                <div class="icon-circle bg-white bg-opacity-20 me-3">
                  <i class="fas fa-map-marked-alt text-white"></i>
                </div>
                <div>
                  <h3 class="mb-0 fw-bold">Gestion des Régions</h3>
                  <small class="opacity-75">Organisez vos zones d'intervention par région</small>
                </div>
              </div>
            </div>
            <div class="col-md-4">
              <div class="header-right">
                <div class="btn-group" role="group">
                  <a href="{{ url_for('ajouter_region') }}" class="btn btn-primary btn-sm">
                    <i class="fas fa-plus"></i> Ajouter
                  </a>
                  <button type="button" class="btn btn-light btn-sm" onclick="window.print()">
                    <i class="fas fa-print"></i> Imprimer
                  </button>
                  <button type="button" class="btn btn-success btn-sm" id="exportExcel">
                    <i class="fas fa-file-excel"></i> Excel
                  </button>
                  <button type="button" class="btn btn-info btn-sm" onclick="location.reload()">
                    <i class="fas fa-sync-alt"></i> Actualiser
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Section des filtres -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-sm border-0">
        <div class="card-header bg-gradient-light">
          <h6 class="mb-0 text-dark fw-bold">
            <i class="fas fa-filter me-2 text-info"></i>Filtres de recherche
          </h6>
        </div>
        <div class="card-body">
          <div class="row g-3">
            <div class="col-md-6">
              <div class="form-floating">
                <input type="text" class="form-control" id="searchRegion" placeholder="Rechercher...">
                <label for="searchRegion"><i class="fas fa-search me-1"></i>Rechercher une région</label>
              </div>
            </div>
            <div class="col-md-6">
              <div class="d-flex gap-2">
                <button type="button" class="btn btn-info btn-modern flex-fill" id="applyFilters">
                  <i class="fas fa-search me-1"></i>Filtrer
                </button>
                <button type="button" class="btn btn-outline-secondary btn-modern" id="resetFilters">
                  <i class="fas fa-undo me-1"></i>Reset
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Tableau des régions -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow-lg border-0">
        <div class="card-header bg-gradient-primary text-white">
          <div class="d-flex justify-content-between align-items-center">
            <h6 class="mb-0 fw-bold">
              <i class="fas fa-list me-2"></i>Liste des Régions
            </h6>
            <span class="badge bg-white text-dark">{{ regions|length if regions else 0 }} régions</span>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0" id="regionsTable">
              <thead class="table-light">
                <tr>
                  <th class="fw-bold"><i class="fas fa-hashtag me-1"></i>ID</th>
                  <th class="fw-bold"><i class="fas fa-map-marker-alt me-1"></i>Nom de la région</th>
                  <th class="fw-bold"><i class="fas fa-file-alt me-1"></i>Description</th>
                  <th class="fw-bold"><i class="fas fa-building me-1"></i>Nombre de sites</th>
                  <th class="fw-bold"><i class="fas fa-calendar me-1"></i>Date de création</th>
                  <th class="fw-bold text-center"><i class="fas fa-cogs me-1"></i>Actions</th>
                </tr>
              </thead>
                        <tbody>
                            {% if regions %}
                                {% for region in regions %}
                                <tr>
                                    <td>{{ region.id }}</td>
                                    <td>{{ region.nom }}</td>
                                    <td>{{ region.description }}</td>
                                    <td>
                                        <span class="badge bg-info">{{ region.sites_count }}</span>
                                        {% if region.sites_count > 0 %}
                                        <a href="{{ url_for('sites', region_id=region.id) }}" class="btn btn-sm btn-outline-info">
                                            <i class="fas fa-eye"></i> Voir
                                        </a>
                                        {% endif %}
                                    </td>
                                    <td>{{ region.date_creation }}</td>
                                    <td>
                                        <a href="{{ url_for('modifier_region', id=region.id) }}" class="btn btn-sm btn-warning">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button"
                                                class="btn btn-sm btn-danger"
                                                onclick="confirmDelete('la région {{ region.nom }}', '{{ url_for('supprimer_region', id=region.id) }}')"
                                                title="Supprimer">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="6" class="text-center">Aucune région trouvée</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
  .card {
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  }

  .table th {
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6 !important;
    font-weight: 600 !important;
    padding: 15px 12px !important;
    font-size: 0.9rem !important;
  }

  .table td {
    padding: 15px 12px !important;
    vertical-align: middle !important;
    border-bottom: 1px solid #dee2e6 !important;
  }

  .table-hover tbody tr:hover {
    background-color: rgba(23,162,184,0.05) !important;
  }

  .btn-group .btn {
    margin: 0 1px !important;
    border-radius: 6px !important;
  }

  .badge {
    font-size: 0.75rem !important;
    padding: 6px 10px !important;
    font-weight: 500 !important;
  }

  .text-white-75 {
    color: rgba(255,255,255,0.75) !important;
  }

  .text-white-50 {
    color: rgba(255,255,255,0.5) !important;
  }

  .card-header {
    border-bottom: 1px solid rgba(0,0,0,0.125) !important;
    padding: 1rem 1.25rem !important;
  }

  .card-body {
    padding: 1.25rem !important;
  }

  .bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
  }

  .bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  }

  .bg-gradient-light {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
  }

  @media (max-width: 768px) {
    .btn-group {
      flex-direction: column !important;
    }

    .btn-group .btn {
      margin: 2px 0 !important;
    }

    .table-responsive {
      font-size: 0.85rem !important;
    }

    .card-body {
      padding: 1rem !important;
    }
  }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Export Excel
        document.getElementById('exportExcel').addEventListener('click', function() {
            exportTableToExcel('regionsTable', 'regions');
        });
    });
</script>
{% endblock %}
