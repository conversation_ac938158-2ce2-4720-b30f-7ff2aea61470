<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}{{ company_info.nom if company_info and company_info.nom else 'Système de Gestion de Maintenance' }}{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" integrity="sha512-DTOQO9RWCH3ppGqcWaEA1BIZOC6xxalwEsw9c2QQeAIftl+Vegovlnee1c9QX4TctnWMn13TZye+giMm8e2LwA==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <!-- Font Awesome Fallback -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v6.5.1/css/all.css" crossorigin="anonymous" />

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            important: true,
            corePlugins: {
                preflight: false,
            }
        }
    </script>



    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/print.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/delete-modal.css') }}">

    {% block extra_css %}{% endblock %}

    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f8f9fa !important;
            min-height: 100vh;
        }

        .sidebar {
            min-height: 100vh;
            background: linear-gradient(180deg, #2c3e50 0%, #34495e 100%);
            color: white;
            position: fixed;
            width: 260px;
            transition: all 0.3s ease;
            z-index: 1000;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
        }

        .sidebar-header {
            padding: 25px 20px;
            background: linear-gradient(135deg, #1a2530 0%, #2c3e50 100%);
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-header img {
            width: 120px;
            height: auto;
            max-height: 80px;
            margin-bottom: 15px;
            border-radius: 8px;
            border: 2px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
            object-fit: contain;
            background: rgba(255,255,255,0.1);
            padding: 5px;
        }

        .sidebar-header img:hover {
            transform: scale(1.1);
        }

        .sidebar-header h3 {
            font-size: 16px;
            margin: 0;
            color: white;
            font-weight: 600;
            letter-spacing: 0.5px;
        }

        .sidebar ul.components {
            padding: 15px 0;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar ul li {
            margin: 5px 15px;
            border-radius: 8px;
            overflow: hidden;
        }

        .sidebar ul li a {
            padding: 15px 20px;
            font-size: 14px;
            display: block;
            color: #ecf0f1;
            text-decoration: none;
            transition: all 0.3s ease;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }

        .sidebar ul li a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .sidebar ul li a:hover::before {
            left: 100%;
        }

        .sidebar ul li a:hover {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            transform: translateX(5px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }

        .sidebar ul li a i {
            margin-right: 12px;
            width: 20px;
            text-align: center;
            font-size: 16px;
        }

        .sidebar ul li.active > a {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .content {
            width: calc(100% - 260px);
            margin-left: 260px;
            transition: all 0.3s ease;
            padding: 25px;
            min-height: 100vh;
        }

        .navbar {
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            padding: 20px 25px;
            margin-bottom: 25px;
            border-radius: 15px;
            border: 1px solid rgba(255,255,255,0.8);
            backdrop-filter: blur(10px);
        }

        .navbar .navbar-brand {
            font-weight: 600;
            color: #2c3e50;
        }

        .navbar .user-info {
            display: flex;
            align-items: center;
        }

        .navbar .user-info img {
            width: 35px;
            height: 35px;
            border-radius: 50%;
            margin-right: 10px;
        }

        .navbar .user-info span {
            color: #2c3e50;
            font-weight: 500;
        }

        .navbar .logout-btn {
            margin-left: 15px;
            background: #e74c3c;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            transition: background 0.3s;
        }

        .navbar .logout-btn:hover {
            background: #c0392b;
        }

        .card {
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            margin-bottom: 20px;
        }

        .card-header {
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            padding: 15px 20px;
            font-weight: 600;
            color: #2c3e50;
        }

        .btn-primary {
            background: #3498db;
            border-color: #3498db;
        }

        .btn-primary:hover {
            background: #2980b9;
            border-color: #2980b9;
        }

        .btn-success {
            background: #2ecc71;
            border-color: #2ecc71;
        }

        .btn-success:hover {
            background: #27ae60;
            border-color: #27ae60;
        }

        .btn-danger {
            background: #e74c3c;
            border-color: #e74c3c;
        }

        .btn-danger:hover {
            background: #c0392b;
            border-color: #c0392b;
        }

        .alert {
            border-radius: 5px;
            margin-bottom: 20px;
        }

        @media (max-width: 768px) {
            .sidebar {
                margin-left: -250px;
            }

            .content {
                width: 100%;
                margin-left: 0;
            }

            .sidebar.active {
                margin-left: 0;
            }

            .content.active {
                margin-left: 250px;
                width: calc(100% - 250px);
            }
        }

        /* Unified button styles for all templates - Professional Look */
        .btn-group .btn-sm, .header-right .btn-sm {
            padding: 0.5rem 1rem !important;
            font-size: 0.9rem !important;
            border-radius: 0.375rem !important;
            font-weight: 500 !important;
            transition: all 0.2s ease-in-out !important;
            border: 1px solid transparent !important;
            min-width: 100px !important;
            text-align: center !important;
        }

        .btn-group .btn-light, .header-right .btn-light {
            background-color: #ffffff !important;
            border-color: #dee2e6 !important;
            color: #495057 !important;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1) !important;
        }

        .btn-group .btn-light:hover, .header-right .btn-light:hover {
            background-color: #f8f9fa !important;
            border-color: #adb5bd !important;
            color: #495057 !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 6px rgba(0,0,0,0.15) !important;
        }

        .btn-group .btn-success, .header-right .btn-success {
            background-color: #28a745 !important;
            border-color: #28a745 !important;
            color: #fff !important;
            box-shadow: 0 1px 3px rgba(40,167,69,0.3) !important;
        }

        .btn-group .btn-success:hover, .header-right .btn-success:hover {
            background-color: #218838 !important;
            border-color: #1e7e34 !important;
            color: #fff !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 6px rgba(40,167,69,0.4) !important;
        }

        .btn-group .btn-info, .header-right .btn-info {
            background-color: #17a2b8 !important;
            border-color: #17a2b8 !important;
            color: #fff !important;
            box-shadow: 0 1px 3px rgba(23,162,184,0.3) !important;
        }

        .btn-group .btn-info:hover, .header-right .btn-info:hover {
            background-color: #138496 !important;
            border-color: #117a8b !important;
            color: #fff !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 6px rgba(23,162,184,0.4) !important;
        }

        .btn-group .btn-primary, .header-right .btn-primary {
            background-color: #007bff !important;
            border-color: #007bff !important;
            color: #fff !important;
            box-shadow: 0 1px 3px rgba(0,123,255,0.3) !important;
        }

        .btn-group .btn-primary:hover, .header-right .btn-primary:hover {
            background-color: #0056b3 !important;
            border-color: #004085 !important;
            color: #fff !important;
            transform: translateY(-1px) !important;
            box-shadow: 0 2px 6px rgba(0,123,255,0.4) !important;
        }

        /* Header layout improvements - 2x2 Grid Layout */
        .header-right {
            margin-left: auto !important;
            display: flex !important;
            align-items: center !important;
            gap: 0.5rem !important;
            flex-shrink: 0 !important;
            min-width: fit-content !important;
        }

        /* Ensure maximum separation between text and buttons */
        .card-header .d-flex,
        .card-header .row {
            width: 100% !important;
        }

        .card-header .col-md-8,
        .card-header .header-left {
            flex: 1 !important;
            max-width: none !important;
        }

        .card-header .col-md-4 {
            flex: 0 0 auto !important;
            max-width: none !important;
            width: auto !important;
            margin-left: auto !important;
        }

        .header-right .btn-group {
            display: grid !important;
            grid-template-columns: 1fr 1fr !important;
            grid-template-rows: 1fr 1fr !important;
            gap: 0.25rem !important;
            width: auto !important;
        }

        /* Ensure buttons fill the grid properly */
        .header-right .btn-group .btn {
            width: 100% !important;
            min-width: 85px !important;
            text-align: center !important;
        }

        /* Responsive adjustments for 2x2 grid */
        @media (max-width: 992px) {
            .header-right .btn-group {
                grid-template-columns: 1fr 1fr !important;
                grid-template-rows: 1fr 1fr !important;
                gap: 0.3rem !important;
                max-width: 200px !important;
            }

            .btn-group .btn-sm, .header-right .btn-sm {
                min-width: 80px !important;
                font-size: 0.8rem !important;
                padding: 0.4rem 0.6rem !important;
            }
        }

        @media (max-width: 768px) {
            .header-right {
                flex-direction: column !important;
                align-items: flex-end !important;
                gap: 0.5rem !important;
            }

            .header-right .btn-group {
                grid-template-columns: 1fr 1fr !important;
                grid-template-rows: 1fr 1fr !important;
                gap: 0.4rem !important;
                max-width: 180px !important;
            }

            .btn-group .btn-sm, .header-right .btn-sm {
                min-width: 75px !important;
                font-size: 0.75rem !important;
                padding: 0.35rem 0.5rem !important;
            }
        }

        @media (max-width: 576px) {
            .header-right {
                width: 100% !important;
                align-items: center !important;
            }

            .header-right .btn-group {
                grid-template-columns: 1fr 1fr !important;
                grid-template-rows: 1fr 1fr !important;
                gap: 0.5rem !important;
                width: 100% !important;
                max-width: none !important;
            }

            .btn-group .btn-sm, .header-right .btn-sm {
                width: 100% !important;
                min-width: auto !important;
                font-size: 0.8rem !important;
                padding: 0.5rem 0.75rem !important;
                border-radius: 0.375rem !important;
            }
        }

        /* Additional improvements for professional look */
        .card-header .header-right {
            flex-shrink: 0 !important;
        }

        .card-header .d-flex {
            width: 100% !important;
            align-items: center !important;
        }

        /* Ensure consistent spacing */
        .header-right > * {
            flex-shrink: 0;
        }

        /* Maximum separation between text and buttons */
        .header-left {
            flex: 1 !important;
            min-width: 0 !important;
        }

        .header-right {
            margin-left: 2rem !important;
            flex: 0 0 auto !important;
        }

        /* Ensure proper spacing in card headers */
        .card-header .d-flex.justify-content-between {
            gap: 2rem !important;
        }

</style>
</head>
<body>
    {% if session.get('utilisateur_id') %}
    <div class="wrapper">
        <!-- Sidebar -->
        <nav id="sidebar" class="sidebar">
            <div class="sidebar-header">
                {% if company_info and company_info.logo %}
                    <img src="{{ url_for('static', filename='uploads/' + company_info.logo) }}" alt="Logo {{ company_info.nom }}" onerror="this.src='{{ url_for('static', filename='images/logo.png') }}'">
                {% else %}
                    <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Logo">
                {% endif %}
                <h3>{{ 'Gestion de Maintenance' if company_info and company_info.nom == 'MAXAFFAIRE' else (company_info.nom if company_info and company_info.nom else 'Gestion de Maintenance') }}</h3>
            </div>

            <ul class="list-unstyled components">
                <li class="{{ 'active' if request.path == '/dashboard' else '' }}">
                    <a href="{{ url_for('dashboard') }}">
                        <i class="fas fa-chart-line"></i> Tableau de bord
                    </a>
                </li>
                <li class="{{ 'active' if request.path == '/carte' else '' }}">
                    <a href="{{ url_for('carte') }}">
                        <i class="fas fa-map-marked-alt"></i> Carte du Maroc
                    </a>
                </li>
                <li class="{{ 'active' if request.path == '/societe' else '' }}">
                    <a href="{{ url_for('societe') }}">
                        <i class="fas fa-building"></i> Informations Société
                    </a>
                </li>
                <li class="{{ 'active' if '/marches' in request.path else '' }}">
                    <a href="{{ url_for('marches') }}">
                        <i class="fas fa-handshake"></i> Marchés
                    </a>
                </li>
                <li class="{{ 'active' if '/interventions' in request.path else '' }}">
                    <a href="{{ url_for('interventions') }}">
                        <i class="fas fa-wrench"></i> Interventions
                    </a>
                </li>
                <li class="{{ 'active' if '/reclamations' in request.path else '' }}">
                    <a href="{{ url_for('reclamations') }}">
                        <i class="fas fa-exclamation-triangle"></i> Réclamations
                    </a>
                </li>
                <li class="{{ 'active' if '/regions' in request.path else '' }}">
                    <a href="{{ url_for('regions') }}">
                        <i class="fas fa-map-marker-alt"></i> Régions
                    </a>
                </li>
                <li class="{{ 'active' if '/sites' in request.path else '' }}">
                    <a href="{{ url_for('sites') }}">
                        <i class="fas fa-building"></i> Sites
                    </a>
                </li>
                {% if session.get('role') == 'admin' %}
                <li class="{{ 'active' if '/utilisateurs' in request.path else '' }}">
                    <a href="{{ url_for('utilisateurs') }}">
                        <i class="fas fa-users-cog"></i> Utilisateurs
                    </a>
                </li>
                <li class="{{ 'active' if '/logs' in request.path else '' }}">
                    <a href="{{ url_for('logs_utilisateurs') }}">
                        <i class="fas fa-clipboard-list"></i> Journal des Activités
                    </a>
                </li>
                <li class="{{ 'active' if '/database-management' in request.path else '' }}">
                    <a href="{{ url_for('database_management') }}">
                        <i class="fas fa-database"></i> Gestion Base de Données
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>

        <!-- Page Content -->
        <div class="content">
            <nav class="navbar">
                <div class="container-fluid">
                    <button type="button" id="sidebarCollapse" class="btn btn-primary d-md-none">
                        <i class="fas fa-bars"></i>
                    </button>
                    <span class="navbar-brand">{% block page_title %}Tableau de bord{% endblock %}</span>
                    <div class="user-info">
                        <img src="{{ url_for('static', filename='images/user.png') }}" alt="User">
                        <span>{{ session.get('nom_complet') }}</span>
                        <a href="{{ url_for('change_password') }}" class="btn btn-sm btn-outline-primary me-2" title="Changer le mot de passe">
                            <i class="fas fa-key"></i>
                        </a>
                        <a href="{{ url_for('logout') }}" class="btn btn-sm logout-btn">
                            <i class="fas fa-sign-out-alt"></i> Déconnexion
                        </a>
                    </div>
                </div>
            </nav>

            <div class="container-fluid">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </div>
        </div>
    </div>
    {% else %}
        {% block auth_content %}{% endblock %}
    {% endif %}

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- SheetJS - Excel Export Library -->
    <script src="https://cdn.jsdelivr.net/npm/xlsx@0.18.5/dist/xlsx.full.min.js"></script>

    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/delete-modal.js') }}"></script>
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
    <script src="{{ url_for('static', filename='js/export.js') }}"></script>
    <script src="{{ url_for('static', filename='js/import-excel.js') }}"></script>
    <script src="{{ url_for('static', filename='js/enhanced-delete.js') }}"></script>
    <script src="{{ url_for('static', filename='js/form-linking.js') }}"></script>

    {% block extra_js %}{% endblock %}

    <script>
        // دالة confirmDelete كحل احتياطي
        window.confirmDelete = window.confirmDelete || function(itemName, deleteUrl) {
            console.log('Fallback confirmDelete called with:', itemName, deleteUrl);

            // إنشاء حوار تأكيد بسيط
            const confirmed = confirm(`Êtes-vous sûr de vouloir supprimer ${itemName} ?\n\nCette action est irréversible !`);

            if (confirmed) {
                // إضافة مؤشر تحميل
                const loadingDiv = document.createElement('div');
                loadingDiv.innerHTML = `
                    <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                                background: rgba(0,0,0,0.8); z-index: 10000;
                                display: flex; align-items: center; justify-content: center;">
                        <div style="background: white; padding: 30px; border-radius: 15px; text-align: center;">
                            <i class="fas fa-spinner fa-spin fa-2x text-danger mb-3"></i>
                            <div>Suppression en cours...</div>
                        </div>
                    </div>
                `;
                document.body.appendChild(loadingDiv);

                // الانتقال إلى رابط الحذف
                window.location.href = deleteUrl;
            }
        };

        $(document).ready(function() {
            // Toggle sidebar on mobile
            $('#sidebarCollapse').on('click', function() {
                $('#sidebar, .content').toggleClass('active');
            });

            // Debug: Vérifier le chargement des scripts
            console.log('✅ Base scripts loaded');
            console.log('📚 jQuery available:', typeof $);
            console.log('🎨 Bootstrap available:', typeof bootstrap);

            // Vérifier Font Awesome
            setTimeout(function() {
                const testIcon = $('<i class="fas fa-home" style="position: absolute; left: -9999px;"></i>');
                $('body').append(testIcon);

                const computedStyle = window.getComputedStyle(testIcon[0], ':before');
                const content = computedStyle.getPropertyValue('content');

                if (content === 'none' || content === '' || content === 'normal') {
                    console.warn('⚠️ Font Awesome ne semble pas être chargé correctement');
                    // Charger Font Awesome en fallback
                    $('<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css">').appendTo('head');
                } else {
                    console.log('✅ Font Awesome chargé avec succès');
                }

                testIcon.remove();
            }, 1000);

            // Vérifier le chargement des systèmes محسنة
            setTimeout(() => {
                console.log('🗑️ Enhanced Delete System:', typeof window.deleteSystem);
                console.log('🔗 Enhanced Form Linking:', typeof window.formLinking);

                if (window.formLinking && window.formLinking.isInitialized) {
                    console.log('✅ نظام ربط النماذج جاهز ومهيأ');
                } else {
                    console.log('⚠️ نظام ربط النماذج لم يتم تهيئته بعد');
                }
            }, 500);
        });
    </script>
    <script src="{{ url_for('static', filename='js/simple-delete.js') }}"></script>
</body>
</html>
