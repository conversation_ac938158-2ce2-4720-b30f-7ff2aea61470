#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تحويل الصورة إلى أيقونة .ico
"""

import os
from PIL import Image

def create_icon():
    """تحويل الصورة إلى أيقونة"""
    
    try:
        # مسار الصورة الأصلية
        input_image = "static/images/GESTION DES MAINTENANCES.png"
        
        # مسار الأيقونة الجديدة
        output_icon = "maintenance_icon.ico"
        
        print(f"🔄 تحويل الصورة إلى أيقونة...")
        print(f"📁 المصدر: {input_image}")
        print(f"📁 الهدف: {output_icon}")
        
        # فتح الصورة
        if not os.path.exists(input_image):
            print(f"❌ الصورة غير موجودة: {input_image}")
            return False
        
        # فتح وتحويل الصورة
        with Image.open(input_image) as img:
            # تحويل إلى RGBA إذا لم تكن كذلك
            if img.mode != 'RGBA':
                img = img.convert('RGBA')
            
            # إنشاء أحجام مختلفة للأيقونة
            sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
            
            # حفظ كأيقونة مع أحجام متعددة
            img.save(output_icon, format='ICO', sizes=sizes)
        
        print(f"✅ تم إنشاء الأيقونة بنجاح: {output_icon}")
        
        # فحص حجم الملف
        if os.path.exists(output_icon):
            size = os.path.getsize(output_icon)
            print(f"📊 حجم الأيقونة: {size:,} بايت ({size/1024:.1f} KB)")
        
        return True
        
    except ImportError:
        print("❌ مكتبة Pillow غير مثبتة")
        print("💡 لتثبيتها: pip install Pillow")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في تحويل الصورة: {e}")
        return False

def create_simple_icon():
    """إنشاء أيقونة بسيطة إذا فشل التحويل"""
    
    try:
        from PIL import Image, ImageDraw, ImageFont
        
        print("🎨 إنشاء أيقونة بسيطة...")
        
        # إنشاء صورة جديدة
        size = 256
        img = Image.new('RGBA', (size, size), (255, 255, 255, 0))
        draw = ImageDraw.Draw(img)
        
        # رسم دائرة حمراء
        margin = 20
        draw.ellipse([margin, margin, size-margin, size-margin], 
                    fill=(220, 53, 69, 255), outline=(108, 117, 125, 255), width=3)
        
        # رسم رمز الصيانة (مفتاح ربط)
        center_x, center_y = size // 2, size // 2
        
        # رسم مفتاح ربط بسيط
        tool_color = (255, 255, 255, 255)
        
        # الجزء الأفقي
        draw.rectangle([center_x - 40, center_y - 8, center_x + 40, center_y + 8], fill=tool_color)
        
        # الجزء العمودي
        draw.rectangle([center_x - 8, center_y - 40, center_x + 8, center_y + 40], fill=tool_color)
        
        # النقاط في الأطراف
        for x, y in [(center_x - 30, center_y - 30), (center_x + 30, center_y - 30), 
                     (center_x - 30, center_y + 30), (center_x + 30, center_y + 30)]:
            draw.ellipse([x - 6, y - 6, x + 6, y + 6], fill=tool_color)
        
        # حفظ الأيقونة
        sizes = [(16, 16), (32, 32), (48, 48), (64, 64), (128, 128), (256, 256)]
        img.save("maintenance_icon.ico", format='ICO', sizes=sizes)
        
        print("✅ تم إنشاء أيقونة بسيطة بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الأيقونة البسيطة: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🎯 إنشاء أيقونة نظام إدارة الصيانة")
    print("=" * 50)
    
    # محاولة تحويل الصورة الأصلية
    if create_icon():
        print("\n🎉 تم إنشاء الأيقونة من الصورة الأصلية!")
    else:
        print("\n⚠️ فشل تحويل الصورة الأصلية، سيتم إنشاء أيقونة بسيطة...")
        if create_simple_icon():
            print("🎉 تم إنشاء أيقونة بسيطة!")
        else:
            print("❌ فشل في إنشاء أي أيقونة")
            return False
    
    print("\n📋 الخطوة التالية:")
    print("   سيتم تحديث ملف Inno Setup لاستخدام الأيقونة الجديدة")
    
    return True

if __name__ == "__main__":
    main()
