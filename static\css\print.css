/* CSS optimisé pour l'impression - Version améliorée */

@media print {
    /* Masquer les éléments non nécessaires à l'impression */
    .sidebar,
    .navbar,
    .btn-group,
    .btn,
    button,
    .card-header .btn-group,
    .pagination,
    .alert,
    .dropdown,
    .modal,
    .tooltip,
    .popover,
    .no-print,
    .breadcrumb,
    .nav-tabs,
    .nav-pills,
    .header-right,
    .card-header .d-flex .col-md-4 {
        display: none !important;
    }

    /* Réinitialiser les marges et padding */
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
    }

    body {
        margin: 0;
        padding: 8mm;
        font-size: 12px;
        line-height: 1.5;
        color: #000 !important;
        background: white !important;
        font-family: 'Arial', 'Helvetica', sans-serif;
    }

    /* Optimisation pour format A4 */
    @page {
        size: A4;
        margin: 12mm;
    }

    /* Ajuster le contenu principal */
    .content {
        width: 100% !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    .container-fluid {
        padding: 0 !important;
    }

    /* En-tête d'impression */
    /* En-têtes améliorés */
    .print-header {
        display: block !important;
        text-align: center;
        margin-bottom: 25px;
        border-bottom: 3px solid #000;
        padding-bottom: 15px;
        page-break-after: avoid;
    }

    .print-header h1 {
        font-size: 20px;
        margin: 0 0 10px 0;
        font-weight: bold;
        color: #000;
    }

    .print-header h2 {
        font-size: 16px;
        margin: 5px 0;
        color: #333;
        font-weight: normal;
    }

    /* Titres généraux */
    h1, h2, h3, h4, h5, h6 {
        color: #000 !important;
        page-break-after: avoid !important;
        margin-top: 20px !important;
        margin-bottom: 10px !important;
        font-weight: bold !important;
    }

    h1 {
        font-size: 20px !important;
        border-bottom: 2px solid #000 !important;
        padding-bottom: 8px !important;
        text-align: center !important;
    }

    h2 {
        font-size: 16px !important;
        border-bottom: 1px solid #666 !important;
        padding-bottom: 5px !important;
        margin-top: 25px !important;
    }

    h3 {
        font-size: 14px !important;
        color: #333 !important;
        margin-top: 20px !important;
    }

    h4, h5, h6 {
        font-size: 12px !important;
        margin-top: 15px !important;
    }

    /* Cartes et conteneurs */
    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        margin-bottom: 15px;
        page-break-inside: avoid;
    }

    .card-header {
        background: #f8f9fa !important;
        border-bottom: 1px solid #ddd !important;
        padding: 8px 12px !important;
        font-weight: bold;
    }

    .card-body {
        padding: 12px !important;
    }

    /* Tableaux améliorés pour l'impression */
    .table {
        font-size: 11px !important;
        border-collapse: collapse !important;
        width: 100% !important;
        margin-bottom: 20px !important;
        border: 2px solid #333 !important;
    }

    .table th,
    .table td {
        border: 1px solid #666 !important;
        padding: 8px 6px !important;
        text-align: left !important;
        vertical-align: top !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
    }

    .table th {
        background: #f0f0f0 !important;
        font-weight: bold !important;
        font-size: 11px !important;
        text-align: center !important;
        border-bottom: 2px solid #333 !important;
    }

    .table-striped tbody tr:nth-of-type(odd) {
        background: #f9f9f9 !important;
    }

    /* Largeurs spécifiques pour les colonnes */
    .table th:first-child,
    .table td:first-child {
        width: 12% !important;
        min-width: 60px !important;
    }

    .table th:nth-child(2),
    .table td:nth-child(2) {
        width: 15% !important;
        min-width: 80px !important;
    }

    /* Ajustements pour les longues cellules */
    .table td {
        max-width: 120px !important;
        white-space: normal !important;
        word-break: break-word !important;
    }

    /* Badges et labels */
    .badge {
        border: 1px solid #000 !important;
        background: white !important;
        color: #000 !important;
        padding: 2px 4px;
        font-size: 8px !important;
    }

    /* Formulaires */
    .form-control,
    .form-select {
        border: 1px solid #000 !important;
        background: white !important;
        font-size: 10px !important;
    }

    /* Grilles */
    .row {
        margin: 0 !important;
    }

    .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
    .col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12 {
        float: left;
        padding: 0 5px;
    }

    .col-md-1 { width: 8.33%; }
    .col-md-2 { width: 16.66%; }
    .col-md-3 { width: 25%; }
    .col-md-4 { width: 33.33%; }
    .col-md-5 { width: 41.66%; }
    .col-md-6 { width: 50%; }
    .col-md-7 { width: 58.33%; }
    .col-md-8 { width: 66.66%; }
    .col-md-9 { width: 75%; }
    .col-md-10 { width: 83.33%; }
    .col-md-11 { width: 91.66%; }
    .col-md-12 { width: 100%; }

    /* Saut de page */
    .page-break {
        page-break-before: always;
    }

    .page-break-inside {
        page-break-inside: avoid;
    }

    /* Pied de page d'impression */
    .print-footer {
        display: block !important;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        text-align: center;
        font-size: 8px;
        color: #666;
        border-top: 1px solid #ddd;
        padding: 5px;
        background: white;
    }

    /* Informations de la société */
    .company-info {
        text-align: center;
        margin-bottom: 20px;
        font-size: 10px;
    }

    .company-logo {
        max-width: 120px !important;
        max-height: 100px !important;
        width: auto !important;
        height: auto !important;
    }

    /* Statistiques */
    .stats-print {
        display: flex !important;
        justify-content: space-around;
        margin: 15px 0;
        border: 1px solid #ddd;
        padding: 10px;
    }

    .stat-item {
        text-align: center;
        flex: 1;
    }

    .stat-number {
        font-size: 16px;
        font-weight: bold;
    }

    .stat-label {
        font-size: 10px;
        color: #666;
    }

    /* Optimisations spécifiques */
    .text-truncate {
        white-space: normal !important;
        overflow: visible !important;
        text-overflow: clip !important;
    }

    /* Couleurs pour l'impression */
    .bg-primary { background: #e3f2fd !important; }
    .bg-success { background: #e8f5e8 !important; }
    .bg-warning { background: #fff3cd !important; }
    .bg-danger { background: #f8d7da !important; }
    .bg-info { background: #d1ecf1 !important; }

    .text-primary { color: #0066cc !important; }
    .text-success { color: #006600 !important; }
    .text-warning { color: #cc6600 !important; }
    .text-danger { color: #cc0000 !important; }
    .text-info { color: #0099cc !important; }
}

/* Styles pour l'aperçu avant impression */
@media screen {
    .print-preview {
        background: #f5f5f5;
        padding: 20px;
    }

    .print-preview .page {
        background: white;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        margin: 0 auto 20px;
        padding: 20px;
        max-width: 210mm;
        min-height: 297mm;
    }
}
