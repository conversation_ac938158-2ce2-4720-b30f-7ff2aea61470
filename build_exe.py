#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
سكريپت بناء الملف التنفيذي
"""

import os
import sys
import subprocess
import shutil
import platform

def check_requirements():
    """فحص المتطلبات"""
    print("🔍 فحص المتطلبات...")
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller {PyInstaller.__version__} مثبت")
    except ImportError:
        print("❌ PyInstaller غير مثبت")
        print("📦 تثبيت PyInstaller...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ تم تثبيت PyInstaller")
    
    # فحص الملفات المطلوبة
    required_files = [
        'app.py',
        'launcher.py',
        'maintenance_app.spec',
        'templates',
        'static'
    ]
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file} موجود")
        else:
            print(f"❌ {file} غير موجود")
            return False
    
    return True

def clean_build():
    """تنظيف ملفات البناء السابقة"""
    print("🧹 تنظيف ملفات البناء السابقة...")
    
    dirs_to_clean = ['build', 'dist', '__pycache__']
    
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🗑️ تم حذف {dir_name}")

def build_executable():
    """بناء الملف التنفيذي"""
    print("🔨 بناء الملف التنفيذي...")
    
    # تحديد معمارية النظام
    arch = platform.machine().lower()
    is_64bit = platform.architecture()[0] == '64bit'
    
    print(f"💻 النظام: {platform.system()} {platform.release()}")
    print(f"🏗️ المعمارية: {arch} ({'64-bit' if is_64bit else '32-bit'})")
    
    # بناء الملف التنفيذي
    try:
        cmd = [
            'pyinstaller',
            '--clean',
            '--noconfirm',
            'maintenance_app.spec'
        ]
        
        print(f"⚙️ تشغيل الأمر: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تم بناء الملف التنفيذي بنجاح!")
            return True
        else:
            print(f"❌ فشل في البناء:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ خطأ في البناء: {e}")
        return False

def create_installer_files():
    """إنشاء ملفات التثبيت"""
    print("📦 إنشاء ملفات التثبيت...")
    
    # إنشاء مجلد التوزيع
    dist_dir = "dist"
    if not os.path.exists(dist_dir):
        os.makedirs(dist_dir)
    
    # إنشاء ملف README
    readme_content = """
# نظام إدارة الصيانة
## Maintenance Management System

### متطلبات النظام:
- Windows 7, 10, 11 (32-bit أو 64-bit)
- ذاكرة: 2 GB RAM (الحد الأدنى)
- مساحة القرص: 100 MB

### التثبيت:
1. انسخ مجلد التطبيق إلى أي مكان في الحاسوب
2. شغل ملف "Maintenance_Management_System.exe"
3. سيفتح المتصفح تلقائياً على الرابط المحلي

### الاستخدام:
- يمكن لعدة مستخدمين الوصول للنظام في نفس الوقت
- استخدم الرابط: http://[IP_ADDRESS]:PORT للوصول من أجهزة أخرى
- البيانات محفوظة في ملف maintenance.db

### الدعم الفني:
- للمساعدة أو الاستفسارات، تواصل مع فريق التطوير

### إصدار: 1.0.0
### تاريخ البناء: """ + str(subprocess.check_output(['date', '/t'], shell=True, text=True).strip()) + """
"""
    
    with open(os.path.join(dist_dir, "README.txt"), "w", encoding="utf-8") as f:
        f.write(readme_content)
    
    # إنشاء ملف batch للتشغيل السريع
    batch_content = """@echo off
title نظام إدارة الصيانة
echo.
echo ========================================
echo    نظام إدارة الصيانة
echo    Maintenance Management System
echo ========================================
echo.
echo جاري تشغيل النظام...
echo Starting the system...
echo.

"Maintenance_Management_System.exe"

if errorlevel 1 (
    echo.
    echo ❌ حدث خطأ في تشغيل النظام
    echo ❌ Error starting the system
    echo.
    pause
)
"""
    
    with open(os.path.join(dist_dir, "تشغيل_النظام.bat"), "w", encoding="utf-8") as f:
        f.write(batch_content)
    
    print("✅ تم إنشاء ملفات التثبيت")

def main():
    """الدالة الرئيسية"""
    print("🚀 بناء نظام إدارة الصيانة كملف تنفيذي")
    print("=" * 50)
    
    # فحص المتطلبات
    if not check_requirements():
        print("❌ فشل في فحص المتطلبات")
        return False
    
    # تنظيف ملفات البناء السابقة
    clean_build()
    
    # بناء الملف التنفيذي
    if not build_executable():
        print("❌ فشل في بناء الملف التنفيذي")
        return False
    
    # إنشاء ملفات التثبيت
    create_installer_files()
    
    print("\n🎉 تم بناء النظام بنجاح!")
    print("📁 الملفات موجودة في مجلد 'dist'")
    print("💻 يمكن تشغيل النظام على Windows 7, 10, 11 (32/64 bit)")
    print("👥 يدعم عدة مستخدمين في نفس الوقت")
    print("🌐 للوصول من أجهزة أخرى: http://[IP]:PORT")
    
    return True

if __name__ == "__main__":
    try:
        success = main()
        if success:
            input("\n✅ اضغط Enter للخروج...")
        else:
            input("\n❌ اضغط Enter للخروج...")
    except KeyboardInterrupt:
        print("\n⏹️ تم إلغاء العملية")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
