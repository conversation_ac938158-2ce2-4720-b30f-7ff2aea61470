#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

print("🔍 اختبار بسيط...")

try:
    # اختبار الصفحة الرئيسية
    response = requests.get('http://127.0.0.1:5000/')
    print(f"Homepage: {response.status_code}")
    
    # اختبار صفحة تسجيل الدخول
    response = requests.get('http://127.0.0.1:5000/login')
    print(f"Login page: {response.status_code}")
    
    # اختبار API check-login
    response = requests.get('http://127.0.0.1:5000/api/check-login')
    print(f"Check login: {response.status_code}")
    
    # اختبار route استيراد المواقع بدون تسجيل دخول
    response = requests.post('http://127.0.0.1:5000/api/import/sites', 
                           json={'data': [{'test': 'data'}]})
    print(f"Import sites (no login): {response.status_code}")
    print(f"Response: {response.text[:100]}")
    
except Exception as e:
    print(f"خطأ: {e}")

print("انتهى الاختبار")
