{"folders": [{"path": "."}], "settings": {"python.defaultInterpreter": "./.venv/Scripts/python.exe", "python.pythonPath": "./.venv/Scripts/python.exe", "python.terminal.activateEnvironment": true, "python.analysis.typeCheckingMode": "basic", "python.analysis.autoImportCompletions": true, "python.analysis.extraPaths": ["./.venv/Lib/site-packages"], "files.associations": {"*.py": "python"}, "python.formatting.provider": "black", "pylance.insidersChannel": "off"}, "extensions": {"recommendations": ["ms-python.python", "ms-python.pylance", "ms-python.flake8", "ms-python.black-formatter"]}}