#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء قاعدة بيانات فارغة ونظيفة
"""

import sqlite3
import os
import shutil
from datetime import datetime

def backup_existing_database():
    """عمل نسخة احتياطية من قاعدة البيانات الحالية"""
    if os.path.exists('maintenance.db'):
        backup_name = f'maintenance_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        shutil.copy2('maintenance.db', backup_name)
        print(f"✅ تم حفظ نسخة احتياطية: {backup_name}")
        return backup_name
    return None

def create_clean_database():
    """إنشاء قاعدة بيانات فارغة ونظيفة"""
    
    # حذف قاعدة البيانات الحالية إذا كانت موجودة
    if os.path.exists('maintenance.db'):
        os.remove('maintenance.db')
        print("🗑️ تم حذف قاعدة البيانات القديمة")
    
    # إنشاء قاعدة بيانات جديدة
    conn = sqlite3.connect('maintenance.db')
    cursor = conn.cursor()
    
    print("🔧 إنشاء الجداول...")
    
    # جدول المستخدمين (مع مستخدم admin فقط)
    cursor.execute('''
        CREATE TABLE users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            role TEXT DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إضافة مستخدم admin فقط
    cursor.execute('''
        INSERT INTO users (username, password, role) 
        VALUES ('admin', 'admin123', 'admin')
    ''')
    
    # جدول معلومات الشركة (فارغ)
    cursor.execute('''
        CREATE TABLE company_info (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT,
            address TEXT,
            phone TEXT,
            email TEXT,
            logo_path TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول الأسواق/العقود (فارغ)
    cursor.execute('''
        CREATE TABLE marches (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            numero_marche TEXT,
            objet_marche TEXT,
            delai_execution TEXT,
            montant REAL,
            date_debut DATE,
            date_fin DATE,
            statut TEXT DEFAULT 'en_cours',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول التدخلات (فارغ)
    cursor.execute('''
        CREATE TABLE interventions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            type_intervention TEXT,
            numero_marche TEXT,
            objet_marche TEXT,
            delai_execution TEXT,
            semestre TEXT,
            date_intervention DATE,
            description TEXT,
            statut TEXT DEFAULT 'planifie',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول الشكاوى (فارغ)
    cursor.execute('''
        CREATE TABLE reclamations (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            numero_reclamation TEXT,
            type_reclamation TEXT,
            description TEXT,
            date_reclamation DATE,
            statut TEXT DEFAULT 'ouverte',
            priorite TEXT DEFAULT 'normale',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول المناطق (فارغ)
    cursor.execute('''
        CREATE TABLE regions (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom_region TEXT NOT NULL,
            description TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول المواقع (فارغ)
    cursor.execute('''
        CREATE TABLE sites (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom_site TEXT NOT NULL,
            region_id INTEGER,
            adresse TEXT,
            coordonnees TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (region_id) REFERENCES regions (id)
        )
    ''')
    
    # جدول تفاصيل المطافئ (فارغ)
    cursor.execute('''
        CREATE TABLE extincteur_details (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            intervention_id INTEGER,
            numero_extincteur TEXT,
            type_extincteur TEXT,
            capacite TEXT,
            date_fabrication DATE,
            date_derniere_verification DATE,
            prochaine_verification DATE,
            etat TEXT,
            observations TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (intervention_id) REFERENCES interventions (id)
        )
    ''')
    
    # جدول أنشطة المستخدمين (فارغ)
    cursor.execute('''
        CREATE TABLE user_activities (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT,
            activity_type TEXT,
            description TEXT,
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # جدول النسخ الاحتياطية (فارغ)
    cursor.execute('''
        CREATE TABLE backup_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            backup_type TEXT,
            file_path TEXT,
            file_size INTEGER,
            status TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # حفظ التغييرات
    conn.commit()
    conn.close()
    
    print("✅ تم إنشاء قاعدة بيانات فارغة ونظيفة")
    print("👤 المستخدم الوحيد: admin / admin123")

def main():
    """الدالة الرئيسية"""
    
    print("🧹 تنظيف قاعدة البيانات")
    print("🧹 Database Cleanup")
    print("=" * 50)
    
    # عمل نسخة احتياطية
    backup_file = backup_existing_database()
    
    # تأكيد من المستخدم
    print("\n⚠️ تحذير: سيتم حذف جميع البيانات الحالية!")
    print("⚠️ Warning: All current data will be deleted!")
    
    if backup_file:
        print(f"✅ تم حفظ نسخة احتياطية: {backup_file}")
        print(f"✅ Backup saved: {backup_file}")
    
    confirm = input("\nهل تريد المتابعة؟ (y/n): ").strip().lower()
    
    if confirm in ['y', 'yes', 'نعم']:
        # إنشاء قاعدة بيانات فارغة
        create_clean_database()
        
        print("\n🎉 تم تنظيف قاعدة البيانات بنجاح!")
        print("🎉 Database cleaned successfully!")
        print("\n📋 الآن يمكنك:")
        print("📋 Now you can:")
        print("   1. تشغيل البرنامج")
        print("   1. Run the application")
        print("   2. تسجيل الدخول بـ admin/admin123")
        print("   2. Login with admin/admin123")
        print("   3. إضافة بياناتك الخاصة")
        print("   3. Add your own data")
        
        if backup_file:
            print(f"\n💾 لاستعادة البيانات القديمة:")
            print(f"💾 To restore old data:")
            print(f"   انسخ {backup_file} إلى maintenance.db")
            print(f"   Copy {backup_file} to maintenance.db")
    
    else:
        print("\n❌ تم إلغاء العملية")
        print("❌ Operation cancelled")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
