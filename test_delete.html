<!DOCTYPE html>
<html>
<head>
    <title>اختبار أزرار الحذف</title>
    <meta charset="utf-8">
    <script>
        console.log('🚨 بدء اختبار أزرار الحذف...');
        
        function confirmDelete(itemName, deleteUrl) {
            console.log('🚨 confirmDelete called:', itemName, deleteUrl);
            alert('تم استدعاء confirmDelete بنجاح!\nالعنصر: ' + itemName + '\nالرابط: ' + deleteUrl);
            return false;
        }
        
        window.onload = function() {
            console.log('🚨 الصفحة محملة، confirmDelete متاح:', typeof confirmDelete);
        };
    </script>
</head>
<body>
    <h1>اختبار أزرار الحذف</h1>
    
    <h2>اختبار 1: زر بسيط</h2>
    <button onclick="confirmDelete('اختبار 1', '/test/delete/1')">حذف اختبار 1</button>
    
    <h2>اختبار 2: زر مع كلاس</h2>
    <button class="btn btn-danger" onclick="confirmDelete('اختبار 2', '/test/delete/2')">حذف اختبار 2</button>
    
    <h2>اختبار 3: رابط</h2>
    <a href="#" onclick="confirmDelete('اختبار 3', '/test/delete/3'); return false;">حذف اختبار 3</a>
    
    <script>
        console.log('🚨 نهاية الصفحة، جميع العناصر محملة');
    </script>
</body>
</html>