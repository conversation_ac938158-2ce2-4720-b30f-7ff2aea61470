#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask-WTF Forms pour l'application
"""

from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileAllowed
from wtforms import <PERSON>Field, TextAreaField, SubmitField
from wtforms.validators import DataRequired, Email, Optional, Length

class SocieteForm(FlaskForm):
    """Formulaire des informations de la société"""

    nom = StringField(
        'Nom de la société',
        validators=[DataRequired(message="Le nom de la société est obligatoire")],
        render_kw={"placeholder": "Entrez le nom de la société"}
    )

    responsable = StringField(
        'Responsable',
        validators=[Optional()],
        render_kw={"placeholder": "Nom du responsable"}
    )

    telephone = StringField(
        'Téléphone',
        validators=[Optional()],
        render_kw={"placeholder": "+212 5XX-XXXXXX"}
    )

    email = StringField(
        'Email',
        validators=[Optional(), Email(message="Format d'email invalide")],
        render_kw={"placeholder": "<EMAIL>"}
    )

    adresse = TextAreaField(
        'Adresse',
        validators=[Optional()],
        render_kw={"placeholder": "Adresse complète de la société"}
    )

    if_fiscal = StringField(
        'Identifiant Fiscal (IF)',
        validators=[Optional()],
        render_kw={"placeholder": "IF12345"}
    )

    rc = StringField(
        'Registre de Commerce (RC)',
        validators=[Optional()],
        render_kw={"placeholder": "RC12345"}
    )

    patente = StringField(
        'Patente',
        validators=[Optional()],
        render_kw={"placeholder": "PAT12345"}
    )

    ice = StringField(
        'ICE',
        validators=[Optional()],
        render_kw={"placeholder": "ICE12345"}
    )

    cnss = StringField(
        'CNSS',
        validators=[Optional()],
        render_kw={"placeholder": "CNSS12345"}
    )

    logo = FileField(
        'Logo de la société',
        validators=[
            Optional(),
            FileAllowed(['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'],
                       'Seuls les fichiers image sont autorisés!')
        ]
    )

    pied_de_pages = TextAreaField(
        'Pied de page',
        validators=[Optional()],
        render_kw={"placeholder": "Texte du pied de page pour les documents"},
        description="Ce texte apparaîtra en bas des documents générés"
    )

    submit = SubmitField('Enregistrer les modifications')
