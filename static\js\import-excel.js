/**
 * ملف JavaScript محسن للاستيراد من Excel
 * Enhanced JavaScript file for Excel import
 */

// متغير عام لتخزين البيانات المستوردة
let importedData = null;
let currentModule = null;

/**
 * دالة الاستيراد من Excel الرئيسية
 * Main Excel import function
 */
function importFromExcel() {
    // تحديد النموذج الحالي من URL
    const path = window.location.pathname;
    if (path.includes('marches')) {
        currentModule = 'marches';
    } else if (path.includes('interventions')) {
        currentModule = 'interventions';
    } else if (path.includes('reclamations')) {
        currentModule = 'reclamations';
    } else if (path.includes('regions')) {
        currentModule = 'regions';
    } else if (path.includes('sites')) {
        currentModule = 'sites';
    } else {
        showNotification('Module non supporté pour l\'importation', 'error');
        return;
    }

    // إنشاء input file
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xlsx,.xls';
    input.style.display = 'none';

    input.onchange = function(e) {
        const file = e.target.files[0];
        if (!file) return;

        // التحقق من نوع الملف
        if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
            showNotification('Veuillez sélectionner un fichier Excel (.xlsx ou .xls)', 'error');
            return;
        }

        // عرض مؤشر التحميل
        showLoadingModal('Lecture du fichier Excel en cours...');

        // قراءة الملف
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                const data = new Uint8Array(e.target.result);
                const workbook = XLSX.read(data, {type: 'array'});

                // أخذ الورقة الأولى
                const firstSheetName = workbook.SheetNames[0];
                const worksheet = workbook.Sheets[firstSheetName];

                // تحويل إلى JSON
                const jsonData = XLSX.utils.sheet_to_json(worksheet);

                hideLoadingModal();

                if (!jsonData || jsonData.length === 0) {
                    showNotification('Le fichier est vide ou ne contient pas de données valides', 'error');
                    return;
                }

                // حفظ البيانات وعرض المعاينة
                importedData = jsonData;
                showImportPreview(jsonData, file.name);

            } catch (error) {
                hideLoadingModal();
                console.error('Erreur lors de la lecture du fichier:', error);
                showNotification('Erreur lors de la lecture du fichier Excel: ' + error.message, 'error');
            }
        };

        reader.onerror = function() {
            hideLoadingModal();
            showNotification('Erreur lors de la lecture du fichier', 'error');
        };

        reader.readAsArrayBuffer(file);
    };

    document.body.appendChild(input);
    input.click();
    document.body.removeChild(input);
}

/**
 * عرض معاينة البيانات المستوردة
 * Show preview of imported data
 */
function showImportPreview(data, filename) {
    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'importPreviewModal';
    modal.innerHTML = `
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-file-excel me-2"></i>
                        Aperçu de l'importation Excel
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Fichier:</strong> ${filename}<br>
                        <strong>Nombre d'enregistrements:</strong> ${data.length}<br>
                        <strong>Module:</strong> ${getModuleDisplayName(currentModule)}
                    </div>

                    <div class="table-responsive" style="max-height: 400px;">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark sticky-top">
                                <tr>
                                    ${Object.keys(data[0]).map(key => `<th>${key}</th>`).join('')}
                                </tr>
                            </thead>
                            <tbody>
                                ${data.slice(0, 10).map(row => `
                                    <tr>
                                        ${Object.values(row).map(value => `<td>${value || ''}</td>`).join('')}
                                    </tr>
                                `).join('')}
                                ${data.length > 10 ? `
                                    <tr class="table-warning">
                                        <td colspan="${Object.keys(data[0]).length}" class="text-center">
                                            <i class="fas fa-ellipsis-h me-2"></i>
                                            ... et ${data.length - 10} enregistrements supplémentaires
                                        </td>
                                    </tr>
                                ` : ''}
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Annuler
                    </button>
                    <button type="button" class="btn btn-success" onclick="processImport()">
                        <i class="fas fa-upload me-1"></i>Importer les données (${data.length} enregistrements)
                    </button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();

    // تنظيف عند الإغلاق
    modal.addEventListener('hidden.bs.modal', () => {
        modal.remove();
        importedData = null;
        currentModule = null;
        // إزالة أي backdrop متبقي
        const backdrops = document.querySelectorAll('.modal-backdrop');
        backdrops.forEach(backdrop => backdrop.remove());
        // إعادة تفعيل scroll للصفحة
        document.body.classList.remove('modal-open');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    });
}

/**
 * معالجة الاستيراد الفعلي
 * Process actual import
 */
function processImport() {
    if (!importedData || !currentModule) {
        showNotification('لا توجد بيانات للاستيراد', 'error');
        return;
    }

    // إغلاق modal المعاينة
    const previewModal = bootstrap.Modal.getInstance(document.getElementById('importPreviewModal'));
    if (previewModal) {
        previewModal.hide();
    }

    // عرض مؤشر التحميل
    showLoadingModal('Importation des données en cours...');

    // إنشاء FormData
    const formData = new FormData();

    // تحويل البيانات إلى ملف Excel مؤقت
    const ws = XLSX.utils.json_to_sheet(importedData);
    const wb = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(wb, ws, currentModule);

    // تحويل إلى blob
    const wbout = XLSX.write(wb, {bookType:'xlsx', type:'array'});
    const blob = new Blob([wbout], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});

    formData.append('file', blob, `import_${currentModule}.xlsx`);

    // إرسال الطلب
    fetch(`/api/import/${currentModule}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingModal();

        if (data.success) {
            showNotification(data.message, 'success');
            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showNotification(data.error || 'Erreur lors de l\'importation', 'error');
        }
    })
    .catch(error => {
        hideLoadingModal();
        console.error('Erreur lors de l\'importation:', error);
        showNotification('Erreur de connexion au serveur', 'error');
    });
}

/**
 * عرض modal التحميل
 * Show loading modal
 */
function showLoadingModal(message) {
    // إزالة أي modal تحميل موجود
    const existingModal = document.getElementById('loadingModal');
    if (existingModal) {
        existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'loadingModal';
    modal.setAttribute('data-bs-backdrop', 'static');
    modal.setAttribute('data-bs-keyboard', 'false');
    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center p-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <h5>${message}</h5>
                    <p class="text-muted mb-0">Veuillez patienter...</p>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}

/**
 * إخفاء modal التحميل
 * Hide loading modal
 */
function hideLoadingModal() {
    const modal = document.getElementById('loadingModal');
    if (modal) {
        const bootstrapModal = bootstrap.Modal.getInstance(modal);
        if (bootstrapModal) {
            bootstrapModal.hide();
        }
        // إزالة الـ modal والـ backdrop بشكل فوري
        setTimeout(() => {
            modal.remove();
            // إزالة أي backdrop متبقي
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
            // إعادة تفعيل scroll للصفحة
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }, 100);
    }
}

/**
 * الحصول على اسم النموذج للعرض
 * Get module display name
 */
function getModuleDisplayName(module) {
    const names = {
        'marches': 'Marchés',
        'interventions': 'Interventions',
        'reclamations': 'Réclamations',
        'regions': 'Régions',
        'sites': 'Sites'
    };
    return names[module] || module;
}

/**
 * عرض الإشعارات
 * Show notifications
 */
function showNotification(message, type = 'info') {
    // إزالة الإشعارات الموجودة
    const existingNotifications = document.querySelectorAll('.import-notification');
    existingNotifications.forEach(notification => notification.remove());

    // إنشاء الإشعار
    const notification = document.createElement('div');
    notification.className = `import-notification alert alert-${type} alert-dismissible fade show`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 350px;
        max-width: 500px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;

    const icon = type === 'success' ? 'check-circle' :
                 type === 'error' ? 'exclamation-circle' :
                 type === 'warning' ? 'exclamation-triangle' : 'info-circle';

    notification.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(notification);

    // إزالة تلقائية بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentElement) {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 150);
        }
    }, 5000);
}

// تحميل المكتبة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التأكد من تحميل مكتبة XLSX
    if (typeof XLSX === 'undefined') {
        console.warn('مكتبة XLSX غير محملة. سيتم تحميلها...');
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
        script.onload = function() {
            console.log('تم تحميل مكتبة XLSX بنجاح');
        };
        document.head.appendChild(script);
    }
});
