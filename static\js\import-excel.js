/**
 * ملف JavaScript محسن للاستيراد من Excel
 * Enhanced JavaScript file for Excel import
 */

// متغير عام لتخزين البيانات المستوردة
let importedData = null;
let currentModule = null;

/**
 * عرض الإشعارات
 * Show notifications
 */
function showNotification(message, type = 'info') {
    // إزالة الإشعارات السابقة
    const existingNotifications = document.querySelectorAll('.custom-notification');
    existingNotifications.forEach(notification => notification.remove());

    const notification = document.createElement('div');
    notification.className = `alert alert-${type === 'error' ? 'danger' : type} custom-notification`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;

    notification.innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-${type === 'error' ? 'exclamation-triangle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
            <span>${message}</span>
            <button type="button" class="btn-close ms-auto" onclick="this.parentElement.parentElement.remove()"></button>
        </div>
    `;

    document.body.appendChild(notification);

    // إزالة تلقائية بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

/**
 * عرض مؤشر التحميل
 * Show loading modal
 */
function showLoadingModal(message = 'Chargement...') {
    // إزالة أي modal تحميل سابق
    const existingModal = document.getElementById('loadingModal');
    if (existingModal) {
        existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.id = 'loadingModal';
    modal.className = 'modal fade';
    modal.setAttribute('data-bs-backdrop', 'static');
    modal.setAttribute('data-bs-keyboard', 'false');

    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center py-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <h5>${message}</h5>
                    <p class="text-muted mb-0">Veuillez patienter...</p>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    if (typeof bootstrap !== 'undefined') {
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
    }
}

/**
 * إخفاء مؤشر التحميل
 * Hide loading modal
 */
function hideLoadingModal() {
    const modal = document.getElementById('loadingModal');
    if (modal) {
        if (typeof bootstrap !== 'undefined') {
            const bootstrapModal = bootstrap.Modal.getInstance(modal);
            if (bootstrapModal) {
                bootstrapModal.hide();
            }
        }
        setTimeout(() => {
            modal.remove();
        }, 300);
    }
}

/**
 * دالة الاستيراد من Excel الرئيسية
 * Main Excel import function
 */
function importFromExcel() {
    console.log('Début de la fonction importFromExcel');

    // تحديد النموذج الحالي من URL
    const path = window.location.pathname;
    console.log('Chemin actuel:', path);

    if (path.includes('marches')) {
        currentModule = 'marches';
    } else if (path.includes('interventions')) {
        currentModule = 'interventions';
    } else if (path.includes('reclamations')) {
        currentModule = 'reclamations';
    } else if (path.includes('regions')) {
        currentModule = 'regions';
    } else if (path.includes('sites')) {
        currentModule = 'sites';
    } else {
        console.error('Module non supporté:', path);
        showNotification('Module non supporté pour l\'importation', 'error');
        return;
    }

    console.log('Module détecté:', currentModule);

    // إنشاء input file
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.xlsx,.xls,.csv';
    input.style.display = 'none';

    input.onchange = function(e) {
        console.log('Fichier sélectionné');
        const file = e.target.files[0];
        if (!file) {
            console.log('Aucun fichier sélectionné');
            return;
        }

        console.log('Fichier:', file.name, 'Taille:', file.size, 'Type:', file.type);

        // التحقق من نوع الملف
        if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls') && !file.name.endsWith('.csv')) {
            console.error('Type de fichier non supporté:', file.name);
            showNotification('Veuillez sélectionner un fichier Excel (.xlsx, .xls) ou CSV (.csv)', 'error');
            return;
        }

        console.log('Type de fichier valide, début de la lecture...');
        // عرض مؤشر التحميل
        showLoadingModal('Lecture du fichier Excel en cours...');

        // قراءة الملف
        const reader = new FileReader();
        reader.onload = function(e) {
            try {
                console.log('Début de la lecture du fichier...');

                let formattedData = [];

                if (file.name.endsWith('.csv')) {
                    // معالجة ملف CSV
                    console.log('Traitement du fichier CSV...');
                    const csvText = e.target.result;
                    const lines = csvText.split('\n').filter(line => line.trim());

                    if (lines.length === 0) {
                        hideLoadingModal();
                        showNotification('Le fichier CSV est vide', 'error');
                        return;
                    }

                    const headers = lines[0].split(',').map(h => h.trim());
                    const rows = lines.slice(1);

                    formattedData = rows.map(row => {
                        const values = row.split(',').map(v => v.trim());
                        const obj = {};
                        headers.forEach((header, index) => {
                            obj[header] = values[index] || '';
                        });
                        return obj;
                    });

                } else {
                    // معالجة ملف Excel
                    if (typeof XLSX === 'undefined') {
                        hideLoadingModal();
                        showNotification('Bibliothèque Excel non chargée. Veuillez recharger la page.', 'error');
                        return;
                    }

                    console.log('Traitement du fichier Excel...');
                    const data = new Uint8Array(e.target.result);
                    console.log('Taille du fichier:', data.length, 'bytes');

                    const workbook = XLSX.read(data, {type: 'array'});
                    console.log('Workbook chargé, feuilles:', workbook.SheetNames);

                    // التحقق من وجود أوراق
                    if (!workbook.SheetNames || workbook.SheetNames.length === 0) {
                        hideLoadingModal();
                        showNotification('Le fichier Excel ne contient aucune feuille', 'error');
                        return;
                    }

                    // أخذ الورقة الأولى
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    console.log('Feuille sélectionnée:', firstSheetName);

                    // تحويل إلى JSON
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, {header: 1});
                    console.log('Données converties:', jsonData.length, 'lignes');

                    if (!jsonData || jsonData.length === 0) {
                        hideLoadingModal();
                        showNotification('Le fichier est vide ou ne contient pas de données valides', 'error');
                        return;
                    }

                    // تحويل البيانات إلى تنسيق أفضل
                    const headers = jsonData[0];
                    const rows = jsonData.slice(1);

                    formattedData = rows.map(row => {
                        const obj = {};
                        headers.forEach((header, index) => {
                            obj[header || `Colonne_${index + 1}`] = row[index] || '';
                        });
                        return obj;
                    });
                }

                hideLoadingModal();
                console.log('Données formatées:', formattedData.length, 'enregistrements');

                // حفظ البيانات وعرض المعاينة
                importedData = formattedData;
                showImportPreview(formattedData, file.name);

            } catch (error) {
                hideLoadingModal();
                console.error('Erreur lors de la lecture du fichier:', error);
                showNotification('Erreur lors de la lecture du fichier: ' + error.message, 'error');
            }
        };

        reader.onerror = function() {
            hideLoadingModal();
            showNotification('Erreur lors de la lecture du fichier', 'error');
        };

        // قراءة الملف بالطريقة المناسبة
        if (file.name.endsWith('.csv')) {
            reader.readAsText(file, 'UTF-8');
        } else {
            reader.readAsArrayBuffer(file);
        }
    };

    document.body.appendChild(input);
    input.click();
    document.body.removeChild(input);
}

/**
 * عرض معاينة البيانات المستوردة
 * Show preview of imported data
 */
function showImportPreview(data, filename) {
    try {
        // التحقق من صحة البيانات
        if (!data || !Array.isArray(data) || data.length === 0) {
            showNotification('Aucune donnée valide trouvée dans le fichier', 'error');
            return;
        }

        // إزالة النافذة السابقة إن وجدت
        const existingModal = document.getElementById('importPreviewModal');
        if (existingModal) {
            existingModal.remove();
        }

        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'importPreviewModal';

        // التحقق من وجود مفاتيح في البيانات
        const firstRow = data[0];
        if (!firstRow || typeof firstRow !== 'object') {
            showNotification('Format de données non valide', 'error');
            return;
        }

        const keys = Object.keys(firstRow);
        if (keys.length === 0) {
            showNotification('Aucune colonne trouvée dans les données', 'error');
            return;
        }

        modal.innerHTML = `
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header bg-primary text-white">
                        <h5 class="modal-title">
                            <i class="fas fa-file-excel me-2"></i>
                            Aperçu de l'importation Excel
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Fichier:</strong> ${filename}<br>
                            <strong>Nombre d'enregistrements:</strong> ${data.length}<br>
                            <strong>Module:</strong> Sites
                        </div>

                        <div class="table-responsive" style="max-height: 400px;">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark sticky-top">
                                    <tr>
                                        ${keys.map(key => `<th>${key}</th>`).join('')}
                                    </tr>
                                </thead>
                                <tbody>
                                    ${data.slice(0, 10).map(row => `
                                        <tr>
                                            ${keys.map(key => `<td>${row[key] || ''}</td>`).join('')}
                                        </tr>
                                    `).join('')}
                                    ${data.length > 10 ? `
                                        <tr class="table-warning">
                                            <td colspan="${keys.length}" class="text-center">
                                                <i class="fas fa-ellipsis-h me-2"></i>
                                                ... et ${data.length - 10} enregistrements supplémentaires
                                            </td>
                                        </tr>
                                    ` : ''}
                                </tbody>
                            </table>
                        </div>
                    </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-1"></i>Annuler
                    </button>
                    <button type="button" class="btn btn-success" onclick="processImport()">
                        <i class="fas fa-upload me-1"></i>Importer les données (${data.length} enregistrements)
                    </button>
                </div>
            </div>
        </div>
    `;

        document.body.appendChild(modal);

        // التحقق من وجود Bootstrap
        if (typeof bootstrap === 'undefined') {
            showNotification('Bootstrap non chargé. Veuillez recharger la page.', 'error');
            modal.remove();
            return;
        }

        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();

        // تنظيف عند الإغلاق
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
            importedData = null;
            currentModule = null;
            // إزالة أي backdrop متبقي
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
            // إعادة تفعيل scroll للصفحة
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        });

    } catch (error) {
        console.error('Erreur lors de l\'affichage de l\'aperçu:', error);
        showNotification('Erreur lors de l\'affichage de l\'aperçu: ' + error.message, 'error');
    }
}

/**
 * معالجة الاستيراد الفعلي
 * Process actual import
 */
function processImport() {
    if (!importedData || !currentModule) {
        showNotification('لا توجد بيانات للاستيراد', 'error');
        return;
    }

    // إغلاق modal المعاينة
    const previewModal = bootstrap.Modal.getInstance(document.getElementById('importPreviewModal'));
    if (previewModal) {
        previewModal.hide();
    }

    // عرض مؤشر التحميل
    showLoadingModal('Importation des données en cours...');

    console.log('Envoi des données vers:', `/api/import/${currentModule}`);
    console.log('Données à envoyer:', importedData);

    // إرسال البيانات مباشرة كـ JSON
    fetch(`/api/import/${currentModule}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            data: importedData
        })
    })
    .then(response => response.json())
    .then(data => {
        hideLoadingModal();

        if (data.success) {
            showNotification(data.message, 'success');
            // إعادة تحميل الصفحة بعد ثانيتين
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showNotification(data.error || 'Erreur lors de l\'importation', 'error');
        }
    })
    .catch(error => {
        hideLoadingModal();
        console.error('Erreur lors de l\'importation:', error);
        showNotification('Erreur de connexion au serveur', 'error');
    });
}

/**
 * عرض modal التحميل
 * Show loading modal
 */
function showLoadingModal(message) {
    // إزالة أي modal تحميل موجود
    const existingModal = document.getElementById('loadingModal');
    if (existingModal) {
        existingModal.remove();
    }

    const modal = document.createElement('div');
    modal.className = 'modal fade';
    modal.id = 'loadingModal';
    modal.setAttribute('data-bs-backdrop', 'static');
    modal.setAttribute('data-bs-keyboard', 'false');
    modal.innerHTML = `
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-body text-center p-4">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <h5>${message}</h5>
                    <p class="text-muted mb-0">Veuillez patienter...</p>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    const bootstrapModal = new bootstrap.Modal(modal);
    bootstrapModal.show();
}

/**
 * إخفاء modal التحميل
 * Hide loading modal
 */
function hideLoadingModal() {
    const modal = document.getElementById('loadingModal');
    if (modal) {
        const bootstrapModal = bootstrap.Modal.getInstance(modal);
        if (bootstrapModal) {
            bootstrapModal.hide();
        }
        // إزالة الـ modal والـ backdrop بشكل فوري
        setTimeout(() => {
            modal.remove();
            // إزالة أي backdrop متبقي
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
            // إعادة تفعيل scroll للصفحة
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }, 100);
    }
}

/**
 * الحصول على اسم النموذج للعرض
 * Get module display name
 */
function getModuleDisplayName(module) {
    const names = {
        'marches': 'Marchés',
        'interventions': 'Interventions',
        'reclamations': 'Réclamations',
        'regions': 'Régions',
        'sites': 'Sites'
    };
    return names[module] || module;
}

/**
 * عرض الإشعارات
 * Show notifications
 */
function showNotification(message, type = 'info') {
    // إزالة الإشعارات الموجودة
    const existingNotifications = document.querySelectorAll('.import-notification');
    existingNotifications.forEach(notification => notification.remove());

    // إنشاء الإشعار
    const notification = document.createElement('div');
    notification.className = `import-notification alert alert-${type} alert-dismissible fade show`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 350px;
        max-width: 500px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;

    const icon = type === 'success' ? 'check-circle' :
                 type === 'error' ? 'exclamation-circle' :
                 type === 'warning' ? 'exclamation-triangle' : 'info-circle';

    notification.innerHTML = `
        <i class="fas fa-${icon} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(notification);

    // إزالة تلقائية بعد 5 ثوان
    setTimeout(() => {
        if (notification.parentElement) {
            notification.classList.remove('show');
            setTimeout(() => notification.remove(), 150);
        }
    }, 5000);
}

// تحميل المكتبة عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    // التأكد من تحميل مكتبة XLSX
    if (typeof XLSX === 'undefined') {
        console.warn('مكتبة XLSX غير محملة. سيتم تحميلها...');
        const script = document.createElement('script');
        script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
        script.onload = function() {
            console.log('تم تحميل مكتبة XLSX بنجاح');
        };
        document.head.appendChild(script);
    }
});
