{% extends "interventions/details.html" %}

{% block content %}
{{ super() }}

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-fire-extinguisher"></i> Détails des extincteurs</h5>
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#updateExtincteursModal">
                        <i class="fas fa-edit"></i> Mettre à jour les quantités
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered">
                        <thead>
                            <tr class="table-primary">
                                <th colspan="4" class="text-center">Extincteurs à poudre ABC</th>
                                <th colspan="4" class="text-center">Extincteurs à eau pulvérisée</th>
                                <th colspan="4" class="text-center">Extincteurs à CO2</th>
                            </tr>
                            <tr>
                                <th>ABC 6Kg</th>
                                <th>ABC 9Kg</th>
                                <th>ABC 1Kg</th>
                                <th class="table-info">Total ABC</th>
                                <th>Eau 6L</th>
                                <th>Eau 2L</th>
                                <th>Eau 9L</th>
                                <th class="table-info">Total Eau</th>
                                <th>CO2 2Kg</th>
                                <th>CO2 5Kg</th>
                                <th>CO2 6Kg</th>
                                <th class="table-info">Total CO2</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>{{ extincteur_details.abc_6kg or 0 }}</td>
                                <td>{{ extincteur_details.abc_9kg or 0 }}</td>
                                <td>{{ extincteur_details.abc_1kg or 0 }}</td>
                                <td class="table-info">{{ (extincteur_details.abc_6kg or 0) + (extincteur_details.abc_9kg or 0) + (extincteur_details.abc_1kg or 0) }}</td>
                                <td>{{ extincteur_details.eau_pulverise_6l or 0 }}</td>
                                <td>{{ extincteur_details.eau_pulverise_2l or 0 }}</td>
                                <td>{{ extincteur_details.eau_pulverise_9l or 0 }}</td>
                                <td class="table-info">{{ (extincteur_details.eau_pulverise_6l or 0) + (extincteur_details.eau_pulverise_2l or 0) + (extincteur_details.eau_pulverise_9l or 0) + (extincteur_details.eau_pulverise_10l or 0) }}</td>
                                <td>{{ extincteur_details.co2_2kg or 0 }}</td>
                                <td>{{ extincteur_details.co2_5kg or 0 }}</td>
                                <td>{{ extincteur_details.co2_6kg or 0 }}</td>
                                <td class="table-info">{{ (extincteur_details.co2_2kg or 0) + (extincteur_details.co2_5kg or 0) + (extincteur_details.co2_6kg or 0) + (extincteur_details.co2_10kg or 0) }}</td>
                            </tr>
                        </tbody>
                        <tfoot>
                            <tr class="table-success">
                                <th colspan="11" class="text-end">Total général des extincteurs:</th>
                                <th>{{ (extincteur_details.abc_6kg or 0) + (extincteur_details.abc_9kg or 0) + (extincteur_details.abc_1kg or 0) + 
                                       (extincteur_details.eau_pulverise_6l or 0) + (extincteur_details.eau_pulverise_2l or 0) + (extincteur_details.eau_pulverise_9l or 0) + (extincteur_details.eau_pulverise_10l or 0) +
                                       (extincteur_details.co2_2kg or 0) + (extincteur_details.co2_5kg or 0) + (extincteur_details.co2_6kg or 0) + (extincteur_details.co2_10kg or 0) }}</th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour mettre à jour les quantités d'extincteurs -->
<div class="modal fade" id="updateExtincteursModal" tabindex="-1" aria-labelledby="updateExtincteursModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="updateExtincteursModalLabel">Mettre à jour les quantités d'extincteurs</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('details_intervention', id=intervention.id) }}">
                <input type="hidden" name="update_extincteurs" value="1">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <h6 class="border-bottom pb-2">Extincteurs à poudre ABC</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="abc_6kg" class="form-label">ABC 6Kg</label>
                            <input type="number" class="form-control" id="abc_6kg" name="abc_6kg" value="{{ extincteur_details.abc_6kg or 0 }}" min="0">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="abc_9kg" class="form-label">ABC 9Kg</label>
                            <input type="number" class="form-control" id="abc_9kg" name="abc_9kg" value="{{ extincteur_details.abc_9kg or 0 }}" min="0">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="abc_1kg" class="form-label">ABC 1Kg</label>
                            <input type="number" class="form-control" id="abc_1kg" name="abc_1kg" value="{{ extincteur_details.abc_1kg or 0 }}" min="0">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <h6 class="border-bottom pb-2">Extincteurs à eau pulvérisée</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="eau_pulverise_6l" class="form-label">Eau 6L</label>
                            <input type="number" class="form-control" id="eau_pulverise_6l" name="eau_pulverise_6l" value="{{ extincteur_details.eau_pulverise_6l or 0 }}" min="0">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="eau_pulverise_2l" class="form-label">Eau 2L</label>
                            <input type="number" class="form-control" id="eau_pulverise_2l" name="eau_pulverise_2l" value="{{ extincteur_details.eau_pulverise_2l or 0 }}" min="0">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="eau_pulverise_9l" class="form-label">Eau 9L</label>
                            <input type="number" class="form-control" id="eau_pulverise_9l" name="eau_pulverise_9l" value="{{ extincteur_details.eau_pulverise_9l or 0 }}" min="0">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="eau_pulverise_10l" class="form-label">Eau 10L</label>
                            <input type="number" class="form-control" id="eau_pulverise_10l" name="eau_pulverise_10l" value="{{ extincteur_details.eau_pulverise_10l or 0 }}" min="0">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <h6 class="border-bottom pb-2">Extincteurs à CO2</h6>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <label for="co2_2kg" class="form-label">CO2 2Kg</label>
                            <input type="number" class="form-control" id="co2_2kg" name="co2_2kg" value="{{ extincteur_details.co2_2kg or 0 }}" min="0">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="co2_5kg" class="form-label">CO2 5Kg</label>
                            <input type="number" class="form-control" id="co2_5kg" name="co2_5kg" value="{{ extincteur_details.co2_5kg or 0 }}" min="0">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="co2_6kg" class="form-label">CO2 6Kg</label>
                            <input type="number" class="form-control" id="co2_6kg" name="co2_6kg" value="{{ extincteur_details.co2_6kg or 0 }}" min="0">
                        </div>
                        <div class="col-md-3 mb-3">
                            <label for="co2_10kg" class="form-label">CO2 10Kg</label>
                            <input type="number" class="form-control" id="co2_10kg" name="co2_10kg" value="{{ extincteur_details.co2_10kg or 0 }}" min="0">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
