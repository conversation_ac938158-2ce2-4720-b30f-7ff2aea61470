#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار Installer نظام إدارة الصيانة
"""

import os
import sys
import subprocess
import platform

def check_inno_setup():
    """فحص وجود Inno Setup"""
    
    print("🔍 فحص Inno Setup...")
    
    inno_paths = [
        r"C:\Program Files (x86)\Inno Setup 6\ISCC.exe",
        r"C:\Program Files\Inno Setup 6\ISCC.exe",
        r"C:\Program Files (x86)\Inno Setup 5\ISCC.exe",
        r"C:\Program Files\Inno Setup 5\ISCC.exe"
    ]
    
    for path in inno_paths:
        if os.path.exists(path):
            print(f"✅ Inno Setup موجود: {path}")
            return path
    
    print("❌ Inno Setup غير مثبت")
    print("💡 يرجى تحميل وتثبيت Inno Setup من:")
    print("   https://jrsoftware.org/isdl.php")
    return None

def check_required_files():
    """فحص الملفات المطلوبة"""
    
    print("\n📁 فحص الملفات المطلوبة...")
    
    required_files = {
        'maintenance_installer.iss': 'ملف Inno Setup Script',
        'dist/Maintenance_Management_System.exe': 'الملف التنفيذي',
        'config_loader.py': 'محمل الإعدادات',
        'تشغيل_مع_متصفح.bat': 'ملف التشغيل المحسن',
        'BROWSER_FIX_GUIDE.md': 'دليل حل مشكلة المتصفح',
        'SOLUTION_SUMMARY.md': 'ملخص الحل',
        'INSTALLATION_GUIDE.md': 'دليل التثبيت'
    }
    
    missing_files = []
    
    for file_path, description in required_files.items():
        if os.path.exists(file_path):
            print(f"   ✅ {file_path} - {description}")
        else:
            print(f"   ❌ {file_path} - {description} (غير موجود)")
            missing_files.append(file_path)
    
    return missing_files

def check_system_compatibility():
    """فحص توافق النظام"""
    
    print(f"\n💻 فحص توافق النظام...")
    
    system_info = {
        'نظام التشغيل': platform.system(),
        'إصدار النظام': platform.release(),
        'المعمارية': platform.machine(),
        'معالج': platform.processor(),
        'Python': sys.version.split()[0]
    }
    
    print(f"📊 معلومات النظام:")
    for key, value in system_info.items():
        print(f"   {key}: {value}")
    
    # فحص Windows
    if platform.system() != 'Windows':
        print(f"⚠️ Inno Setup يعمل على Windows فقط")
        return False
    
    # فحص إصدار Windows
    version = platform.release()
    if version in ['7', '8', '8.1', '10', '11']:
        print(f"✅ إصدار Windows مدعوم: {version}")
    else:
        print(f"⚠️ إصدار Windows قد لا يكون مدعوم: {version}")
    
    return True

def test_installer_script():
    """اختبار ملف Installer Script"""
    
    print(f"\n📋 اختبار ملف Installer Script...")
    
    script_file = 'maintenance_installer.iss'
    
    if not os.path.exists(script_file):
        print(f"❌ ملف Script غير موجود: {script_file}")
        return False
    
    try:
        with open(script_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص العناصر المهمة
        required_sections = [
            '[Setup]',
            '[Languages]',
            '[Tasks]',
            '[Files]',
            '[Icons]',
            '[Registry]',
            '[Run]',
            '[Code]'
        ]
        
        missing_sections = []
        for section in required_sections:
            if section in content:
                print(f"   ✅ {section} موجود")
            else:
                print(f"   ❌ {section} غير موجود")
                missing_sections.append(section)
        
        # فحص الإعدادات المهمة
        important_settings = [
            'AppName=',
            'AppVersion=',
            'DefaultDirName=',
            'OutputBaseFilename=',
            'MinVersion=6.1',
            'ArchitecturesAllowed=x86 x64'
        ]
        
        for setting in important_settings:
            if setting in content:
                print(f"   ✅ {setting}")
            else:
                print(f"   ⚠️ {setting} قد يكون مفقود")
        
        if not missing_sections:
            print(f"✅ ملف Script صحيح")
            return True
        else:
            print(f"❌ ملف Script يحتاج مراجعة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في قراءة ملف Script: {e}")
        return False

def estimate_installer_size():
    """تقدير حجم Installer"""
    
    print(f"\n📊 تقدير حجم Installer...")
    
    total_size = 0
    
    files_to_include = [
        'dist/Maintenance_Management_System.exe',
        'config_loader.py',
        'تشغيل_مع_متصفح.bat',
        'BROWSER_FIX_GUIDE.md',
        'SOLUTION_SUMMARY.md',
        'INSTALLATION_GUIDE.md'
    ]
    
    for file_path in files_to_include:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            total_size += size
            print(f"   📄 {file_path}: {size:,} بايت")
    
    # إضافة حجم قاعدة البيانات إذا كانت موجودة
    if os.path.exists('maintenance.db'):
        db_size = os.path.getsize('maintenance.db')
        total_size += db_size
        print(f"   🗄️ maintenance.db: {db_size:,} بايت")
    
    print(f"\n📊 إجمالي الحجم المقدر: {total_size:,} بايت ({total_size/1024/1024:.1f} MB)")
    
    # تقدير حجم Installer مع الضغط
    compressed_size = total_size * 0.7  # تقدير ضغط 30%
    print(f"📦 حجم Installer المتوقع: {compressed_size:,} بايت ({compressed_size/1024/1024:.1f} MB)")
    
    return total_size

def main():
    """الدالة الرئيسية"""
    
    print("🧪 اختبار Installer نظام إدارة الصيانة")
    print("🧪 Testing Maintenance System Installer")
    print("=" * 60)
    
    try:
        # فحص Inno Setup
        inno_path = check_inno_setup()
        if not inno_path:
            return False
        
        # فحص توافق النظام
        if not check_system_compatibility():
            return False
        
        # فحص الملفات المطلوبة
        missing_files = check_required_files()
        if missing_files:
            print(f"\n❌ ملفات مفقودة: {len(missing_files)}")
            for file in missing_files:
                print(f"   • {file}")
            return False
        
        # اختبار ملف Script
        if not test_installer_script():
            return False
        
        # تقدير حجم Installer
        estimate_installer_size()
        
        # النتيجة النهائية
        print(f"\n🎉 النتيجة النهائية:")
        print("=" * 60)
        print(f"✅ جميع المتطلبات متوفرة")
        print(f"✅ النظام متوافق")
        print(f"✅ الملفات موجودة")
        print(f"✅ ملف Script صحيح")
        print(f"✅ جاهز لبناء Installer")
        
        print(f"\n🚀 لبناء Installer:")
        print(f"   شغل: build_installer.bat")
        print(f"   أو: {inno_path} maintenance_installer.iss")
        
        print(f"\n🎯 مميزات Installer:")
        print(f"   🚫 بدون خلفية سوداء")
        print(f"   💻 Windows 7, 10, 11 (32/64 bit)")
        print(f"   👥 عدة مستخدمين")
        print(f"   🌐 إعدادات شبكية")
        print(f"   📦 تثبيت احترافي")
        
        return True
        
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        return False

if __name__ == "__main__":
    try:
        success = main()
        
        if success:
            print(f"\n✅ الاختبار مكتمل بنجاح!")
        else:
            print(f"\n❌ فشل في الاختبار")
        
        input(f"\nاضغط Enter للخروج...")
        
    except KeyboardInterrupt:
        print(f"\n⏹️ تم إلغاء الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ غير متوقع: {e}")
        input("اضغط Enter للخروج...")
