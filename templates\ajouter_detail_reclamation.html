{% extends "base.html" %}

{% block title %}Ajouter un Détail de Réclamation - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Ajouter un Détail de Réclamation{% endblock %}

{% block extra_css %}
<style>
  /* Styles pour les input-group */
  .input-group {
    margin-bottom: 0;
    border-radius: 0.25rem;
    transition: all 0.2s ease-in-out;
  }
  
  .input-group-prepend {
    display: flex;
  }
  
  .input-group-text {
    display: flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: center;
    white-space: nowrap;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    border-radius: 0.25rem 0 0 0.25rem;
  }
  
  .form-select, .form-control {
    border-radius: 0 0.25rem 0.25rem 0;
  }

    /* Boutons modernes */
    .btn-modern {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: none;
    }
    
    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }
    
    .btn-modern:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .btn-modern i {
        font-size: 0.9em;
    }
    
    /* Couleurs spécifiques */
    .btn-primary.btn-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }
    
    .btn-success.btn-modern {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }
    
    .btn-danger.btn-modern {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }
    
    .btn-warning.btn-modern {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #333;
    }
    
    .btn-info.btn-modern {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
    }
    
    .btn-outline-primary.btn-modern {
        border: 2px solid #667eea;
        color: #667eea;
        background: transparent;
    }
    
    .btn-outline-primary.btn-modern:hover {
        background: #667eea;
        color: white;
    }
    
</style>
{% endblock %}

{% block content %}
<div class="container mt-4 mb-5">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="text-primary"><i class="fas fa-plus-circle me-2"></i> Ajouter un détail à la réclamation #{{ reclamation.id }}</h4>
    <a href="{{ url_for('voir_reclamation', id=reclamation.id) }}" class="btn btn-outline-secondary">
      <i class="fas fa-arrow-left me-1"></i> Retour aux détails
    </a>
  </div>

  <!-- Informations de la réclamation -->
  <div class="card shadow-sm mb-4">
    <div class="card-header bg-light">
      <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i> Informations de la réclamation</h5>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-3">
          <p><strong>N° de marché:</strong> {{ reclamation.numero_marche }}</p>
        </div>
        <div class="col-md-3">
          <p><strong>Client:</strong> {{ reclamation.client }}</p>
        </div>
        <div class="col-md-3">
          <p><strong>Domaine:</strong> {{ reclamation.domaine }}</p>
        </div>
        <div class="col-md-3">
          <p><strong>Lieu:</strong> {{ reclamation.lieu }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Formulaire d'ajout de détail -->
  <div class="card shadow-sm">
    <div class="card-header bg-light">
      <h5 class="mb-0"><i class="fas fa-list-alt me-2"></i> Détails de l'intervention</h5>
    </div>
    <div class="card-body">
      <form method="POST" action="{{ url_for('ajouter_detail_reclamation', id=reclamation.id) }}">
        <div class="row mb-4">
          <div class="col-md-4 mb-3">
            <label for="region" class="form-label">Région</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-map"></i></span>
              </div>
              <select class="form-select" id="region" name="region" required>
                <option value="">Sélectionner une région</option>
                {% for region in regions %}
                <option value="{{ region.nom }}">{{ region.nom }}</option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="nom_site" class="form-label">Nom du site</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-building"></i></span>
              </div>
              <input type="text" class="form-control" id="nom_site" name="nom_site" required>
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="nbr_systeme" class="form-label">Nombre de systèmes</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-server"></i></span>
              </div>
              <input type="number" class="form-control" id="nbr_systeme" name="nbr_systeme" min="1" value="1" required>
            </div>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-4 mb-3">
            <label for="date_reclamation" class="form-label">Date de réclamation</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
              </div>
              <input type="date" class="form-control" id="date_reclamation" name="date_reclamation" required>
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="date_intervention" class="form-label">Date d'intervention</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-calendar-check"></i></span>
              </div>
              <input type="date" class="form-control" id="date_intervention" name="date_intervention">
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="technicien" class="form-label">Technicien</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-user-cog"></i></span>
              </div>
              <input type="text" class="form-control" id="technicien" name="technicien">
            </div>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-4 mb-3">
            <label for="situation" class="form-label">Situation</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-tasks"></i></span>
              </div>
              <select class="form-select" id="situation" name="situation" required>
                <option value="">Sélectionner une situation</option>
                <option value="Réglé">Réglé</option>
                <option value="Pas encore" selected>En attente</option>
                <option value="Problème">Problème</option>
              </select>
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="etat_materiel" class="form-label">État du matériel</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-tools"></i></span>
              </div>
              <select class="form-select" id="etat_materiel" name="etat_materiel" required>
                <option value="">Sélectionner un état</option>
                <option value="Bon">Bon</option>
                <option value="Moyen">Moyen</option>
                <option value="Mauvais">Mauvais</option>
              </select>
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="gps" class="form-label">Coordonnées GPS</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
              </div>
              <input type="text" class="form-control" id="gps" name="gps" placeholder="Ex: 34.0522, -118.2437">
            </div>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-4 mb-3">
            <label for="telephone_chef_site" class="form-label">Téléphone chef de site</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-phone"></i></span>
              </div>
              <input type="text" class="form-control" id="telephone_chef_site" name="telephone_chef_site">
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="telephone_securite" class="form-label">Téléphone sécurité</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-shield-alt"></i></span>
              </div>
              <input type="text" class="form-control" id="telephone_securite" name="telephone_securite">
            </div>
          </div>
          <div class="col-md-4 mb-3">
            <label for="technicien_contact" class="form-label">Technicien à contacter</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-address-book"></i></span>
              </div>
              <input type="text" class="form-control" id="technicien_contact" name="technicien_contact">
            </div>
          </div>
        </div>

        <div class="row mb-4">
          <div class="col-md-12 mb-3">
            <label for="observation" class="form-label">Observation</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-comment"></i></span>
              </div>
              <textarea class="form-control" id="observation" name="observation" rows="3"></textarea>
            </div>
          </div>
        </div>

        <!-- Boutons d'action -->
        <div class="d-flex justify-content-between mt-4">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-1"></i> Enregistrer le détail
          </button>
          <div>
            <button type="reset" class="btn btn-outline-secondary me-2">
              <i class="fas fa-undo me-1"></i> Réinitialiser
            </button>
            <a href="{{ url_for('voir_reclamation', id=reclamation.id) }}" class="btn btn-outline-danger">
              <i class="fas fa-times me-1"></i> Annuler
            </a>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Définir la date de réclamation à aujourd'hui par défaut
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('date_reclamation').value = today;
        
        // Ajouter des effets visuels pour améliorer l'expérience utilisateur
        const formInputs = document.querySelectorAll('input, select, textarea');
        formInputs.forEach(input => {
            // Ajouter une classe lors du focus
            input.addEventListener('focus', function() {
                const inputGroup = this.closest('.input-group');
                if (inputGroup) {
                    inputGroup.style.borderColor = '#0d6efd';
                    inputGroup.style.boxShadow = '0 0 0 0.25rem rgba(13, 110, 253, 0.25)';
                    const icon = inputGroup.querySelector('.input-group-text');
                    if (icon) {
                        icon.style.backgroundColor = '#e9ecef';
                        icon.style.borderColor = '#0d6efd';
                    }
                }
            });
            
            // Retirer la classe lors de la perte du focus
            input.addEventListener('blur', function() {
                const inputGroup = this.closest('.input-group');
                if (inputGroup) {
                    inputGroup.style.borderColor = '';
                    inputGroup.style.boxShadow = '';
                    const icon = inputGroup.querySelector('.input-group-text');
                    if (icon) {
                        icon.style.backgroundColor = '';
                        icon.style.borderColor = '';
                    }
                }
            });
        });
    });
</script>
{% endblock %}
