@echo off
title تنظيف قاعدة البيانات - نظام إدارة الصيانة
color 0C
echo.
echo ========================================
echo    تنظيف قاعدة البيانات
echo    Database Cleanup
echo ========================================
echo.

echo 🧹 أداة تنظيف قاعدة البيانات
echo 🧹 Database cleanup tool
echo.
echo ⚠️ تحذير: سيتم حذف جميع البيانات الحالية!
echo ⚠️ Warning: All current data will be deleted!
echo.
echo 💾 سيتم حفظ نسخة احتياطية تلقائياً
echo 💾 A backup will be created automatically
echo.

pause

echo.
echo 🔧 تشغيل أداة التنظيف...
echo 🔧 Running cleanup tool...
echo.

if exist "create_clean_database.py" (
    python create_clean_database.py
) else (
    echo ❌ لم يتم العثور على ملف التنظيف
    echo ❌ Cleanup file not found
    echo.
    echo 💡 تأكد من وجود create_clean_database.py
    echo 💡 Make sure create_clean_database.py exists
    pause
    exit /b 1
)

echo.
echo 🎯 انتهى التنظيف
echo 🎯 Cleanup finished
pause
