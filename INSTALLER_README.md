# دليل Installer نظام إدارة الصيانة
# Maintenance System Installer Guide

---

## 🎯 **نظرة عامة**

تم إنشاء **Installer احترافي** لنظام إدارة الصيانة باستخدام **Inno Setup** مع جميع المميزات المطلوبة:

- ✅ **بدون خلفية سوداء** أو نوافذ console
- ✅ **متوافق مع Windows 7, 10, 11**
- ✅ **يدعم 32-bit و 64-bit**
- ✅ **عدة مستخدمين** في نفس الوقت
- ✅ **تثبيت تلقائي** وسهل

---

## 📦 **بناء الـ Installer**

### **المتطلبات:**
1. **Inno Setup 6** - [تحميل من هنا](https://jrsoftware.org/isdl.php)
2. **الملف التنفيذي** جاهز في مجلد `dist`
3. **جميع الملفات** المطلوبة موجودة

### **خطوات البناء:**

#### **الطريقة السريعة:**
```bash
# 1. تثبيت Inno Setup (إذا لم يكن مثبت)
# 2. تشغيل ملف البناء
build_installer.bat
```

#### **الطريقة اليدوية:**
```bash
# 1. فتح Inno Setup Compiler
# 2. فتح ملف maintenance_installer.iss
# 3. الضغط على Build أو F9
```

---

## 🎨 **مميزات الـ Installer**

### **واجهة المستخدم:**
- **🎯 واجهة حديثة** مع Wizard Style
- **🌍 دعم متعدد اللغات** (عربي، إنجليزي، فرنسي)
- **📋 خيارات تثبيت متقدمة**
- **🔧 إعدادات مخصصة** للشبكة

### **أنواع التثبيت:**
1. **تثبيت عادي** - مستخدم واحد محلي
2. **تثبيت شبكي** - عدة مستخدمين
3. **تثبيت خادم** - للشبكة المحلية

### **الإعدادات:**
- **🌐 تكوين المنفذ** تلقائياً أو يدوياً
- **👥 صلاحيات متعددة المستخدمين**
- **🔒 أمان الملفات** والمجلدات
- **📊 ملف إعدادات** قابل للتخصيص

---

## 📁 **محتويات التثبيت**

### **الملفات الرئيسية:**
```
Program Files\Maintenance Management System\
├── Maintenance_Management_System.exe    (الملف التنفيذي)
├── config.ini                          (ملف الإعدادات)
├── config_loader.py                     (محمل الإعدادات)
├── تشغيل_النظام.bat                     (تشغيل عادي)
├── تشغيل_مع_متصفح.bat                  (تشغيل محسن)
├── maintenance.db                       (قاعدة البيانات)
├── uploads\                             (مجلد الرفع)
└── docs\                                (الوثائق)
    ├── INSTALLATION_GUIDE.md
    ├── BROWSER_FIX_GUIDE.md
    └── SOLUTION_SUMMARY.md
```

### **الاختصارات:**
- **🖥️ سطح المكتب** (اختياري)
- **📋 قائمة ابدأ** (افتراضي)
- **⚡ شريط التشغيل السريع** (اختياري)
- **🚀 تشغيل تلقائي** (اختياري)

---

## 🔧 **إعدادات التثبيت**

### **ملف config.ini:**
```ini
[Settings]
InstallationType=0          # 0=عادي, 1=شبكي, 2=خادم
Port=5000                   # المنفذ المستخدم
MultiUser=False             # دعم عدة مستخدمين
ServerMode=False            # وضع الخادم
Version=1.0.0              # إصدار النظام

[Network]
Host=127.0.0.1             # المضيف (0.0.0.0 للشبكة)
AllowExternalAccess=False   # السماح بالوصول الخارجي

[Database]
Path=maintenance.db         # مسار قاعدة البيانات
BackupEnabled=True          # تفعيل النسخ الاحتياطي
BackupInterval=24           # فترة النسخ الاحتياطي (ساعات)
```

---

## 🚀 **التشغيل بعد التثبيت**

### **الطرق المتاحة:**
1. **من قائمة ابدأ:** "نظام إدارة الصيانة"
2. **من سطح المكتب:** أيقونة النظام
3. **تشغيل محسن:** "تشغيل مع المتصفح"
4. **تشغيل تلقائي:** مع بدء Windows

### **أول تشغيل:**
1. **يتم تحميل الإعدادات** من config.ini
2. **إنشاء قاعدة البيانات** إذا لم تكن موجودة
3. **تكوين الصلاحيات** للمستخدمين المتعددين
4. **فتح المتصفح** تلقائياً (أو يدوياً)

---

## 🌐 **الاستخدام الشبكي**

### **إعداد الخادم:**
1. **اختر "تثبيت خادم"** أثناء التثبيت
2. **حدد المنفذ** المطلوب (افتراضي: 5000)
3. **تأكد من إعدادات Firewall**
4. **شارك عنوان IP** مع المستخدمين

### **الوصول من أجهزة أخرى:**
```
http://[SERVER_IP]:5000
```

### **إدارة المستخدمين:**
- **👤 حسابات منفصلة** لكل مستخدم
- **🔒 صلاحيات محددة** حسب الدور
- **📊 تتبع النشاط** والتغييرات
- **🔄 تحديث فوري** للبيانات

---

## 🗑️ **إلغاء التثبيت**

### **الطرق المتاحة:**
1. **من قائمة ابدأ:** "إلغاء تثبيت نظام إدارة الصيانة"
2. **من لوحة التحكم:** "البرامج والميزات"
3. **من الإعدادات:** "التطبيقات والميزات"

### **ما يتم حذفه:**
- ✅ **جميع ملفات البرنامج**
- ✅ **الاختصارات والقوائم**
- ✅ **إعدادات التسجيل**
- ⚠️ **قاعدة البيانات محفوظة** (نسخة احتياطية)

---

## 🔧 **استكشاف الأخطاء**

### **مشاكل التثبيت:**
- **خطأ في الصلاحيات:** شغل كـ Administrator
- **مساحة غير كافية:** تحرير مساحة (100 MB مطلوب)
- **إصدار Windows قديم:** Windows 7+ مطلوب

### **مشاكل التشغيل:**
- **لا يفتح المتصفح:** راجع BROWSER_FIX_GUIDE.md
- **خطأ في المنفذ:** غير المنفذ في config.ini
- **مشاكل الشبكة:** تحقق من Firewall

### **مشاكل عدة المستخدمين:**
- **رفض الوصول:** تحقق من صلاحيات المجلد
- **بطء في الاستجابة:** قلل عدد المستخدمين المتزامنين
- **تعارض البيانات:** استخدم النسخ الاحتياطي

---

## 📊 **الإحصائيات والمراقبة**

### **ملفات السجل:**
- **📝 سجل التطبيق:** logs/app.log
- **🌐 سجل الشبكة:** logs/network.log
- **👥 سجل المستخدمين:** logs/users.log

### **مراقبة الأداء:**
- **💾 استخدام الذاكرة**
- **🌐 اتصالات الشبكة**
- **📊 استخدام قاعدة البيانات**
- **⏱️ أوقات الاستجابة**

---

## 🎉 **الخلاصة**

تم إنشاء **Installer احترافي ومتكامل** لنظام إدارة الصيانة مع:

### **✅ جميع المتطلبات مستوفاة:**
- **🚫 بدون خلفية سوداء** أو نوافذ console
- **💻 متوافق مع جميع إصدارات Windows**
- **🏗️ دعم 32-bit و 64-bit**
- **👥 عدة مستخدمين** في نفس الوقت
- **📦 تثبيت سهل** ومرن
- **🔧 إعدادات قابلة للتخصيص**

### **🚀 جاهز للتوزيع:**
- **📁 ملف واحد:** MaintenanceSystemSetup.exe
- **⚡ تثبيت سريع:** أقل من دقيقة
- **🎯 واجهة بديهية:** سهلة الاستخدام
- **🔒 آمن ومستقر:** للاستخدام المهني

**🎊 النظام مكتمل وجاهز للتوزيع والاستخدام المهني! ✨**
