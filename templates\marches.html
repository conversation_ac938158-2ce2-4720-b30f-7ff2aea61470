{% extends "base.html" %}

{% block title %}Gestion des Marchés - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Gestion des Marchés{% endblock %}

{% block content %}
<div class="container-fluid">
  <!-- Header amélioré -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card border-0 shadow-lg" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
        <div class="card-body py-4">
          <div class="d-flex justify-content-between align-items-center w-100">
            <div class="header-left">
              <h1 class="mb-2 text-white fw-bold">
                <i class="fas fa-file-contract me-3"></i>Gestion des Marchés
              </h1>
              <p class="mb-0 text-white-75 fs-5">Gérez vos contrats et marchés avec les clients</p>
              <p class="mb-0 text-white-50 mt-2">
                <i class="fas fa-chart-bar me-2"></i>{{ marches|length }} marchés enregistrés
              </p>
            </div>
            <div class="header-right">
              <div class="btn-group" role="group">
                <a href="{{ url_for('ajouter_marche') }}" class="btn btn-primary btn-sm">
                  <i class="fas fa-plus"></i> Ajouter
                </a>

                <button type="button" class="btn btn-success btn-sm" id="exportExcel">
                  <i class="fas fa-file-excel"></i> Exporter Excel
                </button>
                <button type="button" class="btn btn-info btn-sm" onclick="importFromExcel()">
                  <i class="fas fa-file-import"></i> Importer Excel
                </button>
                <button type="button" class="btn btn-info btn-sm" onclick="location.reload()">
                  <i class="fas fa-sync-alt"></i> Actualiser
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistiques rapides -->
  <div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-3">
      <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="flex-grow-1">
              <div class="small fw-bold text-uppercase mb-1 opacity-75">Total Marchés</div>
              <div class="h4 mb-0 fw-bold">{{ marches|length }}</div>
            </div>
            <div class="ms-3">
              <i class="fas fa-file-contract fa-2x opacity-75"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
      <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="flex-grow-1">
              <div class="small fw-bold text-uppercase mb-1 opacity-75">Domaines</div>
              <div class="h4 mb-0 fw-bold">{{ marches|map(attribute='domaine')|unique|list|length }}</div>
            </div>
            <div class="ms-3">
              <i class="fas fa-layer-group fa-2x opacity-75"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
      <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="flex-grow-1">
              <div class="small fw-bold text-uppercase mb-1 opacity-75">Clients</div>
              <div class="h4 mb-0 fw-bold">{{ marches|map(attribute='client')|unique|list|length }}</div>
            </div>
            <div class="ms-3">
              <i class="fas fa-users fa-2x opacity-75"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-md-6 mb-3">
      <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);">
        <div class="card-body text-white">
          <div class="d-flex align-items-center">
            <div class="flex-grow-1">
              <div class="small fw-bold text-uppercase mb-1 opacity-75">Montant Total</div>
              <div class="h4 mb-0 fw-bold">{{ "%.0f"|format(marches|sum(attribute='montant')|default(0)) }} DH</div>
            </div>
            <div class="ms-3">
              <i class="fas fa-money-bill-wave fa-2x opacity-75"></i>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>



  <!-- Filtres améliorés -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card border-0 shadow-sm">
        <div class="card-body">
          <h6 class="card-title mb-3">
            <i class="fas fa-filter me-2 text-primary"></i>Filtres de recherche
          </h6>
          <div class="row g-3">
            <div class="col-md-4">
              <div class="input-group">
                <span class="input-group-text"><i class="fas fa-search"></i></span>
                <input type="text" class="form-control" id="searchMarche" placeholder="Rechercher un marché...">
              </div>
            </div>
            <div class="col-md-3">
              <select class="form-select" id="filterDomaine">
                <option value="">Tous les domaines</option>
                <option value="SVS">SVS</option>
                <option value="EXTINCTEUR">EXTINCTEUR</option>
                <option value="SYSTEME D'INCENDIE">SYSTEME D'INCENDIE</option>
                <option value="SYSTEME D'ALARME">SYSTEME D'ALARME</option>
                <option value="SYSTEME TELEPHONIQUE">SYSTEME TELEPHONIQUE</option>
                <option value="L'AFFICHAGE DYNAMIQUE">L'AFFICHAGE DYNAMIQUE</option>
              </select>
            </div>
            <div class="col-md-3">
              <input type="text" class="form-control" id="filterClient" placeholder="Filtrer par client...">
            </div>
            <div class="col-md-2">
              <button type="button" class="btn btn-primary w-100" id="resetFilters">
                <i class="fas fa-undo me-1"></i>Reset
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Tableau des marchés -->
  <div class="row">
    <div class="col-12">
      <div class="card border-0 shadow-sm">
        <div class="card-header bg-primary text-white">
          <div class="d-flex justify-content-between align-items-center">
            <h5 class="mb-0">
              <i class="fas fa-list me-2"></i>Liste des Marchés
            </h5>
            <span class="badge bg-light text-dark">{{ marches|length if marches else 0 }} marchés</span>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0" id="marchesTable">
              <thead class="table-light">
                <tr>
                  <th style="width: 10%;" class="fw-bold">N° Marché</th>
                  <th style="width: 12%;" class="fw-bold">Domaine</th>
                  <th style="width: 10%;" class="fw-bold">Date</th>
                  <th style="width: 25%;" class="fw-bold">Objet</th>
                  <th style="width: 15%;" class="fw-bold">Client</th>
                  <th style="width: 10%;" class="fw-bold">Montant</th>
                  <th style="width: 8%;" class="fw-bold">Délai</th>
                  <th style="width: 10%;" class="fw-bold">Actions</th>
                </tr>
              </thead>
              <tbody>
                {% if marches %}
                  {% for marche in marches %}
                  <tr>
                    <td class="fw-bold text-primary">{{ marche.numero }}</td>
                    <td>
                      {% if marche.domaine == 'SVS' %}
                        <span class="badge bg-primary rounded-pill">SVS</span>
                      {% elif marche.domaine == 'EXTINCTEUR' %}
                        <span class="badge bg-danger rounded-pill">EXTINCTEUR</span>
                      {% elif marche.domaine == 'SYSTEME D\'INCENDIE' %}
                        <span class="badge bg-warning text-dark rounded-pill">SYS. INCENDIE</span>
                      {% elif marche.domaine == 'SYSTEME D\'ALARME' %}
                        <span class="badge bg-info rounded-pill">SYS. ALARME</span>
                      {% elif marche.domaine == 'SYSTEME TELEPHONIQUE' %}
                        <span class="badge bg-success rounded-pill">SYS. TÉLÉPHONE</span>
                      {% elif marche.domaine == "L'AFFICHAGE DYNAMIQUE" %}
                        <span class="badge bg-dark rounded-pill">AFFICHAGE DYN.</span>
                      {% else %}
                        <span class="badge bg-secondary rounded-pill">{{ marche.domaine }}</span>
                      {% endif %}
                    </td>
                    <td>{{ marche.date }}</td>
                    <td class="text-truncate" style="max-width: 200px;" title="{{ marche.objet }}">{{ marche.objet }}</td>
                    <td>{{ marche.client }}</td>
                    <td class="fw-bold text-success">{{ "{:,.0f}".format(marche.montant) }} DH</td>
                    <td>{{ marche.delai_execution }}</td>
                    <td>
                      <div class="btn-group" role="group">
                        <a href="{{ url_for('details_marche', numero=marche.numero) }}"
                           class="btn btn-sm btn-outline-info" title="Voir les détails">
                          <i class="fas fa-eye"></i>
                        </a>

                        <a href="{{ url_for('modifier_marche', numero=marche.numero) }}"
                           class="btn btn-sm btn-outline-warning" title="Modifier">
                          <i class="fas fa-edit"></i>
                        </a>
                        <button type="button"
                                class="btn btn-sm btn-danger"
                                onclick="if(confirm('Êtes-vous sûr de vouloir supprimer le marché: {{ marche.numero }}?\n\nCette action est irréversible!')) {
                                    console.log('Redirecting to delete URL...');
                                    window.location.href='{{ url_for('supprimer_marche', numero=marche.numero) }}';
                                }"
                                title="Supprimer">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                {% else %}
                  <tr>
                    <td colspan="8" class="text-center py-4 text-muted">
                      <i class="fas fa-inbox fa-2x mb-2"></i><br>
                      Aucun marché trouvé
                    </td>
                  </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
  .card {
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  }

  .table th {
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6 !important;
    font-weight: 600 !important;
    padding: 15px 12px !important;
    font-size: 0.9rem !important;
  }

  .table td {
    padding: 15px 12px !important;
    vertical-align: middle !important;
    border-bottom: 1px solid #dee2e6 !important;
  }

  .table-hover tbody tr:hover {
    background-color: rgba(0,123,255,0.05) !important;
  }

  .btn-group .btn {
    margin: 0 1px !important;
    border-radius: 6px !important;
  }

  .badge {
    font-size: 0.75rem !important;
    padding: 6px 10px !important;
    font-weight: 500 !important;
  }

  .text-white-75 {
    color: rgba(255,255,255,0.75) !important;
  }

  .text-white-50 {
    color: rgba(255,255,255,0.5) !important;
  }

  .card-header {
    border-bottom: 1px solid rgba(0,0,0,0.125) !important;
    padding: 1rem 1.25rem !important;
  }

  .card-body {
    padding: 1.25rem !important;
  }

  @media (max-width: 768px) {
    .btn-group {
      flex-direction: column !important;
    }

    .btn-group .btn {
      margin: 2px 0 !important;
    }

    .table-responsive {
      font-size: 0.85rem !important;
    }

    .card-body {
      padding: 1rem !important;
    }
  }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const searchInput = document.getElementById('searchMarche');
        const filterDomaine = document.getElementById('filterDomaine');
        const filterClient = document.getElementById('filterClient');
        const resetBtn = document.getElementById('resetFilters');
        const table = document.getElementById('marchesTable');
        const rows = table.querySelectorAll('tbody tr');

        // Fonction de filtrage en temps réel
        function filterTable() {
            const searchTerm = searchInput.value.toLowerCase();
            const selectedDomaine = filterDomaine.value.toLowerCase();
            const clientFilter = filterClient.value.toLowerCase();

            let visibleCount = 0;

            rows.forEach(row => {
                if (row.querySelector('td[colspan]')) return; // Skip "no data" row

                const cells = row.querySelectorAll('td');
                const numero = cells[0]?.textContent.toLowerCase() || '';
                const domaine = cells[1]?.textContent.toLowerCase() || '';
                const date = cells[2]?.textContent.toLowerCase() || '';
                const objet = cells[3]?.textContent.toLowerCase() || '';
                const client = cells[4]?.textContent.toLowerCase() || '';
                const montant = cells[5]?.textContent.toLowerCase() || '';

                // Recherche globale
                const matchesSearch = !searchTerm ||
                    numero.includes(searchTerm) ||
                    domaine.includes(searchTerm) ||
                    date.includes(searchTerm) ||
                    objet.includes(searchTerm) ||
                    client.includes(searchTerm) ||
                    montant.includes(searchTerm);

                // Filtre par domaine
                const matchesDomaine = !selectedDomaine || domaine.includes(selectedDomaine);

                // Filtre par client
                const matchesClient = !clientFilter || client.includes(clientFilter);

                const isVisible = matchesSearch && matchesDomaine && matchesClient;

                row.style.display = isVisible ? '' : 'none';
                if (isVisible) visibleCount++;
            });

            // Mettre à jour le compteur
            updateCounter(visibleCount);
        }

        // Mettre à jour le compteur de résultats
        function updateCounter(count) {
            const badge = document.querySelector('.badge.bg-light.text-dark');
            if (badge) {
                badge.textContent = `${count} marché${count !== 1 ? 's' : ''}`;
            }
        }

        // Événements de filtrage en temps réel
        searchInput.addEventListener('input', filterTable);
        filterDomaine.addEventListener('change', filterTable);
        filterClient.addEventListener('input', filterTable);

        // Reset des filtres
        resetBtn.addEventListener('click', function() {
            searchInput.value = '';
            filterDomaine.value = '';
            filterClient.value = '';
            filterTable();

            // Animation de reset
            [searchInput, filterDomaine, filterClient].forEach(element => {
                element.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    element.style.transform = 'scale(1)';
                }, 150);
            });
        });

        // Export Excel
        document.getElementById('exportExcel').addEventListener('click', function() {
            exportTableToExcel('marchesTable', 'marches');
        });

        // Fonction d'export Excel
        function exportTableToExcel(tableId, filename) {
            const table = document.getElementById(tableId);
            const visibleRows = Array.from(table.querySelectorAll('tbody tr')).filter(row =>
                row.style.display !== 'none' && !row.querySelector('td[colspan]')
            );

            if (visibleRows.length === 0) {
                alert('Aucune donnée à exporter');
                return;
            }

            let csv = 'N° Marché,Domaine,Date,Objet,Client,Montant,Délai\n';

            visibleRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                const rowData = [
                    cells[0]?.textContent.trim() || '',
                    cells[1]?.textContent.trim().replace(/\s+/g, ' ') || '',
                    cells[2]?.textContent.trim() || '',
                    cells[3]?.textContent.trim() || '',
                    cells[4]?.textContent.trim() || '',
                    cells[5]?.textContent.trim() || '',
                    cells[6]?.textContent.trim() || ''
                ];
                csv += rowData.map(field => `"${field}"`).join(',') + '\n';
            });

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `${filename}_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }

        // Animation d'entrée pour les lignes
        rows.forEach((row, index) => {
            row.style.opacity = '0';
            row.style.transform = 'translateY(20px)';
            setTimeout(() => {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '1';
                row.style.transform = 'translateY(0)';
            }, index * 50);
        });




    });
</script>
{% endblock %}
