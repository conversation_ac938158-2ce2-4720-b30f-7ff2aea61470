#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أزرار الحذف والتعديل في جداول التفاصيل
"""

import os

def test_edit_delete_buttons():
    """اختبار أزرار الحذف والتعديل"""
    
    print("🔍 اختبار أزرار الحذف والتعديل في جداول التفاصيل")
    print("=" * 60)
    
    # فحص الملفات المحدثة
    files_to_check = {
        'app.py': {
            'description': 'ملف التطبيق الرئيسي',
            'routes_to_check': [
                'modifier_detail_reclamation',
                'supprimer_detail_reclamation', 
                'modifier_detail_intervention'
            ]
        },
        'templates/interventions/svs.html': {
            'description': 'قالب تفاصيل إنترفنشن SVS',
            'elements_to_check': [
                'Actions</th>',
                'btn-group',
                'btn-warning',
                'btn-danger',
                'fas fa-edit',
                'fas fa-trash',
                'confirmDelete'
            ]
        },
        'templates/voir_reclamation.html': {
            'description': 'قالب عرض تفاصيل الريكلاماسيون',
            'elements_to_check': [
                'btn-group',
                'btn-warning',
                'btn-danger',
                'fas fa-edit',
                'fas fa-trash',
                'confirmDelete'
            ]
        },
        'templates/modifier_detail_reclamation.html': {
            'description': 'قالب تعديل تفاصيل الريكلاماسيون',
            'elements_to_check': [
                'modifier_detail_reclamation',
                'form method="POST"',
                'btn btn-primary',
                'Enregistrer les modifications'
            ]
        },
        'templates/modifier_detail_intervention.html': {
            'description': 'قالب تعديل تفاصيل الإنترفنشن',
            'elements_to_check': [
                'modifier_detail_intervention',
                'form method="POST"',
                'btn btn-primary',
                'Enregistrer les modifications'
            ]
        }
    }
    
    total_files = len(files_to_check)
    successful_files = 0
    
    for file_path, info in files_to_check.items():
        print(f"\n📄 فحص {info['description']}:")
        print(f"   📁 {file_path}")
        
        if not os.path.exists(file_path):
            print(f"   ❌ الملف غير موجود")
            continue
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        file_success = True
        
        if 'routes_to_check' in info:
            # فحص routes في app.py
            for route in info['routes_to_check']:
                if f"def {route}(" in content:
                    print(f"   ✅ Route {route} موجود")
                else:
                    print(f"   ❌ Route {route} غير موجود")
                    file_success = False
        
        if 'elements_to_check' in info:
            # فحص العناصر في القوالب
            for element in info['elements_to_check']:
                if element in content:
                    print(f"   ✅ {element} موجود")
                else:
                    print(f"   ❌ {element} غير موجود")
                    file_success = False
        
        if file_success:
            successful_files += 1
            print(f"   🎉 الملف محدث بنجاح")
        else:
            print(f"   ⚠️ الملف يحتاج مراجعة")
    
    # إحصائيات عامة
    print(f"\n📊 الإحصائيات العامة:")
    print(f"   📁 إجمالي الملفات: {total_files}")
    print(f"   ✅ ملفات محدثة بنجاح: {successful_files}")
    print(f"   ❌ ملفات تحتاج مراجعة: {total_files - successful_files}")
    print(f"   📈 نسبة النجاح: {(successful_files/total_files)*100:.1f}%")
    
    # فحص خاص للأزرار
    print(f"\n🔧 فحص خاص للأزرار:")
    
    # فحص قالب SVS
    svs_template = 'templates/interventions/svs.html'
    if os.path.exists(svs_template):
        with open(svs_template, 'r', encoding='utf-8') as f:
            svs_content = f.read()
        
        button_checks = [
            ('زر العرض', 'btn-info'),
            ('زر التعديل', 'btn-warning'),
            ('زر الحذف', 'btn-danger'),
            ('أيقونة العين', 'fas fa-eye'),
            ('أيقونة التعديل', 'fas fa-edit'),
            ('أيقونة الحذف', 'fas fa-trash'),
            ('عمود الأكشن', 'Actions</th>'),
            ('مجموعة الأزرار', 'btn-group')
        ]
        
        svs_buttons_found = 0
        for check_name, check_element in button_checks:
            if check_element in svs_content:
                print(f"   ✅ SVS: {check_name} موجود")
                svs_buttons_found += 1
            else:
                print(f"   ❌ SVS: {check_name} غير موجود")
        
        print(f"   📊 SVS: {svs_buttons_found}/{len(button_checks)} أزرار موجودة")
    
    # فحص قالب الريكلاماسيون
    reclamation_template = 'templates/voir_reclamation.html'
    if os.path.exists(reclamation_template):
        with open(reclamation_template, 'r', encoding='utf-8') as f:
            reclamation_content = f.read()
        
        reclamation_buttons_found = 0
        for check_name, check_element in button_checks:
            if check_element in reclamation_content:
                print(f"   ✅ Réclamation: {check_name} موجود")
                reclamation_buttons_found += 1
            else:
                print(f"   ❌ Réclamation: {check_name} غير موجود")
        
        print(f"   📊 Réclamation: {reclamation_buttons_found}/{len(button_checks)} أزرار موجودة")
    
    # النتيجة النهائية
    print(f"\n🎯 النتيجة النهائية:")
    print("=" * 60)
    
    if successful_files == total_files:
        print(f"✅ جميع الملفات محدثة بنجاح!")
        print(f"🎉 أزرار الحذف والتعديل متاحة في جداول التفاصيل")
        print(f"🚀 النظام جاهز للاستخدام")
        
        print(f"\n💡 الميزات المضافة:")
        print(f"   🔧 أزرار تعديل وحذف في جدول SVS")
        print(f"   🔧 أزرار تعديل وحذف في جدول الريكلاماسيون")
        print(f"   📝 قوالب تعديل التفاصيل")
        print(f"   🗑️ تأكيد الحذف بـ JavaScript")
        print(f"   👁️ عرض التفاصيل في modals")
        
        print(f"\n🎯 كيفية الاستخدام:")
        print(f"   1. اذهب إلى تفاصيل إنترفنشن SVS أو ريكلاماسيون")
        print(f"   2. في جدول التفاصيل، ستجد 3 أزرار لكل صف:")
        print(f"      👁️ عرض التفاصيل الكاملة")
        print(f"      ✏️ تعديل التفاصيل")
        print(f"      🗑️ حذف التفاصيل")
        print(f"   3. عند الحذف، ستظهر رسالة تأكيد")
        print(f"   4. عند التعديل، ستنتقل لصفحة التعديل")
        
        return True
    else:
        print(f"⚠️ بعض الملفات تحتاج مراجعة")
        print(f"🔧 راجع الملفات المذكورة أعلاه")
        return False

def check_routes_functionality():
    """فحص وظائف الـ routes"""
    
    print(f"\n🔗 فحص وظائف الـ Routes:")
    print("-" * 60)
    
    routes_info = {
        'modifier_detail_reclamation': {
            'method': 'GET, POST',
            'function': 'تعديل تفاصيل الريكلاماسيون',
            'template': 'modifier_detail_reclamation.html'
        },
        'supprimer_detail_reclamation': {
            'method': 'GET',
            'function': 'حذف تفاصيل الريكلاماسيون',
            'redirect': 'voir_reclamation'
        },
        'modifier_detail_intervention': {
            'method': 'GET, POST',
            'function': 'تعديل تفاصيل الإنترفنشن',
            'template': 'modifier_detail_intervention.html'
        }
    }
    
    if os.path.exists('app.py'):
        with open('app.py', 'r', encoding='utf-8') as f:
            app_content = f.read()
        
        for route_name, route_info in routes_info.items():
            print(f"\n🔗 Route: {route_name}")
            print(f"   📋 الوظيفة: {route_info['function']}")
            print(f"   🔧 الطريقة: {route_info['method']}")
            
            if f"def {route_name}(" in app_content:
                print(f"   ✅ Route موجود في app.py")
                
                if 'template' in route_info:
                    template_path = f"templates/{route_info['template']}"
                    if os.path.exists(template_path):
                        print(f"   ✅ القالب {route_info['template']} موجود")
                    else:
                        print(f"   ❌ القالب {route_info['template']} غير موجود")
                
                if 'redirect' in route_info:
                    if f"redirect(url_for('{route_info['redirect']}'" in app_content:
                        print(f"   ✅ Redirect إلى {route_info['redirect']} موجود")
                    else:
                        print(f"   ❌ Redirect إلى {route_info['redirect']} غير موجود")
            else:
                print(f"   ❌ Route غير موجود في app.py")

if __name__ == "__main__":
    # اختبار أزرار الحذف والتعديل
    success = test_edit_delete_buttons()
    
    # فحص وظائف الـ routes
    check_routes_functionality()
    
    print(f"\n🎊 انتهى الاختبار!")
    print("=" * 60)
    
    if success:
        print(f"🎉 تم إضافة أزرار الحذف والتعديل بنجاح!")
        print(f"✨ جداول التفاصيل محدثة ومكتملة")
    else:
        print(f"⚠️ بعض التحديثات تحتاج مراجعة")
        print(f"🔧 راجع الملفات المذكورة أعلاه")
