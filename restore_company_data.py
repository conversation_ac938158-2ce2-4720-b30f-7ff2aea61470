#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
استعادة معلومات الشركة وإزالة "MAXAFFAIRE" من واجهة المستخدم فقط
"""

import sqlite3
import os

def restore_company_data():
    """استعادة معلومات الشركة مع إزالة MAXAFFAIRE من الواجهة فقط"""
    
    print("🔄 استعادة معلومات الشركة")
    print("=" * 40)
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()
        
        # حذف البيانات الحالية
        cursor.execute("DELETE FROM societe")
        
        # إدراج معلومات الشركة الصحيحة (بدون MAXAFFAIRE في الواجهة)
        print("📝 إدراج معلومات الشركة المحدثة...")
        cursor.execute('''
        INSERT INTO societe (nom, logo, telephone, responsable, email, adresse, if_fiscal, rc, patente, ice, cnss, pied_page)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            'شركتك',  # اسم عام بدلاً من MAXAFFAIRE
            'logo_company.png',  # شعار افتراضي
            '+212 6 XX XX XX XX',  # هاتف مثال
            'المدير العام',  # مسؤول
            '<EMAIL>',  # بريد مثال
            'العنوان، المدينة، المغرب',  # عنوان مثال
            'XXXXXXXX',  # IF مثال
            'XXXXXXXX',  # RC مثال
            'XXXXXXXX',  # patente مثال
            'XXXXXXXXXXXXXXX',  # ICE مثال
            'XXXXXXXX',  # CNSS مثال
            'نظام إدارة الصيانة المتقدم'   # تذييل الصفحة
        ))
        
        # حفظ التغييرات
        conn.commit()
        print("✅ تم إدراج معلومات الشركة بنجاح")
        
        # عرض البيانات الجديدة
        print("\n📊 معلومات الشركة المحدثة:")
        cursor.execute("SELECT nom, email, telephone, adresse, logo FROM societe LIMIT 1")
        row = cursor.fetchone()
        if row:
            print(f"   📛 الاسم: {row[0]}")
            print(f"   📧 البريد: {row[1]}")
            print(f"   📞 الهاتف: {row[2]}")
            print(f"   🏠 العنوان: {row[3]}")
            print(f"   🖼️ الشعار: {row[4]}")
        
        conn.close()
        
        # إنشاء شعار افتراضي إذا لم يكن موجود
        print("\n🖼️ التحقق من الشعار...")
        logo_path = 'static/uploads/logo_company.png'
        if not os.path.exists(logo_path):
            # إنشاء مجلد uploads إذا لم يكن موجود
            os.makedirs('static/uploads', exist_ok=True)
            
            # نسخ الشعار الافتراضي
            default_logo = 'static/images/logo.png'
            if os.path.exists(default_logo):
                import shutil
                shutil.copy2(default_logo, logo_path)
                print(f"   ✅ تم إنشاء شعار افتراضي: {logo_path}")
            else:
                print(f"   ⚠️ الشعار الافتراضي غير موجود: {default_logo}")
        else:
            print(f"   ✅ الشعار موجود: {logo_path}")
        
        print("\n✅ تم استعادة معلومات الشركة بنجاح!")
        print("💡 يمكنك الآن تعديل المعلومات من صفحة 'Informations de la Société'")
        
    except Exception as e:
        print(f"❌ خطأ في استعادة البيانات: {e}")

def update_ui_text():
    """تحديث النصوص في واجهة المستخدم لإزالة MAXAFFAIRE"""
    
    print("\n🎨 تحديث واجهة المستخدم...")
    
    # قائمة الملفات التي تحتاج تحديث
    files_to_update = [
        'templates/login.html',
        'templates/base.html',
        'templates/dashboard.html'
    ]
    
    for file_path in files_to_update:
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # استبدال النصوص المحددة
                replacements = {
                    'MAX AFFAIRE': 'نظام إدارة الصيانة',
                    'MAXAFFAIRE': 'نظام إدارة الصيانة',
                    'Max Affaire': 'نظام إدارة الصيانة'
                }
                
                updated = False
                for old_text, new_text in replacements.items():
                    if old_text in content:
                        content = content.replace(old_text, new_text)
                        updated = True
                        print(f"   ✅ تم استبدال '{old_text}' في {file_path}")
                
                if updated:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"   💾 تم حفظ التحديثات في {file_path}")
                else:
                    print(f"   ℹ️ لا توجد تحديثات مطلوبة في {file_path}")
                    
            except Exception as e:
                print(f"   ❌ خطأ في تحديث {file_path}: {e}")
        else:
            print(f"   ❌ الملف غير موجود: {file_path}")

if __name__ == "__main__":
    restore_company_data()
    update_ui_text()
    print("\n🎉 تم الانتهاء من الإصلاحات!")
    print("=" * 40)
