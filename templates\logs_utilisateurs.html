{% extends "base.html" %}

{% block title %}Journal des Activités - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Journal des Activités des Utilisateurs{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header amélioré avec boutons d'action -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient-primary text-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="icon-circle bg-white bg-opacity-20 me-3">
                                <i class="fas fa-history text-white"></i>
                            </div>
                            <div>
                                <h4 class="mb-0 fw-bold">Journal des Activités des Utilisateurs</h4>
                                <small class="opacity-75 text-black">Suivi et monitoring des actions utilisateurs</small>
                            </div>
                        </div>
                        <div class="header-right">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-danger btn-sm" onclick="showBulkDeleteModal()">
                                    <i class="fas fa-trash-alt"></i> حذف كلي
                                </button>
                                <button type="button" class="btn btn-success btn-sm" onclick="exportToExcel('logs-table', 'journal_activites')">
                                    <i class="fas fa-file-excel"></i> Excel
                                </button>
                                <button type="button" class="btn btn-info btn-sm" onclick="refreshPage()">
                                    <i class="fas fa-sync-alt"></i> Actualiser
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filtres améliorés -->
                <div class="card-body bg-gradient-light">
                    <div class="row align-items-center mb-3">
                        <div class="col-md-6">
                            <h6 class="mb-0 text-dark fw-bold">
                                <i class="fas fa-filter me-2 text-primary"></i>Filtres de recherche
                            </h6>
                            <small class="text-muted">Affinez votre recherche dans le journal des activités</small>
                        </div>
                        <div class="col-md-6 text-end">
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-info" onclick="setQuickFilter('today')">
                                    <i class="fas fa-calendar-day me-1"></i>Aujourd'hui
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="setQuickFilter('week')">
                                    <i class="fas fa-calendar-week me-1"></i>Cette semaine
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="setQuickFilter('month')">
                                    <i class="fas fa-calendar-alt me-1"></i>Ce mois
                                </button>
                            </div>
                        </div>
                    </div>
                    <form method="GET" class="row g-3">
                        <div class="col-lg-2 col-md-4">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-user me-1 text-primary"></i>Utilisateur
                            </label>
                            <select name="utilisateur" class="form-select form-select-modern">
                                <option value="">Tous les utilisateurs</option>
                                {% for utilisateur in utilisateurs %}
                                <option value="{{ utilisateur }}" {{ 'selected' if filters.utilisateur == utilisateur else '' }}>
                                    {{ utilisateur }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="col-lg-2 col-md-4">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-cube me-1 text-success"></i>Module
                            </label>
                            <select name="module" class="form-select form-select-modern">
                                <option value="">Tous les modules</option>
                                {% for module in modules %}
                                <option value="{{ module }}" {{ 'selected' if filters.module == module else '' }}>
                                    {{ module }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="col-lg-2 col-md-4">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-cog me-1 text-warning"></i>Action
                            </label>
                            <select name="action" class="form-select form-select-modern">
                                <option value="">Toutes les actions</option>
                                {% for action in actions %}
                                <option value="{{ action }}" {{ 'selected' if filters.action == action else '' }}>
                                    {{ action }}
                                </option>
                                {% endfor %}
                            </select>
                        </div>

                        <div class="col-lg-2 col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-calendar-plus me-1 text-info"></i>Date début
                            </label>
                            <input type="date" name="date_debut" class="form-control form-control-modern" value="{{ filters.date_debut }}">
                        </div>

                        <div class="col-lg-2 col-md-6">
                            <label class="form-label fw-semibold">
                                <i class="fas fa-calendar-minus me-1 text-info"></i>Date fin
                            </label>
                            <input type="date" name="date_fin" class="form-control form-control-modern" value="{{ filters.date_fin }}">
                        </div>

                        <div class="col-lg-2 col-md-12 d-flex align-items-end">
                            <div class="btn-group w-100">
                                <button type="submit" class="btn btn-primary btn-modern">
                                    <i class="fas fa-search me-1"></i> Filtrer
                                </button>
                                <a href="{{ url_for('logs_utilisateurs') }}" class="btn btn-outline-secondary btn-modern">
                                    <i class="fas fa-times me-1"></i> Reset
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques rapides améliorées -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card-modern border-0 shadow-sm h-100">
                <div class="card-body text-center p-4">
                    <div class="stat-icon-wrapper mb-3">
                        <div class="stat-icon bg-primary bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center">
                            <i class="fas fa-list-alt text-primary"></i>
                        </div>
                    </div>
                    <h3 class="fw-bold text-primary mb-1">{{ logs|length }}</h3>
                    <p class="text-muted mb-0 fw-semibold">Activités affichées</p>
                    <small class="text-muted">Entrées dans le journal</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card-modern border-0 shadow-sm h-100">
                <div class="card-body text-center p-4">
                    <div class="stat-icon-wrapper mb-3">
                        <div class="stat-icon bg-info bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center">
                            <i class="fas fa-users text-info"></i>
                        </div>
                    </div>
                    <h3 class="fw-bold text-info mb-1">{{ utilisateurs|length }}</h3>
                    <p class="text-muted mb-0 fw-semibold">Utilisateurs actifs</p>
                    <small class="text-muted">Comptes avec activité</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card-modern border-0 shadow-sm h-100">
                <div class="card-body text-center p-4">
                    <div class="stat-icon-wrapper mb-3">
                        <div class="stat-icon bg-success bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center">
                            <i class="fas fa-cubes text-success"></i>
                        </div>
                    </div>
                    <h3 class="fw-bold text-success mb-1">{{ modules|length }}</h3>
                    <p class="text-muted mb-0 fw-semibold">Modules utilisés</p>
                    <small class="text-muted">Fonctionnalités accédées</small>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card stat-card-modern border-0 shadow-sm h-100">
                <div class="card-body text-center p-4">
                    <div class="stat-icon-wrapper mb-3">
                        <div class="stat-icon bg-warning bg-opacity-10 rounded-circle d-inline-flex align-items-center justify-content-center">
                            <i class="fas fa-cogs text-warning"></i>
                        </div>
                    </div>
                    <h3 class="fw-bold text-warning mb-1">{{ actions|length }}</h3>
                    <p class="text-muted mb-0 fw-semibold">Types d'actions</p>
                    <small class="text-muted">Opérations différentes</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Tableau des logs amélioré -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient-secondary text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="d-flex align-items-center">
                            <div class="icon-circle bg-white bg-opacity-20 me-3">
                                <i class="fas fa-list text-white"></i>
                            </div>
                            <div>
                                <h6 class="mb-0 fw-bold">Historique des Activités</h6>
                                <small class="opacity-75">{{ logs|length }} entrées trouvées</small>
                            </div>
                        </div>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-light btn-sm" onclick="toggleTableView()">
                                <i class="fas fa-th-list me-1"></i>Vue
                            </button>
                            <button type="button" class="btn btn-light btn-sm" onclick="exportTableData()">
                                <i class="fas fa-download me-1"></i>Export
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-modern table-hover mb-0" id="logs-table">
                            <thead class="table-dark">
                                <tr>
                                    <th>ID</th>
                                    <th>Date & Heure</th>
                                    <th>Utilisateur</th>
                                    <th>Nom Complet</th>
                                    <th>Action</th>
                                    <th>Module</th>
                                    <th>Détails</th>
                                    <th>Adresse IP</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs %}
                                <tr>
                                    <td>{{ log.id }}</td>
                                    <td>
                                        <div class="d-flex flex-column">
                                            <span class="fw-bold">{{ log.date_creation.split(' ')[0] }}</span>
                                            <small class="text-muted">{{ log.date_creation.split(' ')[1] if ' ' in log.date_creation else '' }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">{{ log.nom_utilisateur }}</span>
                                    </td>
                                    <td>{{ log.nom_complet }}</td>
                                    <td>
                                        {% if log.action == 'Connexion' %}
                                            <span class="badge bg-success">{{ log.action }}</span>
                                        {% elif log.action == 'Déconnexion' %}
                                            <span class="badge bg-warning">{{ log.action }}</span>
                                        {% elif log.action == 'Création' %}
                                            <span class="badge bg-info">{{ log.action }}</span>
                                        {% elif log.action == 'Modification' %}
                                            <span class="badge bg-primary">{{ log.action }}</span>
                                        {% elif log.action == 'Suppression' %}
                                            <span class="badge bg-danger">{{ log.action }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ log.action }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="badge bg-outline-dark">{{ log.module }}</span>
                                    </td>
                                    <td>
                                        {% if log.details %}
                                            <span class="text-truncate d-inline-block" style="max-width: 200px;" title="{{ log.details }}">
                                                {{ log.details }}
                                            </span>
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <small class="text-muted">{{ log.adresse_ip }}</small>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-inbox fa-3x mb-3"></i>
                                            <p>Aucune activité trouvée avec les filtres sélectionnés.</p>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .table th {
        font-weight: 600;
        font-size: 0.9rem;
    }

    .table td {
        vertical-align: middle;
        font-size: 0.9rem;
    }

    .badge {
        font-size: 0.8rem;
    }

    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .table-responsive {
        max-height: 600px;
        overflow-y: auto;
    }

    @media print {
        .btn-group, .card-body.bg-light {
            display: none !important;
        }

        .table {
            font-size: 0.8rem;
        }
    }

    /* Boutons modernes */
    .btn-modern {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: none;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .btn-modern:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn-modern i {
        font-size: 0.9em;
    }

    /* Couleurs spécifiques */
    .btn-primary.btn-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .btn-success.btn-modern {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .btn-danger.btn-modern {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .btn-warning.btn-modern {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #333;
    }

    .btn-info.btn-modern {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
    }

    .btn-outline-primary.btn-modern {
        border: 2px solid #667eea;
        color: #667eea;
        background: transparent;
    }

    .btn-outline-primary.btn-modern:hover {
        background: #667eea;
        color: white;
    }


    /* Tableaux améliorés */
    .table-modern {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .table-modern thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
        border: none;
        padding: 15px 12px;
    }

    .table-modern tbody tr {
        transition: all 0.3s ease;
    }

    .table-modern tbody tr:hover {
        background-color: #f8f9fa;
        transform: scale(1.01);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .table-modern tbody td {
        padding: 12px;
        vertical-align: middle;
        border-color: #e9ecef;
    }

    .table-modern .badge {
        font-size: 0.75rem;
        padding: 0.5em 0.75em;
        border-radius: 6px;
    }

    /* Pagination améliorée */
    .pagination-modern .page-link {
        border-radius: 6px;
        margin: 0 2px;
        border: none;
        color: #667eea;
        font-weight: 500;
    }

    .pagination-modern .page-link:hover {
        background-color: #667eea;
        color: white;
        transform: translateY(-1px);
    }

    .pagination-modern .page-item.active .page-link {
        background-color: #667eea;
        border-color: #667eea;
    }

    /* Styles pour les nouvelles améliorations */
    .bg-gradient-primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .bg-gradient-secondary {
        background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    }

    .bg-gradient-light {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    }

    .icon-circle {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
    }

    .stat-card-modern {
        transition: all 0.3s ease;
        border-radius: 15px;
        overflow: hidden;
    }

    .stat-card-modern:hover {
        transform: translateY(-5px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    }

    .stat-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .form-select-modern, .form-control-modern {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        transition: all 0.3s ease;
    }

    .form-select-modern:focus, .form-control-modern:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        transform: translateY(-1px);
    }

    .table-modern {
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .table-modern thead th {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.85rem;
        letter-spacing: 0.5px;
        border: none;
        padding: 15px 12px;
    }

    .table-modern tbody tr {
        transition: all 0.3s ease;
    }

    .table-modern tbody tr:hover {
        background-color: #f8f9fa;
        transform: scale(1.01);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .table-modern tbody td {
        padding: 12px;
        vertical-align: middle;
        border-color: #e9ecef;
    }

    .table-modern .badge {
        font-size: 0.75rem;
        padding: 0.5em 0.75em;
        border-radius: 6px;
    }

    /* Animation pour les cartes de statistiques */
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .stat-card-modern {
        animation: fadeInUp 0.6s ease-out;
    }

    .stat-card-modern:nth-child(1) { animation-delay: 0.1s; }
    .stat-card-modern:nth-child(2) { animation-delay: 0.2s; }
    .stat-card-modern:nth-child(3) { animation-delay: 0.3s; }
    .stat-card-modern:nth-child(4) { animation-delay: 0.4s; }

</style>
{% endblock %}

{% block extra_js %}
<script>
function refreshPage() {
    // Ajouter un effet de chargement
    const refreshBtn = document.querySelector('[onclick="refreshPage()"]');
    if (refreshBtn) {
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Actualisation...';
        refreshBtn.disabled = true;
    }

    setTimeout(() => {
        window.location.reload();
    }, 500);
}

// Fonction pour les filtres rapides
function setQuickFilter(period) {
    const today = new Date();
    let startDate, endDate = today.toISOString().split('T')[0];

    switch(period) {
        case 'today':
            startDate = endDate;
            break;
        case 'week':
            const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
            startDate = weekAgo.toISOString().split('T')[0];
            break;
        case 'month':
            const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            startDate = monthAgo.toISOString().split('T')[0];
            break;
    }

    document.querySelector('input[name="date_debut"]').value = startDate;
    document.querySelector('input[name="date_fin"]').value = endDate;

    // Soumettre automatiquement le formulaire
    document.querySelector('form').submit();
}



// Fonction pour basculer la vue du tableau
function toggleTableView() {
    const table = document.getElementById('logs-table');
    const isCompact = table.classList.contains('table-sm');

    if (isCompact) {
        table.classList.remove('table-sm');
        table.classList.add('table-lg');
    } else {
        table.classList.add('table-sm');
        table.classList.remove('table-lg');
    }
}

// Fonction pour exporter les données du tableau
function exportTableData() {
    const table = document.getElementById('logs-table');
    const rows = Array.from(table.querySelectorAll('tr'));

    let csv = '';
    rows.forEach(row => {
        const cells = Array.from(row.querySelectorAll('th, td'));
        const rowData = cells.map(cell => `"${cell.textContent.trim()}"`).join(',');
        csv += rowData + '\n';
    });

    const blob = new Blob([csv], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `journal_activites_${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
}

// Auto-refresh every 30 seconds
setInterval(function() {
    // Only refresh if no filters are applied
    const urlParams = new URLSearchParams(window.location.search);
    if (!urlParams.toString()) {
        // Ajouter un indicateur visuel de refresh automatique
        const indicator = document.createElement('div');
        indicator.className = 'position-fixed top-0 end-0 m-3 alert alert-info alert-dismissible fade show';
        indicator.innerHTML = `
            <i class="fas fa-sync-alt fa-spin me-2"></i>
            Actualisation automatique...
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.appendChild(indicator);

        setTimeout(() => {
            refreshPage();
        }, 2000);
    }
}, 30000);

// Initialize tooltips et animations
document.addEventListener('DOMContentLoaded', function() {
    // Tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Animation des cartes de statistiques au scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.animationPlayState = 'running';
            }
        });
    }, observerOptions);

    document.querySelectorAll('.stat-card-modern').forEach(card => {
        card.style.animationPlayState = 'paused';
        observer.observe(card);
    });

    // Améliorer les interactions des formulaires
    document.querySelectorAll('.form-select-modern, .form-control-modern').forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
});

// دالة إظهار modal الحذف الكلي
function showBulkDeleteModal() {
    const modal = document.getElementById('bulkDeleteModal');
    if (modal) {
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
    }
}

// دالة تغيير نوع التاريخ
function changeDateType() {
    const dateType = document.getElementById('dateType').value;
    const singleDateDiv = document.getElementById('singleDateDiv');
    const dateRangeDiv = document.getElementById('dateRangeDiv');

    // إخفاء جميع الحقول أولاً
    singleDateDiv.style.display = 'none';
    dateRangeDiv.style.display = 'none';

    // إظهار الحقول المناسبة
    if (dateType === 'before' || dateType === 'after' || dateType === 'specific') {
        singleDateDiv.style.display = 'block';
    } else if (dateType === 'between') {
        dateRangeDiv.style.display = 'block';
    }
}

// دالة تأكيد الحذف
function confirmBulkDelete() {
    const dateType = document.getElementById('dateType').value;
    let confirmMessage = '';

    switch(dateType) {
        case 'all':
            confirmMessage = 'هل أنت متأكد من حذف جميع سجلات الأنشطة؟';
            break;
        case 'before':
            confirmMessage = 'هل أنت متأكد من حذف جميع السجلات قبل التاريخ المحدد؟';
            break;
        case 'after':
            confirmMessage = 'هل أنت متأكد من حذف جميع السجلات بعد التاريخ المحدد؟';
            break;
        case 'specific':
            confirmMessage = 'هل أنت متأكد من حذف جميع السجلات في التاريخ المحدد؟';
            break;
        case 'between':
            confirmMessage = 'هل أنت متأكد من حذف جميع السجلات بين التاريخين المحددين؟';
            break;
    }

    if (confirm(confirmMessage + '\n\nهذا الإجراء لا يمكن التراجع عنه!')) {
        document.getElementById('bulkDeleteForm').submit();
    }
}
</script>

<!-- Modal للحذف الكلي -->
<div class="modal fade" id="bulkDeleteModal" tabindex="-1" aria-labelledby="bulkDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="bulkDeleteModalLabel">
                    <i class="fas fa-trash-alt me-2"></i>حذف كلي لسجلات الأنشطة
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>تحذير:</strong> هذا الإجراء سيحذف السجلات نهائياً ولا يمكن التراجع عنه!
                </div>

                <form id="bulkDeleteForm" method="POST" action="{{ url_for('delete_logs_by_date') }}">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="dateType" class="form-label fw-bold">نوع الحذف:</label>
                            <select class="form-select" id="dateType" name="date_type" onchange="changeDateType()" required>
                                <option value="">اختر نوع الحذف...</option>
                                <option value="all">حذف جميع السجلات</option>
                                <option value="before">حذف السجلات قبل تاريخ معين</option>
                                <option value="after">حذف السجلات بعد تاريخ معين</option>
                                <option value="specific">حذف السجلات في تاريخ محدد</option>
                                <option value="between">حذف السجلات بين تاريخين</option>
                            </select>
                        </div>
                    </div>

                    <!-- حقل التاريخ الواحد -->
                    <div id="singleDateDiv" style="display: none;">
                        <div class="row">
                            <div class="col-md-12 mb-3">
                                <label for="dateValue" class="form-label fw-bold">التاريخ:</label>
                                <input type="date" class="form-control" id="dateValue" name="date_value">
                            </div>
                        </div>
                    </div>

                    <!-- حقول نطاق التاريخ -->
                    <div id="dateRangeDiv" style="display: none;">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="dateStart" class="form-label fw-bold">من تاريخ:</label>
                                <input type="date" class="form-control" id="dateStart" name="date_start">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="dateEnd" class="form-label fw-bold">إلى تاريخ:</label>
                                <input type="date" class="form-control" id="dateEnd" name="date_end">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>إلغاء
                </button>
                <button type="button" class="btn btn-danger" onclick="confirmBulkDelete()">
                    <i class="fas fa-trash-alt me-1"></i>تأكيد الحذف
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}
