{% extends "base.html" %}

{% block title %}Modifier un Utilisateur - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Modifier un Utilisateur{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-user-edit"></i> Modifier l'utilisateur
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('modifier_utilisateur', id=utilisateur.id) }}">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="nom" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="nom" name="nom" value="{{ utilisateur.nom }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="prenom" class="form-label">Prénom</label>
                            <input type="text" class="form-control" id="prenom" name="prenom" value="{{ utilisateur.prenom }}" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ utilisateur.email }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">Rôle</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="user" {% if utilisateur.role == 'user' %}selected{% endif %}>Utilisateur</option>
                                <option value="admin" {% if utilisateur.role == 'admin' %}selected{% endif %}>Administrateur</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="nom_utilisateur" class="form-label">Nom d'utilisateur</label>
                            <input type="text" class="form-control" id="nom_utilisateur" value="{{ utilisateur.nom_utilisateur }}" disabled>
                            <small class="text-muted">Le nom d'utilisateur ne peut pas être modifié</small>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="mot_de_passe" class="form-label">Nouveau mot de passe (laisser vide pour ne pas changer)</label>
                            <input type="password" class="form-control" id="mot_de_passe" name="mot_de_passe">
                        </div>
                    </div>

                    <div class="mb-4">
                        <label class="form-label fw-bold">
                            <i class="fas fa-shield-alt text-primary"></i> Permissions et Accès
                        </label>
                        <div class="card border-light">
                            <div class="card-body">
                                <!-- Permissions Générales -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-cog"></i> Permissions Générales
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_dashboard" name="permissions[]" value="dashboard" checked>
                                                <label class="form-check-label" for="perm_dashboard">
                                                    <i class="fas fa-chart-line text-info"></i> Tableau de bord
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_carte" name="permissions[]" value="carte" checked>
                                                <label class="form-check-label" for="perm_carte">
                                                    <i class="fas fa-map-marked-alt text-success"></i> Carte du Maroc
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_societe" name="permissions[]" value="societe" checked>
                                                <label class="form-check-label" for="perm_societe">
                                                    <i class="fas fa-building text-warning"></i> Informations Société
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gestion des Marchés -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-handshake"></i> Gestion des Marchés
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_marches_view" name="permissions[]" value="marches_view" checked>
                                                <label class="form-check-label" for="perm_marches_view">
                                                    <i class="fas fa-eye text-info"></i> Consulter les marchés
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_marches_add" name="permissions[]" value="marches_add" checked>
                                                <label class="form-check-label" for="perm_marches_add">
                                                    <i class="fas fa-plus text-success"></i> Ajouter des marchés
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_marches_edit" name="permissions[]" value="marches_edit" checked>
                                                <label class="form-check-label" for="perm_marches_edit">
                                                    <i class="fas fa-edit text-warning"></i> Modifier les marchés
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_marches_delete" name="permissions[]" value="marches_delete">
                                                <label class="form-check-label" for="perm_marches_delete">
                                                    <i class="fas fa-trash text-danger"></i> Supprimer les marchés
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_marches_print" name="permissions[]" value="marches_print" checked>
                                                <label class="form-check-label" for="perm_marches_print">
                                                    <i class="fas fa-print text-secondary"></i> Imprimer les marchés
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_marches_export" name="permissions[]" value="marches_export" checked>
                                                <label class="form-check-label" for="perm_marches_export">
                                                    <i class="fas fa-file-excel text-success"></i> Exporter les marchés
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gestion des Interventions -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-tools"></i> Gestion des Interventions
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_interventions_view" name="permissions[]" value="interventions_view" checked>
                                                <label class="form-check-label" for="perm_interventions_view">
                                                    <i class="fas fa-eye text-info"></i> Consulter les interventions
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_interventions_add" name="permissions[]" value="interventions_add" checked>
                                                <label class="form-check-label" for="perm_interventions_add">
                                                    <i class="fas fa-plus text-success"></i> Ajouter des interventions
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_interventions_edit" name="permissions[]" value="interventions_edit" checked>
                                                <label class="form-check-label" for="perm_interventions_edit">
                                                    <i class="fas fa-edit text-warning"></i> Modifier les interventions
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_interventions_delete" name="permissions[]" value="interventions_delete">
                                                <label class="form-check-label" for="perm_interventions_delete">
                                                    <i class="fas fa-trash text-danger"></i> Supprimer les interventions
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_interventions_print" name="permissions[]" value="interventions_print" checked>
                                                <label class="form-check-label" for="perm_interventions_print">
                                                    <i class="fas fa-print text-secondary"></i> Imprimer les interventions
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_interventions_export" name="permissions[]" value="interventions_export" checked>
                                                <label class="form-check-label" for="perm_interventions_export">
                                                    <i class="fas fa-file-excel text-success"></i> Exporter les interventions
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gestion des Réclamations -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-exclamation-triangle"></i> Gestion des Réclamations
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_reclamations_view" name="permissions[]" value="reclamations_view" checked>
                                                <label class="form-check-label" for="perm_reclamations_view">
                                                    <i class="fas fa-eye text-info"></i> Consulter les réclamations
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_reclamations_add" name="permissions[]" value="reclamations_add" checked>
                                                <label class="form-check-label" for="perm_reclamations_add">
                                                    <i class="fas fa-plus text-success"></i> Ajouter des réclamations
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_reclamations_edit" name="permissions[]" value="reclamations_edit" checked>
                                                <label class="form-check-label" for="perm_reclamations_edit">
                                                    <i class="fas fa-edit text-warning"></i> Modifier les réclamations
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_reclamations_delete" name="permissions[]" value="reclamations_delete">
                                                <label class="form-check-label" for="perm_reclamations_delete">
                                                    <i class="fas fa-trash text-danger"></i> Supprimer les réclamations
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_reclamations_print" name="permissions[]" value="reclamations_print" checked>
                                                <label class="form-check-label" for="perm_reclamations_print">
                                                    <i class="fas fa-print text-secondary"></i> Imprimer les réclamations
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_reclamations_export" name="permissions[]" value="reclamations_export" checked>
                                                <label class="form-check-label" for="perm_reclamations_export">
                                                    <i class="fas fa-file-excel text-success"></i> Exporter les réclamations
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Gestion des Régions et Sites -->
                                <div class="mb-4">
                                    <h6 class="text-primary mb-3">
                                        <i class="fas fa-map-marker-alt"></i> Gestion des Régions et Sites
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_regions_view" name="permissions[]" value="regions_view" checked>
                                                <label class="form-check-label" for="perm_regions_view">
                                                    <i class="fas fa-eye text-info"></i> Consulter les régions
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_regions_manage" name="permissions[]" value="regions_manage" checked>
                                                <label class="form-check-label" for="perm_regions_manage">
                                                    <i class="fas fa-cogs text-warning"></i> Gérer les régions
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_sites_view" name="permissions[]" value="sites_view" checked>
                                                <label class="form-check-label" for="perm_sites_view">
                                                    <i class="fas fa-eye text-info"></i> Consulter les sites
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_sites_manage" name="permissions[]" value="sites_manage" checked>
                                                <label class="form-check-label" for="perm_sites_manage">
                                                    <i class="fas fa-cogs text-warning"></i> Gérer les sites
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Administration Système -->
                                <div class="mb-4">
                                    <h6 class="text-danger mb-3">
                                        <i class="fas fa-user-shield"></i> Administration Système
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_users_view" name="permissions[]" value="users_view">
                                                <label class="form-check-label" for="perm_users_view">
                                                    <i class="fas fa-users text-info"></i> Consulter les utilisateurs
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_users_manage" name="permissions[]" value="users_manage">
                                                <label class="form-check-label" for="perm_users_manage">
                                                    <i class="fas fa-user-cog text-warning"></i> Gérer les utilisateurs
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_logs_view" name="permissions[]" value="logs_view">
                                                <label class="form-check-label" for="perm_logs_view">
                                                    <i class="fas fa-history text-secondary"></i> Consulter les logs
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_system_config" name="permissions[]" value="system_config">
                                                <label class="form-check-label" for="perm_system_config">
                                                    <i class="fas fa-cog text-danger"></i> Configuration système
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-4 mb-2">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="perm_backup" name="permissions[]" value="backup">
                                                <label class="form-check-label" for="perm_backup">
                                                    <i class="fas fa-database text-primary"></i> Sauvegarde/Restauration
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Actions Rapides -->
                                <div class="mb-3">
                                    <h6 class="text-secondary mb-3">
                                        <i class="fas fa-magic"></i> Actions Rapides
                                    </h6>
                                    <div class="row">
                                        <div class="col-md-6 mb-2">
                                            <button type="button" class="btn btn-outline-success btn-sm w-100" onclick="selectAllPermissions()">
                                                <i class="fas fa-check-double"></i> Tout sélectionner
                                            </button>
                                        </div>
                                        <div class="col-md-6 mb-2">
                                            <button type="button" class="btn btn-outline-warning btn-sm w-100" onclick="deselectAllPermissions()">
                                                <i class="fas fa-times"></i> Tout désélectionner
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer les modifications
                        </button>
                        <a href="{{ url_for('utilisateurs') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Fonction pour sélectionner toutes les permissions
    function selectAllPermissions() {
        const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = true;
        });

        // Animation visuelle
        checkboxes.forEach((checkbox, index) => {
            setTimeout(() => {
                checkbox.parentElement.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    checkbox.parentElement.style.transform = 'scale(1)';
                }, 100);
            }, index * 20);
        });

        // Afficher un message de confirmation
        showToast('Toutes les permissions ont été sélectionnées', 'success');
    }

    // Fonction pour désélectionner toutes les permissions
    function deselectAllPermissions() {
        const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = false;
        });

        // Animation visuelle
        checkboxes.forEach((checkbox, index) => {
            setTimeout(() => {
                checkbox.parentElement.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    checkbox.parentElement.style.transform = 'scale(1)';
                }, 100);
            }, index * 20);
        });

        // Afficher un message de confirmation
        showToast('Toutes les permissions ont été désélectionnées', 'warning');
    }

    // Fonction pour afficher des messages toast
    function showToast(message, type = 'info') {
        // Créer l'élément toast
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Ajouter au body
        document.body.appendChild(toast);

        // Supprimer automatiquement après 3 secondes
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 3000);
    }

    // Fonction pour compter les permissions sélectionnées
    function updatePermissionCount() {
        const checkboxes = document.querySelectorAll('input[name="permissions[]"]');
        const checkedBoxes = document.querySelectorAll('input[name="permissions[]"]:checked');

        // Créer ou mettre à jour le compteur
        let counter = document.getElementById('permission-counter');
        if (!counter) {
            counter = document.createElement('div');
            counter.id = 'permission-counter';
            counter.className = 'alert alert-info mt-2';
            document.querySelector('.card-body').appendChild(counter);
        }

        counter.innerHTML = `
            <i class="fas fa-info-circle"></i>
            <strong>${checkedBoxes.length}</strong> sur <strong>${checkboxes.length}</strong> permissions sélectionnées
        `;
    }

    // Ajouter des événements aux checkboxes
    document.addEventListener('DOMContentLoaded', function() {
        const checkboxes = document.querySelectorAll('input[name="permissions[]"]');

        // Ajouter un événement à chaque checkbox
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', updatePermissionCount);
        });

        // Mettre à jour le compteur initial
        updatePermissionCount();

        // Ajouter des animations aux sections
        const sections = document.querySelectorAll('.mb-4');
        sections.forEach((section, index) => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(20px)';
            section.style.transition = 'all 0.3s ease';

            setTimeout(() => {
                section.style.opacity = '1';
                section.style.transform = 'translateY(0)';
            }, index * 100);
        });
    });

    // Validation du formulaire
    document.querySelector('form').addEventListener('submit', function(e) {
        const checkedBoxes = document.querySelectorAll('input[name="permissions[]"]:checked');

        if (checkedBoxes.length === 0) {
            e.preventDefault();
            showToast('Veuillez sélectionner au moins une permission', 'danger');
            return false;
        }

        // Afficher un indicateur de chargement
        const submitBtn = document.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Enregistrement...';
        submitBtn.disabled = true;

        // Réactiver le bouton après 10 secondes (au cas où il y aurait une erreur)
        setTimeout(() => {
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }, 10000);
    });
</script>
{% endblock %}