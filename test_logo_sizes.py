#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أحجام الشعار في جميع القوالب
"""

import os
import re

def test_logo_sizes():
    """اختبار أحجام الشعار المحدثة"""
    
    print("🖼️ اختبار أحجام الشعار المحدثة")
    print("=" * 50)
    
    # 1. فحص صفحة تسجيل الدخول
    print("\n1️⃣ فحص صفحة تسجيل الدخول:")
    
    if os.path.exists('templates/login.html'):
        with open('templates/login.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن CSS الشعار
        logo_css_pattern = r'\.logo-container \.logo\s*{[^}]*width:\s*(\d+)px[^}]*}'
        match = re.search(logo_css_pattern, content)
        
        if match:
            width = match.group(1)
            print(f"   ✅ عرض الشعار: {width}px")
            
            if int(width) >= 250:
                print("   ✅ الحجم كبير ومناسب للعرض")
            else:
                print("   ⚠️ الحجم قد يكون صغير")
        else:
            print("   ❌ لم يتم العثور على CSS الشعار")
        
        # فحص استخدام company_info
        if 'company_info.logo' in content:
            print("   ✅ يستخدم شعار الشركة من قاعدة البيانات")
        else:
            print("   ❌ لا يستخدم شعار الشركة")
            
        # فحص fallback
        if 'onerror=' in content:
            print("   ✅ يحتوي على fallback للشعار الافتراضي")
        else:
            print("   ❌ لا يحتوي على fallback")
    else:
        print("   ❌ ملف login.html غير موجود")
    
    # 2. فحص الشريط الجانبي
    print("\n2️⃣ فحص الشريط الجانبي:")
    
    if os.path.exists('templates/base.html'):
        with open('templates/base.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن CSS الشعار في الشريط الجانبي
        sidebar_css_pattern = r'\.sidebar-header img\s*{[^}]*width:\s*(\d+)px[^}]*}'
        match = re.search(sidebar_css_pattern, content)
        
        if match:
            width = match.group(1)
            print(f"   ✅ عرض الشعار في الشريط الجانبي: {width}px")
            
            if int(width) >= 100:
                print("   ✅ الحجم مناسب للشريط الجانبي")
            else:
                print("   ⚠️ الحجم قد يكون صغير")
        else:
            print("   ❌ لم يتم العثور على CSS الشعار في الشريط الجانبي")
        
        # فحص object-fit
        if 'object-fit: contain' in content:
            print("   ✅ يستخدم object-fit: contain للحفاظ على نسب الشعار")
        else:
            print("   ⚠️ قد لا يحافظ على نسب الشعار")
    else:
        print("   ❌ ملف base.html غير موجود")
    
    # 3. فحص قوالب التقارير
    print("\n3️⃣ فحص قوالب التقارير:")
    
    if os.path.exists('templates/rapport_base.html'):
        with open('templates/rapport_base.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن CSS الشعار في التقارير
        report_css_pattern = r'\.company-logo\s*{[^}]*width:\s*(\d+)px[^}]*}'
        match = re.search(report_css_pattern, content)
        
        if match:
            width = match.group(1)
            print(f"   ✅ عرض الشعار في التقارير: {width}px")
            
            if int(width) >= 140:
                print("   ✅ الحجم مناسب للتقارير")
            else:
                print("   ⚠️ الحجم قد يكون صغير")
        else:
            print("   ❌ لم يتم العثور على CSS الشعار في التقارير")
        
        # فحص max-height
        if 'max-height:' in content:
            print("   ✅ يستخدم max-height للتحكم في الارتفاع")
        else:
            print("   ⚠️ قد لا يتحكم في الارتفاع")
    else:
        print("   ❌ ملف rapport_base.html غير موجود")
    
    # 4. فحص CSS الطباعة
    print("\n4️⃣ فحص CSS الطباعة:")
    
    if os.path.exists('static/css/print.css'):
        with open('static/css/print.css', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # البحث عن CSS الشعار في الطباعة
        print_css_pattern = r'\.company-logo\s*{[^}]*max-width:\s*(\d+)px[^}]*}'
        match = re.search(print_css_pattern, content)
        
        if match:
            width = match.group(1)
            print(f"   ✅ عرض الشعار في الطباعة: {width}px")
            
            if int(width) >= 100:
                print("   ✅ الحجم مناسب للطباعة")
            else:
                print("   ⚠️ الحجم قد يكون صغير")
        else:
            print("   ❌ لم يتم العثور على CSS الشعار في الطباعة")
        
        # فحص max-height في الطباعة
        if 'max-height:' in content:
            print("   ✅ يستخدم max-height في الطباعة")
        else:
            print("   ⚠️ قد لا يتحكم في الارتفاع في الطباعة")
    else:
        print("   ❌ ملف print.css غير موجود")
    
    # 5. فحص الشعار الافتراضي
    print("\n5️⃣ فحص الشعار الافتراضي:")
    
    default_logo = 'static/images/logo.png'
    if os.path.exists(default_logo):
        size = os.path.getsize(default_logo)
        print(f"   ✅ الشعار الافتراضي موجود ({size} bytes)")
    else:
        print("   ❌ الشعار الافتراضي غير موجود")
    
    # 6. فحص مجلد uploads
    print("\n6️⃣ فحص مجلد uploads:")
    
    uploads_dir = 'static/uploads'
    if os.path.exists(uploads_dir):
        files = os.listdir(uploads_dir)
        logo_files = [f for f in files if f.startswith('logo_') or f.endswith(('.png', '.jpg', '.jpeg', '.gif'))]
        
        if logo_files:
            print(f"   ✅ مجلد uploads يحتوي على {len(logo_files)} ملف شعار")
            for logo_file in logo_files[:2]:  # عرض أول ملفين فقط
                file_path = os.path.join(uploads_dir, logo_file)
                size = os.path.getsize(file_path)
                print(f"      📁 {logo_file} ({size} bytes)")
        else:
            print("   ⚠️ مجلد uploads موجود لكن لا توجد ملفات شعار")
    else:
        print("   ❌ مجلد uploads غير موجود")
    
    # 7. ملخص التحديثات
    print("\n7️⃣ ملخص التحديثات:")
    
    updates = [
        "✅ صفحة تسجيل الدخول: الشعار أصبح 280px عرض",
        "✅ الشريط الجانبي: الشعار أصبح 120px عرض",
        "✅ قوالب التقارير: الشعار أصبح 150px عرض",
        "✅ CSS الطباعة: الشعار أصبح 120px عرض",
        "✅ جميع الشعارات تستخدم object-fit: contain",
        "✅ جميع الشعارات لها max-height محدد",
        "✅ نظام fallback للشعار الافتراضي",
        "✅ دعم كامل لشعارات الشركة من قاعدة البيانات"
    ]
    
    for update in updates:
        print(f"   {update}")
    
    # 8. مقارنة الأحجام
    print("\n8️⃣ مقارنة الأحجام:")
    
    sizes = {
        "صفحة تسجيل الدخول": "280px × auto (max-height: 120px)",
        "الشريط الجانبي": "120px × auto (max-height: 80px)",
        "قوالب التقارير": "150px × auto (max-height: 120px)",
        "CSS الطباعة": "120px × auto (max-height: 100px)"
    }
    
    for location, size in sizes.items():
        print(f"   📏 {location}: {size}")
    
    print("\n🎉 تم الانتهاء من اختبار أحجام الشعار!")
    print("=" * 50)
    print("\n💡 ملاحظات:")
    print("   • جميع الشعارات أصبحت أكبر وأوضح")
    print("   • تم الحفاظ على نسب الشعار باستخدام object-fit: contain")
    print("   • تم تحديد max-height لمنع الشعارات من أن تصبح كبيرة جداً")
    print("   • نظام fallback يضمن ظهور شعار افتراضي في حالة عدم وجود شعار الشركة")

if __name__ == "__main__":
    test_logo_sizes()
