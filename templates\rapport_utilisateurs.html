{% extends "rapport_base.html" %}

{% block title %}Rapport des Utilisateurs - Système de Gestion de Maintenance{% endblock %}

{% block rapport_title %}Rapport des Utilisateurs{% endblock %}

{% block rapport_subtitle %}Gestion des comptes et permissions{% endblock %}

{% block rapport_meta %}
<div class="rapport-meta">
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-users me-2"></i>Total des Utilisateurs:</span>
        <span class="meta-value">{{ utilisateurs|length }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-user-shield me-2"></i>Administrateurs:</span>
        <span class="meta-value">{{ utilisateurs|selectattr('role', 'equalto', 'admin')|list|length }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-user me-2"></i>Utilisateurs Standard:</span>
        <span class="meta-value">{{ utilisateurs|selectattr('role', 'equalto', 'user')|list|length }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-calendar me-2"></i>Date de Génération:</span>
        <span class="meta-value">{{ moment().format('DD/MM/YYYY HH:mm') if moment else date_now }}</span>
    </div>
</div>
{% endblock %}

{% block rapport_content %}
<!-- Répartition par rôle -->
<div class="section-title">
    <i class="fas fa-chart-pie me-2"></i>Répartition par Rôle
</div>

<div class="row mb-4">
    <div class="col-md-4">
        <div class="card border-0 bg-light">
            <div class="card-body text-center">
                <h3 class="text-primary">{{ utilisateurs|length }}</h3>
                <p class="mb-0 text-muted">Total Utilisateurs</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-0 bg-light">
            <div class="card-body text-center">
                <h3 class="text-danger">{{ utilisateurs|selectattr('role', 'equalto', 'admin')|list|length }}</h3>
                <p class="mb-0 text-muted">Administrateurs</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-0 bg-light">
            <div class="card-body text-center">
                <h3 class="text-info">{{ utilisateurs|selectattr('role', 'equalto', 'user')|list|length }}</h3>
                <p class="mb-0 text-muted">Utilisateurs Standard</p>
            </div>
        </div>
    </div>
</div>

<!-- Liste des administrateurs -->
<div class="section-title">
    <i class="fas fa-user-shield me-2"></i>Administrateurs du Système
</div>

<div class="table-responsive mb-4">
    <table class="table table-custom">
        <thead>
            <tr>
                <th style="width: 5%;">#</th>
                <th style="width: 25%;">Nom Complet</th>
                <th style="width: 20%;">Nom d'Utilisateur</th>
                <th style="width: 30%;">Email</th>
                <th style="width: 20%;">Date de Création</th>
            </tr>
        </thead>
        <tbody>
            {% set admin_count = 0 %}
            {% for utilisateur in utilisateurs %}
                {% if utilisateur.role == 'admin' %}
                    {% set admin_count = admin_count + 1 %}
                    <tr>
                        <td class="text-center">{{ admin_count }}</td>
                        <td class="fw-bold">{{ utilisateur.prenom }} {{ utilisateur.nom }}</td>
                        <td>@{{ utilisateur.nom_utilisateur }}</td>
                        <td>{{ utilisateur.email }}</td>
                        <td>{{ utilisateur.date_creation.split(' ')[0] if utilisateur.date_creation else 'N/A' }}</td>
                    </tr>
                {% endif %}
            {% endfor %}
            {% if admin_count == 0 %}
                <tr>
                    <td colspan="5" class="text-center text-muted">Aucun administrateur trouvé</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- Liste des utilisateurs standard -->
<div class="section-title">
    <i class="fas fa-user me-2"></i>Utilisateurs Standard
</div>

<div class="table-responsive mb-4">
    <table class="table table-custom">
        <thead>
            <tr>
                <th style="width: 5%;">#</th>
                <th style="width: 25%;">Nom Complet</th>
                <th style="width: 20%;">Nom d'Utilisateur</th>
                <th style="width: 30%;">Email</th>
                <th style="width: 20%;">Date de Création</th>
            </tr>
        </thead>
        <tbody>
            {% set user_count = 0 %}
            {% for utilisateur in utilisateurs %}
                {% if utilisateur.role == 'user' %}
                    {% set user_count = user_count + 1 %}
                    <tr>
                        <td class="text-center">{{ user_count }}</td>
                        <td class="fw-bold">{{ utilisateur.prenom }} {{ utilisateur.nom }}</td>
                        <td>@{{ utilisateur.nom_utilisateur }}</td>
                        <td>{{ utilisateur.email }}</td>
                        <td>{{ utilisateur.date_creation.split(' ')[0] if utilisateur.date_creation else 'N/A' }}</td>
                    </tr>
                {% endif %}
            {% endfor %}
            {% if user_count == 0 %}
                <tr>
                    <td colspan="5" class="text-center text-muted">Aucun utilisateur standard trouvé</td>
                </tr>
            {% endif %}
        </tbody>
    </table>
</div>

<!-- Permissions et rôles -->
<div class="section-title">
    <i class="fas fa-key me-2"></i>Permissions et Rôles
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <div class="card border-0 bg-light">
            <div class="card-body">
                <h6 class="card-title text-danger">
                    <i class="fas fa-user-shield me-2"></i>Permissions Administrateur
                </h6>
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-check text-success me-2"></i>Gestion complète des utilisateurs</li>
                    <li><i class="fas fa-check text-success me-2"></i>Configuration du système</li>
                    <li><i class="fas fa-check text-success me-2"></i>Accès à tous les modules</li>
                    <li><i class="fas fa-check text-success me-2"></i>Génération de rapports</li>
                    <li><i class="fas fa-check text-success me-2"></i>Sauvegarde et restauration</li>
                    <li><i class="fas fa-check text-success me-2"></i>Gestion des données société</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card border-0 bg-light">
            <div class="card-body">
                <h6 class="card-title text-info">
                    <i class="fas fa-user me-2"></i>Permissions Utilisateur Standard
                </h6>
                <ul class="list-unstyled mb-0">
                    <li><i class="fas fa-check text-success me-2"></i>Consultation des données</li>
                    <li><i class="fas fa-check text-success me-2"></i>Ajout d'interventions</li>
                    <li><i class="fas fa-check text-success me-2"></i>Ajout de réclamations</li>
                    <li><i class="fas fa-check text-success me-2"></i>Modification de son profil</li>
                    <li><i class="fas fa-times text-danger me-2"></i>Gestion des utilisateurs</li>
                    <li><i class="fas fa-times text-danger me-2"></i>Configuration système</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Statistiques d'activité -->
<div class="section-title">
    <i class="fas fa-chart-line me-2"></i>Statistiques d'Activité
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card border-0 bg-light">
            <div class="card-body">
                <h6 class="card-title text-primary">
                    <i class="fas fa-info-circle me-2"></i>Informations Générales
                </h6>
                <div class="row">
                    <div class="col-md-6">
                        <ul class="list-unstyled mb-0">
                            <li><strong>Total des comptes:</strong> {{ utilisateurs|length }}</li>
                            <li><strong>Ratio admin/utilisateur:</strong> 
                                {% if utilisateurs|selectattr('role', 'equalto', 'user')|list|length > 0 %}
                                    1:{{ (utilisateurs|selectattr('role', 'equalto', 'user')|list|length / utilisateurs|selectattr('role', 'equalto', 'admin')|list|length)|round|int if utilisateurs|selectattr('role', 'equalto', 'admin')|list|length > 0 else 'N/A' }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </li>
                            <li><strong>Comptes avec email:</strong> {{ utilisateurs|selectattr('email')|list|length }} ({{ "%.1f"|format((utilisateurs|selectattr('email')|list|length / utilisateurs|length * 100) if utilisateurs|length > 0 else 0) }}%)</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <ul class="list-unstyled mb-0">
                            <li><strong>Dernière création:</strong> 
                                {% set latest_user = utilisateurs|sort(attribute='date_creation', reverse=true)|first %}
                                {% if latest_user %}
                                    {{ latest_user.date_creation.split(' ')[0] if latest_user.date_creation else 'N/A' }}
                                {% else %}
                                    N/A
                                {% endif %}
                            </li>
                            <li><strong>Sécurité:</strong> Mots de passe chiffrés</li>
                            <li><strong>Authentification:</strong> Session sécurisée</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recommandations de sécurité -->
<div class="section-title">
    <i class="fas fa-shield-alt me-2"></i>Recommandations de Sécurité
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card border-0 bg-light">
            <div class="card-body">
                <h6 class="card-title text-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>Bonnes Pratiques
                </h6>
                <ul class="list-unstyled mb-0">
                    <li>• Changer les mots de passe régulièrement</li>
                    <li>• Utiliser des mots de passe complexes</li>
                    <li>• Limiter le nombre d'administrateurs</li>
                    <li>• Réviser les permissions périodiquement</li>
                    <li>• Désactiver les comptes inutilisés</li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card border-0 bg-light">
            <div class="card-body">
                <h6 class="card-title text-success">
                    <i class="fas fa-check-circle me-2"></i>Mesures en Place
                </h6>
                <ul class="list-unstyled mb-0">
                    <li>• Chiffrement des mots de passe</li>
                    <li>• Gestion des sessions sécurisées</li>
                    <li>• Contrôle d'accès par rôles</li>
                    <li>• Interface d'administration protégée</li>
                    <li>• Validation des données d'entrée</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Notes importantes -->
<div class="mt-4 p-3 bg-light rounded">
    <h6 class="text-muted mb-2">
        <i class="fas fa-info-circle me-2"></i>Notes Importantes
    </h6>
    <ul class="mb-0 text-muted">
        <li>Ce rapport contient des informations sensibles et doit être traité de manière confidentielle</li>
        <li>Les mots de passe ne sont jamais affichés en clair dans le système</li>
        <li>Seuls les administrateurs peuvent accéder à la gestion des utilisateurs</li>
        <li>Toute modification des permissions nécessite une authentification administrateur</li>
    </ul>
</div>
{% endblock %}
