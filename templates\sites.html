{% extends "base.html" %}

{% block title %}Gestion des Sites - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Gestion des Sites{% endblock %}

{% block content %}
<div class="container-fluid mt-4 mb-5">
  <!-- Header moderne -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card shadow-lg border-0">
        <div class="card-header bg-gradient-primary text-white py-4">
          <div class="d-flex justify-content-between align-items-center w-100">
            <div class="header-left">
              <div class="d-flex align-items-center">
                <div class="icon-circle bg-white bg-opacity-20 me-3">
                  <i class="fas fa-building text-white"></i>
                </div>
                <div>
                  <h3 class="mb-0 fw-bold">Gestion des Sites</h3>
                  <small class="opacity-75">Gérez les sites de maintenance par région</small>
                </div>
              </div>
            </div>
            <div class="header-right">
              <div class="btn-group" role="group">
                <a href="{{ url_for('ajouter_site') }}" class="btn btn-primary btn-sm">
                  <i class="fas fa-plus"></i> Ajouter
                </a>
                <button type="button" class="btn btn-success btn-sm" id="exportExcel">
                  <i class="fas fa-file-excel"></i> Exporter Excel
                </button>
                <button type="button" class="btn btn-info btn-sm" onclick="importFromExcel()">
                  <i class="fas fa-file-import"></i> Importer Excel
                </button>
                <button type="button" class="btn btn-info btn-sm" onclick="location.reload()">
                  <i class="fas fa-sync-alt"></i> Actualiser
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistiques des sites -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card border-0 bg-gradient-primary text-white">
        <div class="card-body text-center">
          <div class="icon-circle bg-white bg-opacity-20 mx-auto mb-3">
            <i class="fas fa-building text-white"></i>
          </div>
          <h4 class="fw-bold">{{ sites|length }}</h4>
          <p class="mb-0">Total Sites</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card border-0 bg-gradient-info text-white">
        <div class="card-body text-center">
          <div class="icon-circle bg-white bg-opacity-20 mx-auto mb-3">
            <i class="fas fa-map-marker-alt text-white"></i>
          </div>
          <h4 class="fw-bold">{{ regions|length }}</h4>
          <p class="mb-0">Régions</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card border-0 bg-gradient-success text-white">
        <div class="card-body text-center">
          <div class="icon-circle bg-white bg-opacity-20 mx-auto mb-3">
            <i class="fas fa-map-marked-alt text-white"></i>
          </div>
          <h4 class="fw-bold">{{ sites|selectattr('gps')|list|length }}</h4>
          <p class="mb-0">Avec GPS</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card border-0 bg-gradient-warning text-dark">
        <div class="card-body text-center">
          <div class="icon-circle bg-white bg-opacity-20 mx-auto mb-3">
            <i class="fas fa-user-tie text-dark"></i>
          </div>
          <h4 class="fw-bold">{{ sites|selectattr('responsable')|list|length }}</h4>
          <p class="mb-0">Avec Responsable</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Filtres de recherche -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card border-0 bg-light">
        <div class="card-header bg-gradient-info text-white">
          <h5 class="mb-0"><i class="fas fa-filter me-2"></i>Filtres de recherche</h5>
        </div>
        <div class="card-body">
          <form method="GET" action="{{ url_for('sites') }}">
            <div class="row g-3">
              <div class="col-md-4">
                <div class="form-floating">
                  <select class="form-select" id="region_id" name="region_id">
                    <option value="">Toutes les régions</option>
                    {% for region in regions %}
                    <option value="{{ region.id }}" {% if region_id|string == region.id|string %}selected{% endif %}>{{ region.nom }}</option>
                    {% endfor %}
                  </select>
                  <label for="region_id"><i class="fas fa-map-marker-alt me-2"></i>Région</label>
                </div>
              </div>
              <div class="col-md-4">
                <div class="form-floating">
                  <input type="text" class="form-control" id="searchSite" placeholder="Rechercher un site">
                  <label for="searchSite"><i class="fas fa-search me-2"></i>Nom du site</label>
                </div>
              </div>
              <div class="col-md-4">
                <div class="d-flex gap-2 h-100 align-items-end">
                  <button type="submit" class="btn btn-primary btn-modern flex-fill">
                    <i class="fas fa-filter me-1"></i> Filtrer
                  </button>
                  <button type="button" class="btn btn-secondary btn-modern flex-fill" onclick="clearFilters()">
                    <i class="fas fa-times me-1"></i> Effacer
                  </button>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>

  <!-- Tableau des sites -->
  <div class="row">
    <div class="col-12">
      <div class="card shadow-lg border-0">
        <div class="card-header bg-gradient-secondary text-white">
          <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center">
              <div class="icon-circle bg-white bg-opacity-20 me-3">
                <i class="fas fa-table text-white"></i>
              </div>
              <div>
                <h6 class="mb-0 fw-bold">Liste des Sites</h6>
                <small class="opacity-75">{{ sites|length }} sites enregistrés</small>
              </div>
            </div>
          </div>
        </div>
        <div class="card-body p-0">
          <div class="table-responsive">
            <table class="table table-hover mb-0" id="sitesTable">
              <thead class="table-dark">
                <tr>
                  <th class="text-center">#</th>
                  <th><i class="fas fa-map-marker-alt me-2"></i>Région</th>
                  <th><i class="fas fa-building me-2"></i>Site</th>
                  <th><i class="fas fa-map-marked-alt me-2"></i>Localisation</th>
                  <th><i class="fas fa-user-tie me-2"></i>Contact</th>
                  <th class="text-center"><i class="fas fa-cogs me-2"></i>Actions</th>
                </tr>
              </thead>
              <tbody>
                {% if sites %}
                  {% for site in sites %}
                  <tr class="site-row" data-region="{{ site.region_nom }}" data-name="{{ site.nom }}">
                    <td class="text-center">
                      <div class="site-avatar">
                        <span class="avatar-text">{{ loop.index }}</span>
                      </div>
                    </td>
                    <td>
                      <div class="region-info">
                        <span class="badge bg-gradient-info px-3 py-2">
                          <i class="fas fa-map-marker-alt me-1"></i>{{ site.region_nom }}
                        </span>
                      </div>
                    </td>
                    <td>
                      <div class="site-info">
                        <div class="fw-bold text-primary">{{ site.nom }}</div>
                        <small class="text-muted">{{ site.adresse or 'Adresse non spécifiée' }}</small>
                      </div>
                    </td>
                    <td>
                      <div class="location-info">
                        {% if site.gps %}
                        <div class="text-success">
                          <i class="fas fa-map-marked-alt me-1"></i>{{ site.gps }}
                        </div>
                        {% else %}
                        <div class="text-muted">
                          <i class="fas fa-map-marked-alt me-1"></i>GPS non défini
                        </div>
                        {% endif %}
                      </div>
                    </td>
                    <td>
                      <div class="contact-info">
                        {% if site.responsable %}
                        <div class="fw-bold">{{ site.responsable }}</div>
                        {% endif %}
                        {% if site.telephone %}
                        <div><i class="fas fa-phone text-info me-1"></i>{{ site.telephone }}</div>
                        {% endif %}
                        {% if site.email %}
                        <div><i class="fas fa-envelope text-info me-1"></i>{{ site.email }}</div>
                        {% endif %}
                        {% if not site.responsable and not site.telephone and not site.email %}
                        <small class="text-muted">Contact non défini</small>
                        {% endif %}
                      </div>
                    </td>
                    <td class="text-center">
                      <div class="btn-group" role="group">
                        <a href="{{ url_for('modifier_site', id=site.id) }}"
                           class="btn btn-sm btn-warning btn-modern"
                           title="Modifier">
                          <i class="fas fa-edit"></i>
                        </a>
                        <button type="button"
                                class="btn btn-sm btn-danger btn-modern"
                                onclick="if(confirm('Êtes-vous sûr de vouloir supprimer le site: {{ site.nom_site or site.nom }}?\n\nCette action est irréversible!')) { window.location.href='{{ url_for('supprimer_site', id=site.id) }}'; }"
                                title="Supprimer">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </td>
                  </tr>
                  {% endfor %}
                {% else %}
                  <tr>
                    <td colspan="6" class="text-center py-5">
                      <div class="text-muted">
                        <i class="fas fa-building fa-3x mb-3"></i>
                        <h5>Aucun site trouvé</h5>
                        <p>Commencez par ajouter votre premier site</p>
                        <a href="{{ url_for('ajouter_site') }}" class="btn btn-primary btn-modern">
                          <i class="fas fa-plus me-1"></i> Ajouter un site
                        </a>
                      </div>
                    </td>
                  </tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{% endblock %}

{% block extra_css %}
<style>
  .card {
    border: none !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1) !important;
  }

  .table th {
    background-color: #f8f9fa !important;
    border-bottom: 2px solid #dee2e6 !important;
    font-weight: 600 !important;
    padding: 15px 12px !important;
    font-size: 0.9rem !important;
  }

  .table td {
    padding: 15px 12px !important;
    vertical-align: middle !important;
    border-bottom: 1px solid #dee2e6 !important;
  }

  .table-hover tbody tr:hover {
    background-color: rgba(23,162,184,0.05) !important;
  }

  .btn-group .btn {
    margin: 0 1px !important;
    border-radius: 6px !important;
  }

  .badge {
    font-size: 0.75rem !important;
    padding: 6px 10px !important;
    font-weight: 500 !important;
  }

  .text-white-75 {
    color: rgba(255,255,255,0.75) !important;
  }

  .text-white-50 {
    color: rgba(255,255,255,0.5) !important;
  }

  .card-header {
    border-bottom: 1px solid rgba(0,0,0,0.125) !important;
    padding: 1rem 1.25rem !important;
  }

  .card-body {
    padding: 1.25rem !important;
  }

  .bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  }

  .bg-gradient-info {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
  }

  .bg-gradient-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
  }

  .bg-gradient-warning {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
  }

  .bg-gradient-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
  }

  .text-white {
    color: #ffffff !important;
  }

  .card-header.bg-gradient-primary,
  .card-header.bg-gradient-info,
  .card-header.bg-gradient-secondary {
    color: #ffffff !important;
  }

  .card-header.bg-gradient-primary *,
  .card-header.bg-gradient-info *,
  .card-header.bg-gradient-secondary * {
    color: #ffffff !important;
  }

  /* Avatar site */
  .site-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
  }

  .avatar-text {
    color: white;
    font-weight: bold;
    font-size: 14px;
  }

  /* Responsive */
  @media (max-width: 768px) {
    .btn-group {
      flex-direction: column !important;
    }

    .btn-group .btn {
      margin: 2px 0 !important;
    }

    .table-responsive {
      font-size: 0.85rem !important;
    }

    .card-body {
      padding: 1rem !important;
    }

    .site-avatar {
      width: 30px;
      height: 30px;
    }

    .avatar-text {
      font-size: 12px;
    }
  }
</style>
{% endblock %}

{% block extra_js %}
<script>


  // Fonction pour effacer les filtres
  function clearFilters() {
    document.getElementById('region_id').value = '';
    document.getElementById('searchSite').value = '';
    document.querySelectorAll('.site-row').forEach(row => {
      row.style.display = '';
    });
  }

  // Export Excel
  document.getElementById('exportExcel').addEventListener('click', function() {
    const table = document.getElementById('sitesTable');
    const wb = XLSX.utils.table_to_book(table, {sheet: "Sites"});
    XLSX.writeFile(wb, 'sites.xlsx');
  });

  // Recherche en temps réel
  document.getElementById('searchSite').addEventListener('input', function() {
    const searchTerm = this.value.toLowerCase();
    const rows = document.querySelectorAll('.site-row');

    rows.forEach(row => {
      const siteName = row.dataset.name.toLowerCase();
      if (siteName.includes(searchTerm)) {
        row.style.display = '';
      } else {
        row.style.display = 'none';
      }
    });
  });
</script>
{% endblock %}
