#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests

# إنشاء session للاحتفاظ بـ cookies
session = requests.Session()

print("🔍 تشخيص مشكلة تسجيل الدخول...")

try:
    # 1. تسجيل الدخول
    print("1. محاولة تسجيل الدخول...")
    login_data = {
        'nom_utilisateur': 'admin',
        'mot_de_passe': 'admin123'
    }
    
    login_response = session.post('http://127.0.0.1:5000/login', data=login_data)
    print(f"   Login Status: {login_response.status_code}")
    print(f"   Login URL after: {login_response.url}")
    
    # 2. التحقق من حالة تسجيل الدخول
    print("\n2. التحقق من حالة تسجيل الدخول...")
    check_response = session.get('http://127.0.0.1:5000/api/check-login')
    print(f"   Check Status: {check_response.status_code}")
    print(f"   Check Response: {check_response.text}")
    
    # 3. محاولة الوصول لصفحة محمية
    print("\n3. محاولة الوصول لصفحة المواقع...")
    sites_response = session.get('http://127.0.0.1:5000/sites')
    print(f"   Sites Status: {sites_response.status_code}")
    print(f"   Sites URL: {sites_response.url}")
    
    # 4. محاولة استيراد البيانات
    print("\n4. محاولة استيراد البيانات...")
    import_data = {
        'data': [
            {
                'NOM DU SITE': 'Site Test',
                'ADRESSE': '123 Test Street',
                'TÉLÉPHONE': '+212 123456789',
                'RESPONSABLE': 'Test User',
                'EMAIL': '<EMAIL>'
            }
        ]
    }
    
    import_response = session.post(
        'http://127.0.0.1:5000/api/import/sites',
        json=import_data,
        headers={'Content-Type': 'application/json'}
    )
    print(f"   Import Status: {import_response.status_code}")
    print(f"   Import Response: {import_response.text}")
    
except Exception as e:
    print(f"❌ خطأ: {e}")

print("\n🎯 انتهى التشخيص!")
