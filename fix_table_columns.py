#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح أعمدة الجداول المفقودة
Fix missing table columns
"""

import sqlite3
import os
import shutil
from datetime import datetime

def backup_database():
    """عمل نسخة احتياطية من قاعدة البيانات"""
    if os.path.exists('maintenance.db'):
        backup_name = f'maintenance_columns_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        shutil.copy2('maintenance.db', backup_name)
        print(f"✅ تم حفظ نسخة احتياطية: {backup_name}")
        return backup_name
    return None

def fix_marches_table():
    """إصلاح جدول marches"""
    
    conn = sqlite3.connect('maintenance.db')
    cursor = conn.cursor()
    
    print("🔧 إصلاح جدول marches...")
    
    try:
        # فحص الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(marches)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"   📋 الأعمدة الموجودة: {columns}")
        
        # إضافة الأعمدة المفقودة
        if 'numero' not in columns:
            cursor.execute("ALTER TABLE marches ADD COLUMN numero TEXT")
            print("   ✅ تم إضافة عمود numero")
        
        if 'objet' not in columns:
            cursor.execute("ALTER TABLE marches ADD COLUMN objet TEXT")
            print("   ✅ تم إضافة عمود objet")
        
        if 'client' not in columns:
            cursor.execute("ALTER TABLE marches ADD COLUMN client TEXT")
            print("   ✅ تم إضافة عمود client")
        
        if 'date' not in columns:
            cursor.execute("ALTER TABLE marches ADD COLUMN date DATE")
            print("   ✅ تم إضافة عمود date")
        
        if 'domaine' not in columns:
            cursor.execute("ALTER TABLE marches ADD COLUMN domaine TEXT")
            print("   ✅ تم إضافة عمود domaine")
        
        if 'lieu' not in columns:
            cursor.execute("ALTER TABLE marches ADD COLUMN lieu TEXT")
            print("   ✅ تم إضافة عمود lieu")
        
        if 'periode_interventions' not in columns:
            cursor.execute("ALTER TABLE marches ADD COLUMN periode_interventions TEXT")
            print("   ✅ تم إضافة عمود periode_interventions")
        
        # نسخ البيانات من الأعمدة الجديدة إلى القديمة
        if 'numero_marche' in columns and 'numero' in columns:
            cursor.execute("UPDATE marches SET numero = numero_marche WHERE numero IS NULL")
            print("   ✅ تم نسخ البيانات من numero_marche إلى numero")
        
        if 'objet_marche' in columns and 'objet' in columns:
            cursor.execute("UPDATE marches SET objet = objet_marche WHERE objet IS NULL")
            print("   ✅ تم نسخ البيانات من objet_marche إلى objet")
        
        conn.commit()
        print("✅ تم إصلاح جدول marches")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح جدول marches: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()
    
    return True

def fix_interventions_table():
    """إصلاح جدول interventions"""
    
    conn = sqlite3.connect('maintenance.db')
    cursor = conn.cursor()
    
    print("🔧 إصلاح جدول interventions...")
    
    try:
        # فحص الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(interventions)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"   📋 الأعمدة الموجودة: {columns}")
        
        # إضافة الأعمدة المفقودة
        if 'domaine' not in columns:
            cursor.execute("ALTER TABLE interventions ADD COLUMN domaine TEXT")
            print("   ✅ تم إضافة عمود domaine")
        
        if 'client' not in columns:
            cursor.execute("ALTER TABLE interventions ADD COLUMN client TEXT")
            print("   ✅ تم إضافة عمود client")
        
        if 'date_creation' not in columns:
            cursor.execute("ALTER TABLE interventions ADD COLUMN date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            print("   ✅ تم إضافة عمود date_creation")
        
        if 'lieu' not in columns:
            cursor.execute("ALTER TABLE interventions ADD COLUMN lieu TEXT")
            print("   ✅ تم إضافة عمود lieu")
        
        # نسخ البيانات من الأعمدة الجديدة إلى القديمة
        if 'type_intervention' in columns and 'domaine' in columns:
            cursor.execute("UPDATE interventions SET domaine = type_intervention WHERE domaine IS NULL")
            print("   ✅ تم نسخ البيانات من type_intervention إلى domaine")
        
        conn.commit()
        print("✅ تم إصلاح جدول interventions")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح جدول interventions: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()
    
    return True

def fix_reclamations_table():
    """إصلاح جدول reclamations"""
    
    conn = sqlite3.connect('maintenance.db')
    cursor = conn.cursor()
    
    print("🔧 إصلاح جدول reclamations...")
    
    try:
        # فحص الأعمدة الموجودة
        cursor.execute("PRAGMA table_info(reclamations)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"   📋 الأعمدة الموجودة: {columns}")
        
        # إضافة الأعمدة المفقودة
        if 'domaine' not in columns:
            cursor.execute("ALTER TABLE reclamations ADD COLUMN domaine TEXT")
            print("   ✅ تم إضافة عمود domaine")
        
        if 'client' not in columns:
            cursor.execute("ALTER TABLE reclamations ADD COLUMN client TEXT")
            print("   ✅ تم إضافة عمود client")
        
        if 'date_creation' not in columns:
            cursor.execute("ALTER TABLE reclamations ADD COLUMN date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP")
            print("   ✅ تم إضافة عمود date_creation")
        
        if 'lieu' not in columns:
            cursor.execute("ALTER TABLE reclamations ADD COLUMN lieu TEXT")
            print("   ✅ تم إضافة عمود lieu")
        
        # نسخ البيانات من الأعمدة الجديدة إلى القديمة
        if 'type_reclamation' in columns and 'domaine' in columns:
            cursor.execute("UPDATE reclamations SET domaine = type_reclamation WHERE domaine IS NULL")
            print("   ✅ تم نسخ البيانات من type_reclamation إلى domaine")
        
        conn.commit()
        print("✅ تم إصلاح جدول reclamations")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح جدول reclamations: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()
    
    return True

def test_fixed_tables():
    """اختبار الجداول المصلحة"""
    
    print("🧪 اختبار الجداول المصلحة...")
    
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # اختبار جدول marches
        print("\n📋 اختبار جدول marches:")
        try:
            cursor.execute("SELECT numero, objet, client FROM marches LIMIT 1")
            marche = cursor.fetchone()
            if marche:
                print(f"   ✅ البيانات: {dict(marche)}")
            else:
                print("   ✅ الجدول فارغ ولكن الأعمدة موجودة")
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
        
        # اختبار جدول interventions
        print("\n🔧 اختبار جدول interventions:")
        try:
            cursor.execute("SELECT id, domaine, client FROM interventions LIMIT 1")
            intervention = cursor.fetchone()
            if intervention:
                print(f"   ✅ البيانات: {dict(intervention)}")
            else:
                print("   ✅ الجدول فارغ ولكن الأعمدة موجودة")
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
        
        # اختبار جدول reclamations
        print("\n📞 اختبار جدول reclamations:")
        try:
            cursor.execute("SELECT id, domaine, client FROM reclamations LIMIT 1")
            reclamation = cursor.fetchone()
            if reclamation:
                print(f"   ✅ البيانات: {dict(reclamation)}")
            else:
                print("   ✅ الجدول فارغ ولكن الأعمدة موجودة")
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الجداول: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🔧 إصلاح أعمدة الجداول المفقودة")
    print("🔧 Fix Missing Table Columns")
    print("=" * 60)
    
    # عمل نسخة احتياطية
    backup_file = backup_database()
    
    # إصلاح جدول marches
    if fix_marches_table():
        print("✅ تم إصلاح جدول marches")
    else:
        print("❌ فشل في إصلاح جدول marches")
    
    # إصلاح جدول interventions
    if fix_interventions_table():
        print("✅ تم إصلاح جدول interventions")
    else:
        print("❌ فشل في إصلاح جدول interventions")
    
    # إصلاح جدول reclamations
    if fix_reclamations_table():
        print("✅ تم إصلاح جدول reclamations")
    else:
        print("❌ فشل في إصلاح جدول reclamations")
    
    # اختبار الجداول المصلحة
    if test_fixed_tables():
        print("✅ جميع الجداول تعمل بشكل صحيح")
    else:
        print("❌ مشاكل في الجداول")
    
    print("\n🎉 تم الانتهاء من إصلاح الأعمدة!")
    print("🎉 Column fix completed!")
    
    if backup_file:
        print(f"\n💾 النسخة الاحتياطية: {backup_file}")
        print(f"💾 Backup file: {backup_file}")
    
    print("\n📋 الآن يمكنك:")
    print("📋 Now you can:")
    print("   1. تشغيل البرنامج")
    print("   1. Run the application")
    print("   2. اختبار أزرار الحذف")
    print("   2. Test delete buttons")
    print("   3. يجب أن تعمل بدون أخطاء")
    print("   3. Should work without errors")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
