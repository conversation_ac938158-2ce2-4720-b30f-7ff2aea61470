{% extends "base.html" %}

{% block title %}Modifier un Site - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Modifier un Site{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-building"></i> Modifier le site
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('modifier_site', id=site.id) }}">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="region_id" class="form-label">Région</label>
                            <select class="form-select" id="region_id" name="region_id" required>
                                <option value="">Sélectionner une région</option>
                                {% for region in regions %}
                                <option value="{{ region.id }}" {% if site.region_id == region.id %}selected{% endif %}>{{ region.nom }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="nom" class="form-label">Nom du site</label>
                            <input type="text" class="form-control" id="nom" name="nom" value="{{ site.nom }}" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="adresse" class="form-label">Adresse</label>
                            <textarea class="form-control" id="adresse" name="adresse" rows="2">{{ site.adresse }}</textarea>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="telephone" class="form-label">Téléphone</label>
                            <input type="text" class="form-control" id="telephone" name="telephone" value="{{ site.telephone }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="{{ site.email }}">
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="responsable" class="form-label">Responsable</label>
                            <input type="text" class="form-control" id="responsable" name="responsable" value="{{ site.responsable }}">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="gps" class="form-label">Coordonnées GPS</label>
                            <input type="text" class="form-control" id="gps" name="gps" placeholder="Ex: 31.6295, -7.9811" value="{{ site.gps }}">
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer les modifications
                        </button>
                        <a href="{{ url_for('sites') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
