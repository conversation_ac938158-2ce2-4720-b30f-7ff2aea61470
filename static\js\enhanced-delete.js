/**
 * نظام الحذف المحسن - رسائل تأكيد جميلة ومحسنة
 * Enhanced Delete System - Beautiful confirmation dialogs
 */

console.log('🗑️ تحميل نظام الحذف المحسن...');

class EnhancedDeleteSystem {
    constructor() {
        this.init();
    }

    init() {
        this.setupDeleteHandlers();
        this.createDeleteModal();
        console.log('✅ Enhanced delete system initialized');
    }

    setupDeleteHandlers() {
        // Remove existing delete handlers
        this.disableExistingDeleteHandlers();

        // Bind all delete buttons to new system
        document.addEventListener('click', (e) => {
            // Look for delete buttons - check both target and closest parent
            const deleteButton = e.target.closest('.btn-danger') ||
                                e.target.closest('[href*="supprimer"]') ||
                                e.target.closest('[onclick*="supprimer"]') ||
                                e.target.closest('.delete-btn');

            if (deleteButton) {
                // Check if it's actually a delete button
                const isDeleteButton = deleteButton.classList.contains('btn-danger') ||
                                     (deleteButton.href && deleteButton.href.includes('supprimer')) ||
                                     (deleteButton.onclick && deleteButton.onclick.toString().includes('supprimer')) ||
                                     deleteButton.classList.contains('delete-btn') ||
                                     deleteButton.dataset.deleteUrl;

                if (isDeleteButton) {
                    console.log('🗑️ Delete button clicked:', deleteButton);
                    e.preventDefault();
                    e.stopPropagation();
                    this.handleDeleteClick(deleteButton);
                }
            }
        });
    }

    disableExistingDeleteHandlers() {
        // Remove all existing onclick events
        const deleteButtons = document.querySelectorAll('[onclick*="supprimer"], [href*="supprimer"]');
        deleteButtons.forEach(button => {
            // Save link before removing it
            if (button.href && button.href.includes('supprimer')) {
                button.dataset.deleteUrl = button.href;
                button.href = '#';
            }

            // Save onclick before removing it
            if (button.onclick) {
                const onclickStr = button.onclick.toString();
                const urlMatch = onclickStr.match(/['"`]([^'"`]*supprimer[^'"`]*)['"`]/);
                if (urlMatch) {
                    button.dataset.deleteUrl = urlMatch[1];
                }
                button.removeAttribute('onclick');
            }
        });

        console.log(`🗑️ Disabled ${deleteButtons.length} old delete buttons`);
    }

    handleDeleteClick(button) {
        // Extract information about the item to be deleted
        const deleteInfo = this.extractDeleteInfo(button);

        // Show enhanced confirmation dialog
        this.showDeleteConfirmation(deleteInfo);
    }

    extractDeleteInfo(button) {
        // Extract delete information from button or surrounding element
        let itemType = 'cet élément';
        let itemName = '';
        let deleteUrl = '';
        let itemId = '';

        // Extract delete URL
        if (button.dataset.deleteUrl) {
            deleteUrl = button.dataset.deleteUrl;
        } else if (button.href && button.href !== '#') {
            deleteUrl = button.href;
        } else if (button.onclick) {
            const onclickStr = button.onclick.toString();
            const urlMatch = onclickStr.match(/['"`]([^'"`]*supprimer[^'"`]*)['"`]/);
            if (urlMatch) {
                deleteUrl = urlMatch[1];
            }
        }

        // Extract item type from URL or context
        if (deleteUrl.includes('/regions/')) {
            itemType = 'la région';
        } else if (deleteUrl.includes('/sites/')) {
            itemType = 'le site';
        } else if (deleteUrl.includes('/marches/')) {
            itemType = 'le marché';
        } else if (deleteUrl.includes('/interventions/')) {
            itemType = 'l\'intervention';
        } else if (deleteUrl.includes('/reclamations/')) {
            itemType = 'la réclamation';
        } else if (deleteUrl.includes('/utilisateurs/')) {
            itemType = 'l\'utilisateur';
        } else {
            itemType = 'cet élément';
        }

        // Extract item name from surrounding table
        const row = button.closest('tr');
        if (row) {
            const cells = row.querySelectorAll('td');
            if (cells.length > 1) {
                // Search for item name in different columns
                for (let i = 1; i < Math.min(cells.length - 1, 4); i++) {
                    const cellText = cells[i].textContent.trim();
                    if (cellText && !cellText.match(/^\d+$/) && cellText !== '-') {
                        itemName = cellText;
                        break;
                    }
                }
                // If nothing found, take first column
                if (!itemName && cells[0]) {
                    itemName = cells[0].textContent.trim();
                }
            }
        }

        // Extract ID from URL
        const idMatch = deleteUrl.match(/\/(\d+)$/);
        if (idMatch) {
            itemId = idMatch[1];
        }

        // If no name found, try searching in attributes
        if (!itemName) {
            itemName = button.getAttribute('data-name') ||
                      button.getAttribute('title') ||
                      button.getAttribute('aria-label');
        }

        return {
            type: itemType,
            name: itemName || `${itemType} #${itemId}` || itemType,
            url: deleteUrl,
            id: itemId
        };
    }

    createDeleteModal() {
        // Create enhanced confirmation dialog
        const modalHTML = `
            <div id="deleteModal" class="modal fade" tabindex="-1" role="dialog" style="display: none;">
                <div class="modal-dialog modal-dialog-centered" role="document">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Confirmer la suppression
                            </h5>
                        </div>
                        <div class="modal-body text-center">
                            <div class="warning-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <h6 class="mb-3" id="deleteMessage">Êtes-vous sûr de vouloir supprimer cet élément ?</h6>
                            <p class="text-danger mb-0" id="deleteWarning">
                                <small>Cette action est irréversible.</small>
                            </p>
                        </div>
                        <div class="modal-footer justify-content-center">
                            <button type="button" class="btn btn-secondary" id="cancelDelete">
                                <i class="fas fa-times me-1"></i>
                                Annuler
                            </button>
                            <button type="button" class="btn btn-danger" id="confirmDelete">
                                <i class="fas fa-trash me-1"></i>
                                Supprimer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add dialog to page if not exists
        if (!document.getElementById('deleteModal')) {
            document.body.insertAdjacentHTML('beforeend', modalHTML);
            this.setupModalEvents();
        }
    }

    setupModalEvents() {
        const modal = document.getElementById('deleteModal');
        const cancelBtn = document.getElementById('cancelDelete');
        const confirmBtn = document.getElementById('confirmDelete');

        // Close dialog when clicking cancel
        cancelBtn.addEventListener('click', () => {
            this.hideModal();
        });

        // Close dialog when clicking outside
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                this.hideModal();
            }
        });

        // Close dialog with Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && modal.style.display !== 'none') {
                this.hideModal();
            }
        });
    }

    showDeleteConfirmation(deleteInfo) {
        const modal = document.getElementById('deleteModal');
        const messageEl = document.getElementById('deleteMessage');
        const confirmBtn = document.getElementById('confirmDelete');

        // Update confirmation message
        messageEl.innerHTML = `
            Êtes-vous sûr de vouloir supprimer <strong>${deleteInfo.type}</strong><br>
            <span class="text-primary">"${deleteInfo.name}"</span> ?
        `;

        // Bind confirm button to delete operation
        confirmBtn.onclick = () => {
            this.executeDelete(deleteInfo);
        };

        // Show dialog
        this.showModal();
    }

    showModal() {
        const modal = document.getElementById('deleteModal');
        modal.style.display = 'block';
        modal.classList.add('show');
        document.body.classList.add('modal-open');

        // Add backdrop
        if (!document.querySelector('.modal-backdrop')) {
            const backdrop = document.createElement('div');
            backdrop.className = 'modal-backdrop fade show';
            document.body.appendChild(backdrop);
        }
    }

    hideModal() {
        const modal = document.getElementById('deleteModal');
        modal.style.display = 'none';
        modal.classList.remove('show');
        document.body.classList.remove('modal-open');

        // Remove backdrop
        const backdrop = document.querySelector('.modal-backdrop');
        if (backdrop) {
            backdrop.remove();
        }
    }

    executeDelete(deleteInfo) {
        // Show loading indicator
        this.showLoadingState();

        // Execute delete operation
        if (deleteInfo.url) {
            // Add short delay to show loading state
            setTimeout(() => {
                window.location.href = deleteInfo.url;
            }, 500);
        } else {
            console.error('❌ Delete URL not found');
            this.hideModal();
            this.showErrorMessage('Erreur: Lien de suppression introuvable');
        }
    }

    showLoadingState() {
        const confirmBtn = document.getElementById('confirmDelete');
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i> Suppression...';
        confirmBtn.disabled = true;
    }

    showErrorMessage(message) {
        // Show error message
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 10000; min-width: 300px;';
        alert.innerHTML = `
            <i class="fas fa-exclamation-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
        `;
        document.body.appendChild(alert);

        setTimeout(() => alert.remove(), 5000);
    }
}

// Initialize system when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.deleteSystem = new EnhancedDeleteSystem();
});

console.log('✨ Enhanced delete system loaded successfully');
