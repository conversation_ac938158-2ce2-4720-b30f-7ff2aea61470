{% extends "base.html" %}

{% block title %}Carte du Maroc{% endblock %}

{% block page_title %}Carte du Maroc{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h4 class="mb-0"><i class="fas fa-map-marked-alt"></i> Carte du Maroc</h4>
                            <small>Visualisation géographique des données de maintenance</small>
                        </div>
                        <div class="col-md-6 text-end">
                            <button type="button" class="btn btn-light btn-sm me-2" onclick="window.print()">
                                <i class="fas fa-print"></i> Imprimer
                            </button>
                            <button type="button" class="btn btn-success btn-sm me-2" onclick="exportData()">
                                <i class="fas fa-file-excel"></i> Excel
                            </button>
                            <button type="button" class="btn btn-info btn-sm" onclick="location.reload()">
                                <i class="fas fa-sync-alt"></i> Actualiser
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body py-2">
                    <div class="text-center">
                        <span class="me-3"><i class="fas fa-filter"></i> Filtres:</span>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm active" id="btn-all" onclick="showAll()">
                                <i class="fas fa-globe"></i> Tout
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="btn-sites" onclick="showSites()">
                                <i class="fas fa-building"></i> Sites
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" id="btn-marches" onclick="showMarches()">
                                <i class="fas fa-handshake"></i> Marchés
                            </button>
                            <button type="button" class="btn btn-outline-warning btn-sm" id="btn-interventions" onclick="showInterventions()">
                                <i class="fas fa-tools"></i> Interventions
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm" id="btn-reclamations" onclick="showReclamations()">
                                <i class="fas fa-exclamation-triangle"></i> Réclamations
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Map and Statistics -->
    <div class="row">
        <div class="col-lg-9">
            <div class="card">
                <div class="card-body p-0">
                    <div id="map" style="height: 600px;"></div>
                </div>
            </div>
        </div>

        <div class="col-lg-3">
            <!-- Statistics -->
            <div class="card mb-3">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Statistiques</h6>
                </div>
                <div class="card-body">
                    <div class="row g-2">
                        <div class="col-6">
                            <div class="text-center p-2 bg-primary text-white rounded">
                                <div class="h5 mb-0">{{ sites|length }}</div>
                                <small>Sites</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-2 bg-success text-white rounded">
                                <div class="h5 mb-0">{{ marches|length }}</div>
                                <small>Marchés</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-2 bg-warning text-white rounded">
                                <div class="h5 mb-0">{{ interventions|length }}</div>
                                <small>Interventions</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="text-center p-2 bg-danger text-white rounded">
                                <div class="h5 mb-0">{{ reclamations|length }}</div>
                                <small>Réclamations</small>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="text-center">
                        <div class="h4 mb-0">{{ (sites|length) + (marches|length) + (interventions|length) + (reclamations|length) }}</div>
                        <small class="text-muted">Total</small>
                    </div>
                </div>
            </div>

            <!-- Legend -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-map-signs"></i> Légende</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-primary me-2">●</span> Sites
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-success me-2">●</span> Marchés
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <span class="badge bg-warning me-2">●</span> Interventions
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-danger me-2">●</span> Réclamations
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<!-- Custom Map Styles -->
<link rel="stylesheet" href="{{ url_for('static', filename='css/map-styles.css') }}" />

<style>
/* Styles pour les marqueurs personnalisés */
.custom-marker-icon {
    background: transparent !important;
    border: none !important;
}

.marker-container:hover {
    transform: scale(1.1) !important;
}

/* Styles pour les popups */
.custom-popup .leaflet-popup-content-wrapper {
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.2);
}

.custom-popup .leaflet-popup-content {
    margin: 15px;
    font-family: 'Poppins', sans-serif;
}

.marker-popup h6 {
    font-weight: 600;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.marker-popup p {
    margin: 5px 0;
    line-height: 1.4;
}

.marker-popup strong {
    color: #2c3e50;
}

/* Animation pour les marqueurs */
@keyframes markerBounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.marker-container.bounce {
    animation: markerBounce 1s ease-in-out;
}

/* Amélioration de la carte */
#map {
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* Styles pour les boutons de filtre */
.filter-buttons .btn {
    margin: 2px;
    border-radius: 20px;
    padding: 8px 16px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.filter-buttons .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.2);
}

.filter-buttons .btn.active {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
}

/* Responsive */
@media (max-width: 768px) {
    .marker-container {
        width: 30px !important;
        height: 30px !important;
    }

    .marker-background {
        width: 24px !important;
        height: 24px !important;
    }

    .marker-container i {
        font-size: 12px !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<!-- Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
// Variables globales
let map;
let allMarkers = [];
let currentFilter = 'all';

// Données de la carte
const mapData = {
    sites: {{ sites|tojson if sites else '[]' }},
    marches: {{ marches|tojson if marches else '[]' }},
    interventions: {{ interventions|tojson if interventions else '[]' }},
    reclamations: {{ reclamations|tojson if reclamations else '[]' }}
};

// Villes du Maroc avec coordonnées
const cities = {
    'Casablanca': [33.5731, -7.5898],
    'Rabat': [34.0209, -6.8416],
    'Fès': [34.0181, -5.0078],
    'Marrakech': [31.6295, -7.9811],
    'Agadir': [30.4278, -9.5981],
    'Tanger': [35.7595, -5.8340],
    'Meknès': [33.8935, -5.5473],
    'Oujda': [34.6814, -1.9086],
    'Kenitra': [34.2610, -6.5802],
    'Tétouan': [35.5889, -5.3626],
    'Safi': [32.2994, -9.2372],
    'El Jadida': [33.2316, -8.5007],
    'Beni Mellal': [32.3373, -6.3498],
    'Errachidia': [31.9314, -4.4240],
    'Taza': [34.2133, -4.0103],
    'Essaouira': [31.5085, -9.7595],
    'Khouribga': [32.8811, -6.9063],
    'Ouarzazate': [30.9335, -6.9370],
    'Settat': [33.0018, -7.6160],
    'Larache': [35.1932, -6.1563]
};

// Fonction pour obtenir les coordonnées
function getCoordinates(item) {
    // Si GPS existe et est valide
    if (item.gps && item.gps.includes(',')) {
        const coords = item.gps.split(',');
        const lat = parseFloat(coords[0].trim());
        const lng = parseFloat(coords[1].trim());
        if (!isNaN(lat) && !isNaN(lng)) {
            return [lat, lng];
        }
    }

    // Chercher par lieu/ville
    if (item.lieu) {
        for (const [city, coords] of Object.entries(cities)) {
            if (item.lieu.toLowerCase().includes(city.toLowerCase()) ||
                city.toLowerCase().includes(item.lieu.toLowerCase())) {
                return coords;
            }
        }
    }

    // Coordonnées par défaut (centre du Maroc)
    return [31.7917, -7.0926];
}








// ==================== CONFIGURATION ====================
const MARKER_COLORS = {
    'site': '#007bff',
    'marche': '#28a745',
    'intervention': '#ffc107',
    'reclamation': '#dc3545'
};

const MARKER_ICONS = {
    'site': 'building',
    'marche': 'handshake',
    'intervention': 'tools',
    'reclamation': 'exclamation-triangle'
};

const MAP_CONFIG = {
    center: [31.7917, -7.0926],
    zoom: 6,
    maxZoom: 18,
    minZoom: 5
};

// Variables globales
let markersLayer;
let isMapInitialized = false;

// ==================== CRÉATION DES MARQUEURS ====================
function createCustomIcon(type) {
    const color = MARKER_COLORS[type] || '#6c757d';
    const icon = MARKER_ICONS[type] || 'map-marker-alt';

    return L.divIcon({
        html: `
            <div class="custom-marker-icon" style="
                background-color: ${color};
                width: 40px;
                height: 40px;
                border-radius: 50% 50% 50% 0;
                border: 3px solid white;
                box-shadow: 0 4px 15px rgba(0,0,0,0.4);
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                transition: all 0.3s ease;
                transform: rotate(-45deg);
                position: relative;
            " onmouseover="this.style.transform='rotate(-45deg) scale(1.2)'"
               onmouseout="this.style.transform='rotate(-45deg) scale(1)'">
                <i class="fas fa-${icon}" style="
                    color: white;
                    font-size: 16px;
                    transform: rotate(45deg);
                    text-shadow: 0 1px 3px rgba(0,0,0,0.3);
                "></i>
            </div>
        `,
        iconSize: [40, 40],
        iconAnchor: [20, 35],
        popupAnchor: [0, -35],
        className: 'custom-marker-wrapper'
    });
}

function createPopupContent(item) {
    const color = MARKER_COLORS[item.type] || '#6c757d';
    const icon = MARKER_ICONS[item.type] || 'map-marker-alt';

    let content = `
        <div class="marker-popup">
            <h6 style="color: ${color}; margin-bottom: 10px;">
                <i class="fas fa-${icon}"></i> ${item.name}
            </h6>
            <table class="info-table">
    `;

    // Ajouter les informations spécifiques
    Object.entries(item.info || {}).forEach(([key, value]) => {
        if (value && value.toString().trim() !== '') {
            const displayKey = key.charAt(0).toUpperCase() + key.slice(1);
            content += `<tr><td>${displayKey}:</td><td>${value}</td></tr>`;
        }
    });

    content += `
                <tr><td>Coordonnées:</td><td>${item.lat.toFixed(4)}, ${item.lng.toFixed(4)}</td></tr>
            </table>
        </div>
    `;

    return content;
}

function createMarker(item) {
    const marker = L.marker([item.lat, item.lng], {
        icon: createCustomIcon(item.type)
    });

    marker.bindPopup(createPopupContent(item), {
        maxWidth: 300,
        className: 'custom-popup'
    });

    marker.markerType = item.type;
    marker.itemData = item;

    return marker;
}

// ==================== TRAITEMENT DES DONNÉES ====================
function processMapData() {
    console.log('Traitement des données de la carte...');
    console.log('Données reçues:', mapData);
    allMarkers = [];

    // Traiter les sites
    if (mapData.sites && mapData.sites.length > 0) {
        console.log(`Traitement de ${mapData.sites.length} sites...`);
        mapData.sites.forEach((site, index) => {
            const coords = getCoordinates(site);
            console.log(`Site ${site.nom}: GPS=${site.gps}, Coords calculées:`, coords);

            const markerData = {
                id: site.id,
                name: site.nom || 'Site sans nom',
                type: 'site',
                lat: coords[0],
                lng: coords[1],
                info: {
                    région: site.region_nom || '',
                    adresse: site.adresse || '',
                    téléphone: site.telephone || '',
                    responsable: site.responsable || '',
                    email: site.email || ''
                }
            };

            allMarkers.push(createMarker(markerData));
        });
    } else {
        console.log('Aucun site trouvé');
    }

    // Traiter les marchés
    if (mapData.marches && mapData.marches.length > 0) {
        console.log(`Traitement de ${mapData.marches.length} marchés...`);
        mapData.marches.forEach((marche, index) => {
            const coords = getCoordinates(marche);
            console.log(`Marché ${marche.objet}: Lieu=${marche.lieu}, Coords calculées:`, coords);

            const markerData = {
                id: marche.id,
                name: marche.objet || 'Marché sans nom',
                type: 'marche',
                lat: coords[0],
                lng: coords[1],
                info: {
                    numéro: marche.numero || '',
                    client: marche.client || '',
                    montant: marche.montant ? marche.montant + ' DH' : '',
                    date: marche.date || '',
                    lieu: marche.lieu || '',
                    domaine: marche.domaine || ''
                }
            };

            allMarkers.push(createMarker(markerData));
        });
    } else {
        console.log('Aucun marché trouvé');
    }

    // Traiter les interventions
    if (mapData.interventions && mapData.interventions.length > 0) {
        console.log(`Traitement de ${mapData.interventions.length} interventions...`);
        mapData.interventions.forEach((intervention, index) => {
            const coords = getCoordinates(intervention);
            console.log(`Intervention ${intervention.nom_site}: GPS=${intervention.gps}, Coords calculées:`, coords);

            const markerData = {
                id: intervention.id,
                name: intervention.nom_site || intervention.lieu || 'Intervention sans nom',
                type: 'intervention',
                lat: coords[0],
                lng: coords[1],
                info: {
                    client: intervention.client || '',
                    domaine: intervention.domaine || '',
                    région: intervention.region || '',
                    situation: intervention.situation || 'En cours'
                }
            };

            allMarkers.push(createMarker(markerData));
        });
    } else {
        console.log('Aucune intervention trouvée');
    }

    // Traiter les réclamations
    if (mapData.reclamations && mapData.reclamations.length > 0) {
        console.log(`Traitement de ${mapData.reclamations.length} réclamations...`);
        mapData.reclamations.forEach((reclamation, index) => {
            const coords = getCoordinates(reclamation);
            console.log(`Réclamation ${reclamation.nom_site}: GPS=${reclamation.gps}, Coords calculées:`, coords);

            const markerData = {
                id: reclamation.id,
                name: reclamation.nom_site || reclamation.lieu || 'Réclamation sans nom',
                type: 'reclamation',
                lat: coords[0],
                lng: coords[1],
                info: {
                    client: reclamation.client || '',
                    domaine: reclamation.domaine || '',
                    région: reclamation.region || '',
                    situation: reclamation.situation || 'En attente'
                }
            };

            allMarkers.push(createMarker(markerData));
        });
    } else {
        console.log('Aucune réclamation trouvée');
    }

    console.log(`Marqueurs créés: ${allMarkers.length}`);
    updateMarkersDisplay();
}

// ==================== GESTION DES MARQUEURS ====================
function updateMarkersDisplay() {
    if (!markersLayer) return;

    // Vider la couche de marqueurs
    markersLayer.clearLayers();

    // Ajouter les marqueurs selon le filtre
    allMarkers.forEach(marker => {
        if (currentFilter === 'all' || marker.markerType === currentFilter) {
            markersLayer.addLayer(marker);
        }
    });

    updateStatistics();
    console.log(`Marqueurs affichés: ${getVisibleMarkersCount()}`);
}

function getVisibleMarkersCount() {
    return allMarkers.filter(marker =>
        currentFilter === 'all' || marker.markerType === currentFilter
    ).length;
}

function updateStatistics() {
    const stats = {
        sites: allMarkers.filter(m => m.markerType === 'site').length,
        marches: allMarkers.filter(m => m.markerType === 'marche').length,
        interventions: allMarkers.filter(m => m.markerType === 'intervention').length,
        reclamations: allMarkers.filter(m => m.markerType === 'reclamation').length
    };

    // Mettre à jour les statistiques dans l'interface
    document.getElementById('stat-sites').textContent = stats.sites;
    document.getElementById('stat-marches').textContent = stats.marches;
    document.getElementById('stat-interventions').textContent = stats.interventions;
    document.getElementById('stat-reclamations').textContent = stats.reclamations;
    document.getElementById('stat-total').textContent =
        stats.sites + stats.marches + stats.interventions + stats.reclamations;
}

// ==================== FILTRAGE ====================
function filterMarkers(type) {
    currentFilter = type;

    // Mettre à jour les boutons
    document.querySelectorAll('[id^="filter-"]').forEach(btn => {
        btn.classList.remove('active');
    });
    document.getElementById(`filter-${type}`).classList.add('active');

    // Mettre à jour l'affichage
    updateMarkersDisplay();
}

// ==================== FONCTIONS DE FILTRAGE ====================
function showAll() {
    currentFilter = 'all';
    setActiveButton('btn-all');
    updateMarkersDisplay();
}

function showSites() {
    currentFilter = 'site';
    setActiveButton('btn-sites');
    updateMarkersDisplay();
}

function showMarches() {
    currentFilter = 'marche';
    setActiveButton('btn-marches');
    updateMarkersDisplay();
}

function showInterventions() {
    currentFilter = 'intervention';
    setActiveButton('btn-interventions');
    updateMarkersDisplay();
}

function showReclamations() {
    currentFilter = 'reclamation';
    setActiveButton('btn-reclamations');
    updateMarkersDisplay();
}

function setActiveButton(activeId) {
    document.querySelectorAll('[id^="btn-"]').forEach(btn => {
        btn.classList.remove('active');
    });
    document.getElementById(activeId).classList.add('active');
}

// ==================== EXPORT DES DONNÉES ====================
function exportData() {
    const data = [];
    allMarkers.forEach(marker => {
        const coords = marker.getLatLng();
        data.push({
            Type: marker.markerType,
            Nom: marker.itemData.name,
            Latitude: coords.lat.toFixed(6),
            Longitude: coords.lng.toFixed(6)
        });
    });

    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "Type,Nom,Latitude,Longitude\n";
    data.forEach(row => {
        csvContent += `${row.Type},${row.Nom},${row.Latitude},${row.Longitude}\n`;
    });

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "carte_maroc.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// ==================== INITIALISATION DE LA CARTE ====================
function initializeMap() {
    try {
        console.log('🗺️ Initialisation de la carte...');

        // Vérifier que l'élément map existe
        const mapElement = document.getElementById('map');
        if (!mapElement) {
            throw new Error('Élément map non trouvé dans le DOM');
        }

        // Vérifier que Leaflet est chargé
        if (typeof L === 'undefined') {
            throw new Error('Leaflet n\'est pas chargé');
        }

        // Créer la carte avec options améliorées
        map = L.map('map', {
            center: MAP_CONFIG.center,
            zoom: MAP_CONFIG.zoom,
            maxZoom: MAP_CONFIG.maxZoom,
            minZoom: MAP_CONFIG.minZoom,
            zoomControl: true,
            scrollWheelZoom: true,
            doubleClickZoom: true,
            dragging: true,
            touchZoom: true,
            boxZoom: true,
            keyboard: true,
            attributionControl: true
        });

        console.log('✅ Carte créée avec succès');

        // Ajouter les couches de tuiles avec fallback
        const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors',
            maxZoom: MAP_CONFIG.maxZoom,
            subdomains: ['a', 'b', 'c'],
            errorTileUrl: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==',
            crossOrigin: true
        });

        // Événements pour les tuiles
        osmLayer.on('loading', function() {
            console.log('🔄 Chargement des tuiles...');
        });

        osmLayer.on('load', function() {
            console.log('✅ Tuiles chargées');
        });

        osmLayer.on('tileerror', function(e) {
            console.warn('⚠️ Erreur de chargement de tuile:', e);
        });

        // Ajouter la couche par défaut
        osmLayer.addTo(map);
        console.log('✅ Couche de tuiles ajoutée');

        // Créer la couche de marqueurs
        markersLayer = L.layerGroup().addTo(map);
        console.log('✅ Couche de marqueurs créée');

        // Ajouter contrôle d'échelle
        L.control.scale({
            position: 'bottomleft',
            metric: true,
            imperial: false
        }).addTo(map);

        // Marquer comme initialisée
        isMapInitialized = true;

        // Traiter les données et ajouter les marqueurs
        processMapData();

        // Masquer le spinner avec délai
        setTimeout(() => {
            hideLoadingSpinner();
        }, 1000);

        // Ajuster la vue pour montrer tous les marqueurs
        setTimeout(() => {
            if (allMarkers.length > 0) {
                fitMapToMarkers();
            } else {
                console.log('ℹ️ Aucun marqueur à afficher');
            }
        }, 1500);

        console.log('🎉 Carte initialisée avec succès');

    } catch (error) {
        console.error('❌ Erreur lors de l\'initialisation de la carte:', error);
        showError(error.message);
    }
}

// ==================== FONCTIONS UTILITAIRES ====================
function hideLoadingSpinner() {
    const spinner = document.getElementById('loading-spinner');
    if (spinner) {
        spinner.style.display = 'none';
    }
}

function showError(message) {
    const spinner = document.getElementById('loading-spinner');
    const errorDisplay = document.getElementById('error-display');
    const errorMessage = document.getElementById('error-message');

    if (spinner) {
        spinner.style.display = 'none';
    }

    if (errorDisplay && errorMessage) {
        errorMessage.textContent = message;
        errorDisplay.classList.remove('d-none');
        errorDisplay.classList.add('d-flex');
    }
}

// ==================== FONCTIONS UTILITAIRES DE LA CARTE ====================
function fitMapToMarkers() {
    if (!map || allMarkers.length === 0) return;

    const visibleMarkers = allMarkers.filter(marker =>
        currentFilter === 'all' || marker.markerType === currentFilter
    );

    if (visibleMarkers.length === 0) {
        // Si aucun marqueur visible, centrer sur le Maroc
        map.setView(MAP_CONFIG.center, MAP_CONFIG.zoom);
        return;
    }

    try {
        const group = new L.featureGroup(visibleMarkers);
        const bounds = group.getBounds();

        if (bounds.isValid()) {
            map.fitBounds(bounds.pad(0.1));
        } else {
            map.setView(MAP_CONFIG.center, MAP_CONFIG.zoom);
        }
    } catch (error) {
        console.error('Erreur lors de l\'ajustement de la vue:', error);
        map.setView(MAP_CONFIG.center, MAP_CONFIG.zoom);
    }
}

function toggleFullscreen() {
    const mapContainer = document.getElementById('map-container');

    if (!document.fullscreenElement) {
        mapContainer.requestFullscreen().then(() => {
            setTimeout(() => map.invalidateSize(), 100);
        });
    } else {
        document.exitFullscreen().then(() => {
            setTimeout(() => map.invalidateSize(), 100);
        });
    }
}

function refreshMap() {
    if (!map || !isMapInitialized) {
        console.log('🔄 Réinitialisation complète de la carte...');
        location.reload();
        return;
    }

    console.log('🔄 Actualisation de la carte...');

    // Afficher le spinner
    const spinner = document.getElementById('loading-spinner');
    if (spinner) {
        spinner.style.display = 'flex';
    }

    try {
        // Invalider la taille de la carte
        map.invalidateSize();

        // Vider les marqueurs existants
        if (markersLayer) {
            markersLayer.clearLayers();
        }
        allMarkers = [];

        // Retraiter les données
        processMapData();

        // Ajuster la vue
        setTimeout(() => {
            if (allMarkers.length > 0) {
                fitMapToMarkers();
            }
        }, 500);

        console.log('✅ Carte actualisée avec succès');

    } catch (error) {
        console.error('❌ Erreur lors de l\'actualisation:', error);
        showError('Erreur lors de l\'actualisation: ' + error.message);
    }

    // Masquer le spinner
    setTimeout(() => {
        hideLoadingSpinner();
    }, 1000);
}

function printMap() {
    window.print();
}

function exportToExcel() {
    const data = [];

    allMarkers.forEach(marker => {
        const item = marker.itemData;
        data.push({
            Type: item.type,
            Nom: item.name,
            Latitude: item.lat.toFixed(6),
            Longitude: item.lng.toFixed(6),
            ...item.info
        });
    });

    // Créer un fichier CSV simple
    let csvContent = "data:text/csv;charset=utf-8,";

    // En-têtes
    const headers = Object.keys(data[0] || {});
    csvContent += headers.join(",") + "\n";

    // Données
    data.forEach(row => {
        const values = headers.map(header => {
            const value = row[header] || '';
            return `"${value.toString().replace(/"/g, '""')}"`;
        });
        csvContent += values.join(",") + "\n";
    });

    // Télécharger le fichier
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `carte_maroc_${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// ==================== INITIALISATION ====================
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Chargement de la page carte...');

    try {
        // Vérifier que Leaflet est disponible
        if (typeof L === 'undefined') {
            console.error('❌ Leaflet n\'est pas chargé');
            throw new Error('La bibliothèque Leaflet n\'est pas disponible');
        }

        console.log('✅ Leaflet est disponible');
        console.log('📊 Données chargées:', {
            sites: mapData.sites ? mapData.sites.length : 0,
            marches: mapData.marches ? mapData.marches.length : 0,
            interventions: mapData.interventions ? mapData.interventions.length : 0,
            reclamations: mapData.reclamations ? mapData.reclamations.length : 0
        });

        // Attendre un peu pour s'assurer que tout est chargé
        setTimeout(() => {
            initializeMap();
        }, 100);

    } catch (error) {
        console.error('❌ Erreur lors du chargement:', error);
        showError(error.message);
    }
});

// ==================== GESTION DES ÉVÉNEMENTS ====================
window.addEventListener('resize', function() {
    if (map && isMapInitialized) {
        setTimeout(() => {
            map.invalidateSize();
            console.log('🔄 Taille de carte mise à jour');
        }, 100);
    }
});

// Gestion du plein écran
document.addEventListener('fullscreenchange', function() {
    if (map && isMapInitialized) {
        setTimeout(() => {
            map.invalidateSize();
            console.log('🔄 Carte redimensionnée pour plein écran');
        }, 100);
    }
});

// Gestion des erreurs globales
window.addEventListener('error', function(e) {
    console.error('❌ Erreur JavaScript:', e.error);
    if (e.error && e.error.message && e.error.message.includes('Leaflet')) {
        showError('Erreur de chargement de la carte: ' + e.error.message);
    }
});

</script>
{% endblock %}
