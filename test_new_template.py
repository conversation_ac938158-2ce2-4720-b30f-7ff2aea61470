#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار القالب الجديد لمعلومات الشركة
"""

import requests
import sqlite3

def test_new_template():
    """اختبار القالب الجديد"""
    print("🧪 اختبار القالب الجديد لمعلومات الشركة")
    print("=" * 50)
    
    # 1. اختبار الوصول للصفحة
    print("1️⃣ اختبار الوصول للصفحة...")
    try:
        response = requests.get('http://127.0.0.1:5000/societe', timeout=5)
        if response.status_code == 302:
            print("   ✅ الصفحة تتطلب تسجيل دخول (طبيعي)")
        elif response.status_code == 200:
            print("   ✅ الصفحة متاحة")
        else:
            print(f"   ⚠️ رمز الحالة: {response.status_code}")
    except Exception as e:
        print(f"   ❌ خطأ في الوصول: {e}")
    
    # 2. فحص قاعدة البيانات
    print("\n2️⃣ فحص قاعدة البيانات...")
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM societe LIMIT 1")
        info = cursor.fetchone()
        
        if info:
            print("   📊 بيانات الشركة:")
            for key in info.keys():
                value = info[key]
                if key == 'logo':
                    status = "موجود" if value else "فارغ"
                    print(f"      {key}: {status}")
                else:
                    display_value = value if value else "فارغ"
                    print(f"      {key}: {display_value}")
        else:
            print("   ❌ لا توجد بيانات شركة")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {e}")
    
    # 3. فحص الملفات المطلوبة
    print("\n3️⃣ فحص الملفات المطلوبة...")
    files_to_check = [
        'forms.py',
        'templates/societe.html',
        'templates/societe_backup.html'
    ]
    
    import os
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"   ✅ {file_path} ({size} bytes)")
        else:
            print(f"   ❌ {file_path} غير موجود")
    
    # 4. فحص Flask-WTF
    print("\n4️⃣ فحص Flask-WTF...")
    try:
        from forms import SocieteForm
        form = SocieteForm()
        print("   ✅ Flask-WTF يعمل بشكل صحيح")
        print(f"   📋 عدد الحقول في النموذج: {len(form._fields)}")
        print("   📝 الحقول المتاحة:")
        for field_name in form._fields:
            print(f"      - {field_name}")
    except Exception as e:
        print(f"   ❌ خطأ في Flask-WTF: {e}")
    
    print("\n🎯 تعليمات الاختبار:")
    print("1. افتح المتصفح على: http://127.0.0.1:5000")
    print("2. سجل دخول بـ: admin / admin123")
    print("3. انتقل إلى: Informations Société")
    print("4. يجب أن ترى:")
    print("   - نموذج بسيط ونظيف")
    print("   - جميع الحقول فارغة")
    print("   - حقل تحميل الشعار")
    print("   - زر 'Enregistrer les modifications'")
    print("5. جرب:")
    print("   - ملء بعض الحقول")
    print("   - تحميل شعار")
    print("   - حفظ التغييرات")
    
    print("\n✅ انتهى الاختبار!")

if __name__ == "__main__":
    test_new_template()
