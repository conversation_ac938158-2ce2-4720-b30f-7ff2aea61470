{% extends "base.html" %}

{% block title %}Détails du Marché - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Détails du Marché N° {{ marche.numero }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-file-contract"></i> Informations du marché</h5>
                    <div class="header-right">
                        <div class="btn-group" role="group">
                            <a href="{{ url_for('modifier_marche', numero=marche.numero) }}" class="btn btn-primary btn-sm">
                                <i class="fas fa-edit"></i> Modifier
                            </a>

                            <a href="{{ url_for('marches') }}" class="btn btn-success btn-sm">
                                <i class="fas fa-arrow-left"></i> Retour
                            </a>
                            <button type="button" class="btn btn-info btn-sm" onclick="location.reload()">
                                <i class="fas fa-sync-alt"></i> Actualiser
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 40%">N° de marché</th>
                                <td>{{ marche.numero }}</td>
                            </tr>
                            <tr>
                                <th>Domaine</th>
                                <td>
                                    {% if marche.domaine == 'SVS' %}
                                    <span class="badge bg-primary">SVS</span>
                                    {% elif marche.domaine == 'EXTINCTEUR' %}
                                    <span class="badge bg-danger">EXTINCTEUR</span>
                                    {% elif marche.domaine == 'SYSTEME D\'INCENDIE' %}
                                    <span class="badge bg-warning">SYSTEME D'INCENDIE</span>
                                    {% elif marche.domaine == 'SYSTEME D\'ALARME' %}
                                    <span class="badge bg-info">SYSTEME D'ALARME</span>
                                    {% elif marche.domaine == 'SYSTEME TELEPHONIQUE' %}
                                    <span class="badge bg-success">SYSTEME TELEPHONIQUE</span>
                                    {% elif marche.domaine == "L'AFFICHAGE DYNAMIQUE" %}
                                    <span class="badge bg-dark">L'AFFICHAGE DYNAMIQUE</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ marche.domaine }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Date</th>
                                <td>{{ marche.date }}</td>
                            </tr>
                            <tr>
                                <th>Objet de marché</th>
                                <td>{{ marche.objet }}</td>
                            </tr>
                            <tr>
                                <th>Client</th>
                                <td>{{ marche.client }}</td>
                            </tr>
                            <tr>
                                <th>Lieu</th>
                                <td>{{ marche.lieu }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 40%">Montant</th>
                                <td>{{ marche.montant }} DH</td>
                            </tr>
                            <tr>
                                <th>Délai d'exécution</th>
                                <td>{{ marche.delai_execution }}</td>
                            </tr>
                            <tr>
                                <th>Période d'interventions</th>
                                <td>{{ marche.periode_interventions }}</td>
                            </tr>
                            <tr>
                                <th>Date d'ordre de service</th>
                                <td>{{ marche.date_ordre_service }}</td>
                            </tr>
                            <tr>
                                <th>Caution définitif</th>
                                <td>{{ marche.caution_definitif }}</td>
                            </tr>
                            <tr>
                                <th>Mode de paiement</th>
                                <td>{{ marche.mode_paiement }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-tools"></i> Interventions liées à ce marché</h5>
                    <a href="{{ url_for('ajouter_intervention') }}?marche={{ marche.numero }}" class="btn btn-success">
                        <i class="fas fa-plus"></i> Nouvelle intervention
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if interventions %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Domaine</th>
                                <th>Client</th>
                                <th>Lieu</th>
                                <th>Période</th>
                                <th>Date de création</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for intervention in interventions %}
                            <tr>
                                <td>{{ intervention.id }}</td>
                                <td>
                                    {% if intervention.domaine == 'SVS' %}
                                    <span class="badge bg-primary">SVS</span>
                                    {% elif intervention.domaine == 'EXTINCTEUR' %}
                                    <span class="badge bg-danger">EXTINCTEUR</span>
                                    {% elif intervention.domaine == 'SYSTEME D\'INCENDIE' %}
                                    <span class="badge bg-warning">SYSTEME D'INCENDIE</span>
                                    {% elif intervention.domaine == 'SYSTEME D\'ALARME' %}
                                    <span class="badge bg-info">SYSTEME D'ALARME</span>
                                    {% elif intervention.domaine == 'SYSTEME TELEPHONIQUE' %}
                                    <span class="badge bg-success">SYSTEME TELEPHONIQUE</span>
                                    {% elif intervention.domaine == "L'AFFICHAGE DYNAMIQUE" %}
                                    <span class="badge bg-dark">L'AFFICHAGE DYNAMIQUE</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ intervention.domaine }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ intervention.client }}</td>
                                <td>{{ intervention.lieu }}</td>
                                <td>{{ intervention.periode_interventions }}</td>
                                <td>{{ intervention.date_creation }}</td>
                                <td>
                                    <a href="{{ url_for('details_intervention', id=intervention.id) }}" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-center">Aucune intervention liée à ce marché</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-exclamation-circle"></i> Réclamations liées à ce marché</h5>
                    <a href="#" class="btn btn-success">
                        <i class="fas fa-plus"></i> Nouvelle réclamation
                    </a>
                </div>
            </div>
            <div class="card-body">
                {% if reclamations %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Domaine</th>
                                <th>Client</th>
                                <th>Lieu</th>
                                <th>Date de création</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for reclamation in reclamations %}
                            <tr>
                                <td>{{ reclamation.id }}</td>
                                <td>
                                    {% if reclamation.domaine == 'SVS' %}
                                    <span class="badge bg-primary">SVS</span>
                                    {% elif reclamation.domaine == 'EXTINCTEUR' %}
                                    <span class="badge bg-danger">EXTINCTEUR</span>
                                    {% elif reclamation.domaine == 'SYSTEME D\'INCENDIE' %}
                                    <span class="badge bg-warning">SYSTEME D'INCENDIE</span>
                                    {% elif reclamation.domaine == 'SYSTEME D\'ALARME' %}
                                    <span class="badge bg-info">SYSTEME D'ALARME</span>
                                    {% elif reclamation.domaine == 'SYSTEME TELEPHONIQUE' %}
                                    <span class="badge bg-success">SYSTEME TELEPHONIQUE</span>
                                    {% elif reclamation.domaine == "L'AFFICHAGE DYNAMIQUE" %}
                                    <span class="badge bg-dark">L'AFFICHAGE DYNAMIQUE</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ reclamation.domaine }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ reclamation.client }}</td>
                                <td>{{ reclamation.lieu }}</td>
                                <td>{{ reclamation.date_creation }}</td>
                                <td>
                                    <a href="#" class="btn btn-sm btn-info">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-center">Aucune réclamation liée à ce marché</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
