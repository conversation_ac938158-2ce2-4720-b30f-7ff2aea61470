@echo off
title تثبيت متطلبات نظام إدارة الصيانة
echo.
echo ========================================
echo    تثبيت متطلبات نظام إدارة الصيانة
echo    Installing Maintenance System Requirements
echo ========================================
echo.

echo 📦 تثبيت المكتبات المطلوبة...
echo 📦 Installing required packages...
echo.

python -m pip install --upgrade pip
if errorlevel 1 (
    echo ❌ فشل في تحديث pip
    echo ❌ Failed to upgrade pip
    pause
    exit /b 1
)

python -m pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ فشل في تثبيت المتطلبات
    echo ❌ Failed to install requirements
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت جميع المتطلبات بنجاح!
echo ✅ All requirements installed successfully!
echo.
echo 🔨 يمكنك الآن تشغيل build_exe.py لبناء الملف التنفيذي
echo 🔨 You can now run build_exe.py to build the executable
echo.
pause
