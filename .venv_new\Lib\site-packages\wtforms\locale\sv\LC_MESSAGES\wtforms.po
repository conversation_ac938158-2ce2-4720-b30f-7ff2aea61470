# Swedish translations for WTForms.
# Copyright (C) 2020 WTForms Team
# This file is distributed under the same license as the WTForms project.
# <AUTHOR> <EMAIL>, 2020.
#
msgid ""
msgstr ""
"Project-Id-Version: WTForms 2.0dev\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2024-01-11 08:20+0100\n"
"PO-Revision-Date: 2024-01-24 11:01+0000\n"
"Last-Translator: bittin1ddc447d824349b2 <<EMAIL>>\n"
"Language-Team: Swedish <https://hosted.weblate.org/projects/wtforms/wtforms/"
"sv/>\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 5.4-dev\n"
"Generated-By: Babel 2.8.0\n"

#: src/wtforms/validators.py:86
#, python-format
msgid "Invalid field name '%s'."
msgstr "Felaktigt fältnamn '%s'."

#: src/wtforms/validators.py:99
#, python-format
msgid "Field must be equal to %(other_name)s."
msgstr "Fältvärdet måste matcha %(other_name)s."

#: src/wtforms/validators.py:145
#, python-format
msgid "Field must be at least %(min)d character long."
msgid_plural "Field must be at least %(min)d characters long."
msgstr[0] "Fältet måste vara minst %(min)d tecken långt."
msgstr[1] "Fältet måste vara minst %(min)d tecken långt."

#: src/wtforms/validators.py:151
#, python-format
msgid "Field cannot be longer than %(max)d character."
msgid_plural "Field cannot be longer than %(max)d characters."
msgstr[0] "Fältet får inte vara längre än %(max)d tecken."
msgstr[1] "Fältet får inte vara längre än %(max)d tecken."

#: src/wtforms/validators.py:157
#, python-format
msgid "Field must be exactly %(max)d character long."
msgid_plural "Field must be exactly %(max)d characters long."
msgstr[0] "Fält måste vara exakt %(max)d tecken långt."
msgstr[1] "Fältet måste vara exakt %(max)d tecken långt."

#: src/wtforms/validators.py:163
#, python-format
msgid "Field must be between %(min)d and %(max)d characters long."
msgstr "Fältet måste vara mellan %(min)d och %(max)d tecken långt."

#: src/wtforms/validators.py:216
#, python-format
msgid "Number must be at least %(min)s."
msgstr "Numret får inte vara mindre än %(min)s."

#: src/wtforms/validators.py:219
#, python-format
msgid "Number must be at most %(max)s."
msgstr "Numret får inte vara högre än %(max)s."

#: src/wtforms/validators.py:222
#, python-format
msgid "Number must be between %(min)s and %(max)s."
msgstr "Numret måste vara mellan %(min)s och %(max)s."

#: src/wtforms/validators.py:293 src/wtforms/validators.py:323
msgid "This field is required."
msgstr "Det här fältet är obligatoriskt."

#: src/wtforms/validators.py:358
msgid "Invalid input."
msgstr "Felaktig indata."

#: src/wtforms/validators.py:422
msgid "Invalid email address."
msgstr "Felaktig epost-adress."

#: src/wtforms/validators.py:460
msgid "Invalid IP address."
msgstr "Felaktig IP-adress."

#: src/wtforms/validators.py:503
msgid "Invalid Mac address."
msgstr "Felaktig MAC-adress."

#: src/wtforms/validators.py:540
msgid "Invalid URL."
msgstr "Felaktig URL."

#: src/wtforms/validators.py:561
msgid "Invalid UUID."
msgstr "Felaktig UUID."

#: src/wtforms/validators.py:594
#, python-format
msgid "Invalid value, must be one of: %(values)s."
msgstr "Felaktigt värde, måste vara ett av: %(values)s."

#: src/wtforms/validators.py:629
#, python-format
msgid "Invalid value, can't be any of: %(values)s."
msgstr "Felaktigt värde, får inte vara något av: %(values)s."

#: src/wtforms/validators.py:698
msgid "This field cannot be edited."
msgstr "Detta fält kan inte redigeras."

#: src/wtforms/validators.py:714
msgid "This field is disabled and cannot have a value."
msgstr "Det här fältet är inaktiverat och kan inte ha ett värde."

#: src/wtforms/csrf/core.py:96
msgid "Invalid CSRF Token."
msgstr "Felaktigt CSRF-token"

#: src/wtforms/csrf/session.py:63
msgid "CSRF token missing."
msgstr "CSRF-token saknas"

#: src/wtforms/csrf/session.py:71
msgid "CSRF failed."
msgstr "CSRF misslyckades"

#: src/wtforms/csrf/session.py:76
msgid "CSRF token expired."
msgstr "CSRF-token utdaterat"

#: src/wtforms/fields/choices.py:142
msgid "Invalid Choice: could not coerce."
msgstr "Felaktigt val; kunde inte ceorce:a"

#: src/wtforms/fields/choices.py:149 src/wtforms/fields/choices.py:203
msgid "Choices cannot be None."
msgstr "Val kan inte vara Inga."

#: src/wtforms/fields/choices.py:155
msgid "Not a valid choice."
msgstr "Inte ett giltigt val"

#: src/wtforms/fields/choices.py:193
msgid "Invalid choice(s): one or more data inputs could not be coerced."
msgstr "Felaktigt val; ett eller flera inputfält kunde inte coerca:s"

#: src/wtforms/fields/choices.py:214
#, python-format
msgid "'%(value)s' is not a valid choice for this field."
msgid_plural "'%(value)s' are not valid choices for this field."
msgstr[0] "'%(value)s' är inte ett giltigt val för detta fält."
msgstr[1] "'%(value)s' är inte giltiga val för detta fält."

#: src/wtforms/fields/datetime.py:51
msgid "Not a valid datetime value."
msgstr "Inte ett giltigt datum-/tidsvärde"

#: src/wtforms/fields/datetime.py:77
msgid "Not a valid date value."
msgstr "Inte ett giltigt datum"

#: src/wtforms/fields/datetime.py:103
msgid "Not a valid time value."
msgstr "Inte ett giltigt tidsvärde."

#: src/wtforms/fields/datetime.py:148
msgid "Not a valid week value."
msgstr "Inte ett giltigt veckovärde."

#: src/wtforms/fields/numeric.py:82 src/wtforms/fields/numeric.py:92
msgid "Not a valid integer value."
msgstr "Inte ett giltigt heltal"

#: src/wtforms/fields/numeric.py:168
msgid "Not a valid decimal value."
msgstr "Inte ett giltigt decimaltal"

#: src/wtforms/fields/numeric.py:197
msgid "Not a valid float value."
msgstr "Inte ett giltigt flyttal"
