{% extends "base.html" %}

{% block title %}Connexion - Système de Gestion de Maintenance{% endblock %}

{% block auth_content %}
<style>
    body {
        background: linear-gradient(135deg, #3498db, #8e44ad);
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        overflow: hidden;
        position: relative;
    }

    body::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url("{{ url_for('static', filename='images/pattern.png') }}");
        opacity: 0.05;
        z-index: -1;
    }

    .login-container {
        width: 100%;
        max-width: 420px;
        padding: 20px;
        animation: fadeIn 0.8s ease-in-out;
    }

    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(-20px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .login-box {
        background-color: rgba(255, 255, 255, 0.95);
        border-radius: 15px;
        padding: 40px 30px;
        box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        text-align: center;
        position: relative;
        overflow: hidden;
    }

    .login-box::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 5px;
        background: linear-gradient(90deg, #3498db, #8e44ad);
    }

    .logo-container {
        margin-bottom: 30px;
    }

    .logo-container .logo {
        width: 280px;
        height: auto;
        max-height: 120px;
        object-fit: contain;
        margin-bottom: 25px;
        filter: drop-shadow(0 4px 15px rgba(0,0,0,0.1));
    }

    .logo-container h1 {
        color: #2c3e50;
        font-size: 24px;
        margin-bottom: 5px;
        font-weight: 600;
    }

    .logo-container p {
        color: #7f8c8d;
        font-size: 14px;
        margin-bottom: 20px;
    }

    .input-group {
        position: relative;
        margin-bottom: 25px;
    }

    .input-group input {
        width: 100%;
        padding: 12px 15px 12px 45px;
        border: none;
        border-radius: 30px;
        background-color: #f5f5f5;
        font-size: 15px;
        color: #333;
        transition: all 0.3s;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
    }

    .input-group input:focus {
        outline: none;
        background-color: #fff;
        box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.3);
    }

    .input-group i {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        color: #7f8c8d;
        font-size: 18px;
    }

    .login-btn {
        width: 100%;
        padding: 14px;
        border: none;
        border-radius: 30px;
        background: linear-gradient(90deg, #3498db, #8e44ad);
        color: white;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        transition: all 0.3s;
        box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        margin-top: 10px;
    }

    .login-btn:hover {
        background: linear-gradient(90deg, #2980b9, #8e44ad);
        box-shadow: 0 7px 20px rgba(52, 152, 219, 0.6);
        transform: translateY(-2px);
    }

    .login-btn:active {
        transform: translateY(0);
        box-shadow: 0 3px 10px rgba(52, 152, 219, 0.4);
    }

    .links {
        margin-top: 25px;
        display: flex;
        justify-content: space-between;
    }

    .links a {
        color: #7f8c8d;
        text-decoration: none;
        font-size: 14px;
        transition: color 0.3s;
    }

    .links a:hover {
        color: #3498db;
    }

    .alert {
        padding: 12px 15px;
        margin-bottom: 20px;
        border-radius: 8px;
        font-size: 14px;
    }

    .alert-success {
        background-color: #d4edda;
        color: #155724;
        border-left: 4px solid #28a745;
    }

    .alert-danger {
        background-color: #f8d7da;
        color: #721c24;
        border-left: 4px solid #dc3545;
    }

    .alert-info {
        background-color: #d1ecf1;
        color: #0c5460;
        border-left: 4px solid #17a2b8;
    }

    /* Boutons modernes */
    .btn-modern {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: none;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .btn-modern:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn-modern i {
        font-size: 0.9em;
    }

    /* Couleurs spécifiques */
    .btn-primary.btn-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .btn-success.btn-modern {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .btn-danger.btn-modern {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .btn-warning.btn-modern {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #333;
    }

    .btn-info.btn-modern {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
    }

    .btn-outline-primary.btn-modern {
        border: 2px solid #667eea;
        color: #667eea;
        background: transparent;
    }

    .btn-outline-primary.btn-modern:hover {
        background: #667eea;
        color: white;
    }

</style>

<div class="login-container">
    <div class="login-box">
        <div class="logo-container">
            {% if company_info and company_info.logo %}
                <img src="{{ url_for('static', filename='uploads/' + company_info.logo) }}" alt="Logo {{ company_info.nom }}" class="logo" onerror="this.src='{{ url_for('static', filename='images/logo.png') }}'">
            {% else %}
                <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Logo" class="logo">
            {% endif %}
            <h1>{{ 'Gestion de Maintenance' if company_info and company_info.nom == 'MAXAFFAIRE' else (company_info.nom if company_info and company_info.nom else 'Gestion de Maintenance') }}</h1>
            <p>{{ company_info.pied_page if company_info and company_info.pied_page else 'Système professionnel de suivi et gestion' }}</p>
        </div>

        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ category }}">
                        {{ message }}
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}

        <form method="POST" action="{{ url_for('login') }}">
            <div class="input-group">
                <i class="fas fa-user"></i>
                <input type="text" name="nom_utilisateur" placeholder="Nom d'utilisateur" required autofocus>
            </div>

            <div class="input-group">
                <i class="fas fa-lock"></i>
                <input type="password" name="mot_de_passe" placeholder="Mot de passe" required>
            </div>

            <button type="submit" class="login-btn">
                <i class="fas fa-sign-in-alt"></i> Connexion
            </button>

            <div class="links">
                <a href="{{ url_for('forgot_password') }}"><i class="fas fa-question-circle"></i> Mot de passe oublié?</a>
                <a href="{{ url_for('change_password') }}" style="display: none;"><i class="fas fa-info-circle"></i> Aide</a>
            </div>
        </form>
    </div>
</div>
{% endblock %}
