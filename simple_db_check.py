import sqlite3

# فحص قاعدة البيانات
conn = sqlite3.connect('maintenance.db')
cursor = conn.cursor()

print("=== فحص جدول marches ===")
cursor.execute("PRAGMA table_info(marches)")
columns = cursor.fetchall()
print("الأعمدة:")
for col in columns:
    print(f"  - {col[1]} ({col[2]})")

print("\n=== عينة من البيانات ===")
cursor.execute("SELECT * FROM marches LIMIT 3")
marches = cursor.fetchall()
for marche in marches:
    print(f"السوق: {marche}")

print("\n=== اختبار البحث ===")
cursor.execute("SELECT numero FROM marches")
all_nums = cursor.fetchall()
print("جميع أرقام الأسواق:")
for num in all_nums:
    print(f"  - {num[0]}")

conn.close()
print("\nانتهى الفحص")
