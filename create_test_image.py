#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء صورة اختبار بسيطة لتجربة تحميل الشعار
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_test_logo():
    """إنشاء صورة شعار اختبار"""
    print("🎨 إنشاء صورة شعار اختبار...")
    
    # إنشاء صورة جديدة
    width, height = 200, 150
    image = Image.new('RGB', (width, height), color='#4CAF50')
    
    # إضافة نص
    draw = ImageDraw.Draw(image)
    
    try:
        # محاولة استخدام خط افتراضي
        font = ImageFont.load_default()
    except:
        font = None
    
    # إضافة نص الشعار
    text = "LOGO TEST"
    if font:
        # حساب موقع النص في المنتصف
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        x = (width - text_width) // 2
        y = (height - text_height) // 2
        
        draw.text((x, y), text, fill='white', font=font)
    else:
        # استخدام خط افتراضي بسيط
        draw.text((50, 70), text, fill='white')
    
    # إضافة إطار
    draw.rectangle([5, 5, width-5, height-5], outline='white', width=3)
    
    # حفظ الصورة
    filename = 'test_logo.png'
    image.save(filename)
    
    # التحقق من حجم الملف
    file_size = os.path.getsize(filename)
    print(f"✅ تم إنشاء {filename} ({file_size} bytes)")
    
    return filename

if __name__ == "__main__":
    try:
        logo_file = create_test_logo()
        print(f"🎯 يمكنك الآن استخدام {logo_file} لاختبار تحميل الشعار")
        print("📝 خطوات الاختبار:")
        print("1. افتح المتصفح على http://127.0.0.1:5000")
        print("2. سجل دخول بـ admin/admin123")
        print("3. انتقل إلى Informations Société")
        print("4. انقر على منطقة الشعار")
        print(f"5. اختر ملف {logo_file}")
        print("6. انقر على 'Enregistrer les modifications'")
        print("7. راقب سجلات الخادم في Terminal")
    except Exception as e:
        print(f"❌ خطأ في إنشاء الصورة: {e}")
        print("💡 تأكد من تثبيت Pillow: pip install Pillow")
