#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح التكرار في خيارات L'AFFICHAGE DYNAMIQUE
"""

import os
import re

def fix_duplicates():
    """إصلاح التكرار في جميع القوالب"""
    
    print("🔧 إصلاح التكرار في L'AFFICHAGE DYNAMIQUE")
    print("=" * 50)
    
    # قائمة القوالب للإصلاح
    templates_to_fix = [
        'templates/ajouter_marche.html',
        'templates/modifier_marche.html',
        'templates/marches.html',
        'templates/ajouter_intervention.html',
        'templates/interventions.html',
        'templates/ajouter_reclamation.html',
        'templates/reclamations.html'
    ]
    
    fixed_files = 0
    
    for template_path in templates_to_fix:
        if os.path.exists(template_path):
            print(f"\n📄 إصلاح {template_path}...")
            
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # البحث عن التكرارات وإزالتها
            duplicate_patterns = [
                # نمط 1: تكرار مباشر
                r'(<option value="L\'AFFICHAGE DYNAMIQUE"[^>]*>L\'AFFICHAGE DYNAMIQUE</option>)\s*\n\s*<option value="L\'AFFICHAGE DYNAMIQUE"[^>]*>L\'AFFICHAGE DYNAMIQUE</option>',
                
                # نمط 2: تكرار مع selected
                r'(<option value="L\'AFFICHAGE DYNAMIQUE"[^>]*>L\'AFFICHAGE DYNAMIQUE</option>)\s*\n\s*<option value="L\'AFFICHAGE DYNAMIQUE"[^>]*selected[^>]*>L\'AFFICHAGE DYNAMIQUE</option>',
                
                # نمط 3: تكرار عكسي
                r'(<option value="L\'AFFICHAGE DYNAMIQUE"[^>]*selected[^>]*>L\'AFFICHAGE DYNAMIQUE</option>)\s*\n\s*<option value="L\'AFFICHAGE DYNAMIQUE"[^>]*>L\'AFFICHAGE DYNAMIQUE</option>',
                
                # نمط 4: تكرار مع مسافات مختلفة
                r'(<option value="L\'AFFICHAGE DYNAMIQUE">L\'AFFICHAGE DYNAMIQUE</option>)\s*\n\s*<option value="L\'AFFICHAGE DYNAMIQUE">L\'AFFICHAGE DYNAMIQUE</option>',
                
                # نمط 5: تكرار مع اقتباسات مختلفة
                r'(<option value="L\'AFFICHAGE DYNAMIQUE"[^>]*>L\'AFFICHAGE DYNAMIQUE</option>)\s*\n[^<]*<option value="L\'AFFICHAGE DYNAMIQUE"[^>]*>L\'AFFICHAGE DYNAMIQUE</option>'
            ]
            
            duplicates_found = 0
            for pattern in duplicate_patterns:
                matches = re.findall(pattern, content)
                if matches:
                    content = re.sub(pattern, r'\1', content)
                    duplicates_found += len(matches)
                    print(f"   ✅ تم إزالة {len(matches)} تكرار")
            
            # إزالة التكرارات المتتالية (أكثر من 2)
            while True:
                before = content
                content = re.sub(
                    r'(<option value="L\'AFFICHAGE DYNAMIQUE"[^>]*>L\'AFFICHAGE DYNAMIQUE</option>)\s*\n\s*<option value="L\'AFFICHAGE DYNAMIQUE"[^>]*>L\'AFFICHAGE DYNAMIQUE</option>',
                    r'\1',
                    content
                )
                if content == before:
                    break
                duplicates_found += 1
            
            # حفظ الملف إذا تم تعديله
            if content != original_content:
                with open(template_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"   💾 تم حفظ الإصلاحات ({duplicates_found} تكرار تم إزالته)")
                fixed_files += 1
            else:
                print(f"   ℹ️ لا توجد تكرارات للإزالة")
        else:
            print(f"   ❌ الملف غير موجود: {template_path}")
    
    return fixed_files

def verify_no_duplicates():
    """التحقق من عدم وجود تكرارات"""
    
    print(f"\n🔍 التحقق من عدم وجود تكرارات...")
    
    templates_to_check = [
        'templates/ajouter_marche.html',
        'templates/modifier_marche.html',
        'templates/marches.html',
        'templates/ajouter_intervention.html',
        'templates/interventions.html',
        'templates/ajouter_reclamation.html',
        'templates/reclamations.html'
    ]
    
    clean_files = 0
    
    for template_path in templates_to_check:
        if os.path.exists(template_path):
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # عد مرات ظهور L'AFFICHAGE DYNAMIQUE في options
            option_pattern = r'<option[^>]*value="L\'AFFICHAGE DYNAMIQUE"[^>]*>L\'AFFICHAGE DYNAMIQUE</option>'
            matches = re.findall(option_pattern, content)
            
            print(f"   📄 {template_path}: {len(matches)} خيار")
            
            if len(matches) <= 1:
                print(f"      ✅ نظيف (لا توجد تكرارات)")
                clean_files += 1
            else:
                print(f"      ⚠️ يحتوي على {len(matches)} خيار (قد يكون تكرار)")
                
                # عرض السياق
                lines = content.split('\n')
                for i, line in enumerate(lines):
                    if 'L\'AFFICHAGE DYNAMIQUE' in line and '<option' in line:
                        print(f"         السطر {i+1}: {line.strip()}")
        else:
            print(f"   ❌ {template_path}: غير موجود")
    
    print(f"\n📊 ملفات نظيفة: {clean_files}/{len(templates_to_check)}")
    return clean_files == len(templates_to_check)

def manual_fix_remaining():
    """إصلاح يدوي للتكرارات المتبقية"""
    
    print(f"\n🔧 إصلاح يدوي للتكرارات المتبقية...")
    
    # قائمة الإصلاحات اليدوية
    manual_fixes = [
        {
            'file': 'templates/ajouter_marche.html',
            'search': r'(<option value="SYSTEME TELEPHONIQUE">SYSTEME TELEPHONIQUE</option>)\s*\n.*<option value="L\'AFFICHAGE DYNAMIQUE">L\'AFFICHAGE DYNAMIQUE</option>\s*\n.*<option value="L\'AFFICHAGE DYNAMIQUE">L\'AFFICHAGE DYNAMIQUE</option>',
            'replace': r'\1\n                                <option value="L\'AFFICHAGE DYNAMIQUE">L\'AFFICHAGE DYNAMIQUE</option>'
        },
        {
            'file': 'templates/modifier_marche.html',
            'search': r'(<option value="SYSTEME TELEPHONIQUE"[^>]*>SYSTEME TELEPHONIQUE</option>)\s*\n.*<option value="L\'AFFICHAGE DYNAMIQUE"[^>]*>L\'AFFICHAGE DYNAMIQUE</option>\s*\n.*<option value="L\'AFFICHAGE DYNAMIQUE"[^>]*>L\'AFFICHAGE DYNAMIQUE</option>',
            'replace': r'\1\n                                <option value="L\'AFFICHAGE DYNAMIQUE" {% if marche.domaine == "L\'AFFICHAGE DYNAMIQUE" %}selected{% endif %}>L\'AFFICHAGE DYNAMIQUE</option>'
        }
    ]
    
    for fix in manual_fixes:
        file_path = fix['file']
        if os.path.exists(file_path):
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            content = re.sub(fix['search'], fix['replace'], content, flags=re.MULTILINE)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"   ✅ تم إصلاح {file_path}")
            else:
                print(f"   ℹ️ {file_path} لا يحتاج إصلاح")
        else:
            print(f"   ❌ {file_path} غير موجود")

if __name__ == "__main__":
    # إصلاح التكرارات
    fixed_count = fix_duplicates()
    
    # إصلاح يدوي إضافي
    manual_fix_remaining()
    
    # التحقق النهائي
    is_clean = verify_no_duplicates()
    
    print(f"\n🎉 انتهى الإصلاح!")
    print("=" * 50)
    print(f"📊 الإحصائيات:")
    print(f"   • ملفات تم إصلاحها: {fixed_count}")
    print(f"   • حالة النظافة: {'✅ نظيف' if is_clean else '⚠️ يحتاج مراجعة'}")
    
    if is_clean:
        print(f"\n✅ جميع التكرارات تم إزالتها بنجاح!")
        print(f"🎯 الآن كل قالب يحتوي على خيار واحد فقط لـ L'AFFICHAGE DYNAMIQUE")
    else:
        print(f"\n⚠️ لا تزال هناك بعض التكرارات")
        print(f"🔧 قد تحتاج إصلاح يدوي إضافي")
