#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الملف التنفيذي
"""

import os
import subprocess
import time

def test_executable():
    """اختبار الملف التنفيذي"""
    
    print("🎯 اختبار الملف التنفيذي")
    print("=" * 50)
    
    # مسار الملف التنفيذي
    exe_path = "dist/Maintenance_Management_System.exe"
    
    # فحص وجود الملف
    if not os.path.exists(exe_path):
        print(f"❌ الملف التنفيذي غير موجود: {exe_path}")
        return False
    
    # معلومات الملف
    file_size = os.path.getsize(exe_path)
    print(f"📁 مسار الملف: {exe_path}")
    print(f"📊 حجم الملف: {file_size:,} بايت ({file_size/1024/1024:.1f} MB)")
    
    # فحص الملفات المرافقة
    dist_files = {
        "dist/README.txt": "دليل الاستخدام",
        "dist/تشغيل_النظام.bat": "ملف التشغيل السريع"
    }
    
    print(f"\n📄 الملفات المرافقة:")
    for file_path, description in dist_files.items():
        if os.path.exists(file_path):
            print(f"   ✅ {file_path} - {description}")
        else:
            print(f"   ❌ {file_path} - {description} (غير موجود)")
    
    print(f"\n🎉 الملف التنفيذي جاهز!")
    print(f"💻 متوافق مع Windows 7, 10, 11 (32/64 bit)")
    print(f"🚫 بدون خلفية سوداء (console=False)")
    print(f"👥 يدعم عدة مستخدمين في نفس الوقت")
    
    return True

def show_usage_instructions():
    """عرض تعليمات الاستخدام"""
    
    print(f"\n📋 تعليمات الاستخدام:")
    print("=" * 50)
    
    print(f"🚀 التشغيل:")
    print(f"   • انقر مرتين على: Maintenance_Management_System.exe")
    print(f"   • أو شغل: تشغيل_النظام.bat")
    print(f"   • سيفتح المتصفح تلقائياً")
    
    print(f"\n🌐 الوصول من أجهزة أخرى:")
    print(f"   1. شغل النظام على جهاز واحد (الخادم)")
    print(f"   2. اعرف عنوان IP للجهاز:")
    print(f"      cmd > ipconfig")
    print(f"   3. من الأجهزة الأخرى، افتح المتصفح:")
    print(f"      http://[IP_ADDRESS]:[PORT]")
    
    print(f"\n📦 التوزيع:")
    print(f"   • انسخ مجلد 'dist' كاملاً")
    print(f"   • ضعه في أي مكان على الحاسوب")
    print(f"   • لا يحتاج تثبيت Python أو مكتبات")
    print(f"   • يعمل مباشرة على أي Windows")
    
    print(f"\n🔧 المميزات:")
    print(f"   ✅ بدون نوافذ console سوداء")
    print(f"   ✅ تشغيل تلقائي للمتصفح")
    print(f"   ✅ اختيار منفذ تلقائي")
    print(f"   ✅ دعم عدة مستخدمين")
    print(f"   ✅ قاعدة بيانات محلية")
    print(f"   ✅ أزرار مصغرة في الجداول")

def show_technical_details():
    """عرض التفاصيل التقنية"""
    
    print(f"\n🔧 التفاصيل التقنية:")
    print("=" * 50)
    
    print(f"📦 PyInstaller:")
    print(f"   • تم استخدام PyInstaller لبناء الملف التنفيذي")
    print(f"   • جميع المكتبات مدمجة في ملف واحد")
    print(f"   • لا يحتاج Python مثبت على الجهاز المستهدف")
    
    print(f"\n🎯 الإعدادات:")
    print(f"   • console=False (بدون نافذة سوداء)")
    print(f"   • onefile=True (ملف واحد)")
    print(f"   • windowed=True (واجهة رسومية)")
    print(f"   • threaded=True (دعم عدة مستخدمين)")
    
    print(f"\n📁 الملفات المدمجة:")
    print(f"   • جميع قوالب HTML")
    print(f"   • جميع ملفات CSS و JavaScript")
    print(f"   • الصور والأيقونات")
    print(f"   • قاعدة البيانات (إذا كانت موجودة)")
    
    print(f"\n🌐 الشبكة:")
    print(f"   • host='0.0.0.0' (قبول اتصالات من أي IP)")
    print(f"   • port=auto (اختيار منفذ متاح تلقائياً)")
    print(f"   • threading=True (معالجة متوازية)")

if __name__ == "__main__":
    print("🎊 اختبار نظام إدارة الصيانة - الملف التنفيذي")
    print("🎊 Testing Maintenance Management System - Executable")
    print()
    
    try:
        # اختبار الملف التنفيذي
        success = test_executable()
        
        if success:
            # عرض تعليمات الاستخدام
            show_usage_instructions()
            
            # عرض التفاصيل التقنية
            show_technical_details()
            
            print(f"\n🎉 النتيجة النهائية:")
            print("=" * 50)
            print(f"✅ تم إنشاء الملف التنفيذي بنجاح!")
            print(f"✅ جميع المتطلبات مستوفاة:")
            print(f"   🔘 أزرار مصغرة في الجداول")
            print(f"   💻 متوافق مع Windows 7, 10, 11")
            print(f"   🏗️ يدعم 32-bit و 64-bit")
            print(f"   🚫 بدون خلفية سوداء")
            print(f"   👥 عدة مستخدمين في نفس الوقت")
            print(f"   📦 سهل التثبيت والتوزيع")
            
            print(f"\n🚀 جاهز للاستخدام!")
            print(f"📁 الملفات في مجلد: dist/")
            
        else:
            print(f"\n❌ فشل في اختبار الملف التنفيذي")
        
        input(f"\nاضغط Enter للخروج...")
        
    except KeyboardInterrupt:
        print(f"\n⏹️ تم إلغاء الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")
        input("اضغط Enter للخروج...")
