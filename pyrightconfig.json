{"venvPath": ".", "venv": ".venv", "typeCheckingMode": "off", "reportMissingImports": "none", "reportMissingTypeStubs": "none", "reportImportCycles": "none", "reportUnusedImport": "none", "reportUnusedClass": "none", "reportUnusedFunction": "none", "reportUnusedVariable": "none", "reportDuplicateImport": "none", "reportOptionalSubscript": "none", "reportOptionalMemberAccess": "none", "reportOptionalCall": "none", "reportOptionalIterable": "none", "reportOptionalContextManager": "none", "reportOptionalOperand": "none", "reportTypedDictNotRequiredAccess": "none", "reportPrivateImportUsage": "none", "reportConstantRedefinition": "none", "reportIncompatibleMethodOverride": "none", "reportIncompatibleVariableOverride": "none", "reportInconsistentConstructor": "none", "reportOverlappingOverloads": "none", "reportMissingSuperCall": "none", "reportUninitializedInstanceVariable": "none", "reportInvalidStringEscapeSequence": "none", "reportUnknownParameterType": "none", "reportUnknownArgumentType": "none", "reportUnknownLambdaType": "none", "reportUnknownVariableType": "none", "reportUnknownMemberType": "none", "reportMissingParameterType": "none", "reportMissingTypeArgument": "none", "reportInvalidTypeVarUse": "none", "reportCallInDefaultInitializer": "none", "reportUnnecessaryIsInstance": "none", "reportUnnecessaryCast": "none", "reportUnnecessaryComparison": "none", "reportAssertAlwaysTrue": "none", "reportSelfClsParameterName": "none", "reportImplicitStringConcatenation": "none", "reportUndefinedVariable": "error", "reportUnboundVariable": "error"}