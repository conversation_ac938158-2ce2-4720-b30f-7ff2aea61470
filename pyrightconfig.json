{"include": ["."], "exclude": ["**/node_modules", "**/__pycache__", "**/.*"], "venvPath": ".", "venv": ".venv", "pythonVersion": "3.11", "pythonPlatform": "Windows", "typeCheckingMode": "basic", "useLibraryCodeForTypes": true, "autoImportCompletions": true, "reportMissingImports": "warning", "reportMissingTypeStubs": false, "reportImportCycles": false, "reportUnusedImport": "warning", "reportUnusedClass": "warning", "reportUnusedFunction": "warning", "reportUnusedVariable": "warning", "reportDuplicateImport": "warning", "reportOptionalSubscript": "warning", "reportOptionalMemberAccess": "warning", "reportOptionalCall": "warning", "reportOptionalIterable": "warning", "reportOptionalContextManager": "warning", "reportOptionalOperand": "warning", "reportTypedDictNotRequiredAccess": "warning", "reportPrivateImportUsage": "warning", "reportConstantRedefinition": "warning", "reportIncompatibleMethodOverride": "error", "reportIncompatibleVariableOverride": "error", "reportInconsistentConstructor": "error", "reportOverlappingOverloads": "warning", "reportMissingSuperCall": "warning", "reportUninitializedInstanceVariable": "warning", "reportInvalidStringEscapeSequence": "warning", "reportUnknownParameterType": "warning", "reportUnknownArgumentType": "warning", "reportUnknownLambdaType": "warning", "reportUnknownVariableType": "warning", "reportUnknownMemberType": "warning", "reportMissingParameterType": "warning", "reportMissingTypeArgument": "warning", "reportInvalidTypeVarUse": "warning", "reportCallInDefaultInitializer": "warning", "reportUnnecessaryIsInstance": "warning", "reportUnnecessaryCast": "warning", "reportUnnecessaryComparison": "warning", "reportAssertAlwaysTrue": "warning", "reportSelfClsParameterName": "warning", "reportImplicitStringConcatenation": "warning", "reportUndefinedVariable": "error", "reportUnboundVariable": "error", "reportInvalidStubStatement": "error", "reportIncompleteStub": "error", "reportUnsupportedDunderAll": "error", "reportUnusedCoroutine": "error", "reportFunctionMemberAccess": "error"}