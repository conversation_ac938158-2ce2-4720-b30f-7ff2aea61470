#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

# اختبار route استيراد المواقع
url = 'http://127.0.0.1:5000/api/import/sites'

# بيانات اختبار
test_data = {
    'data': [
        {
            'NOM DU SITE': 'Site Test 1',
            'ADRESSE': '123 Rue Test, Casablanca',
            'TÉLÉPHONE': '+212 522 123456',
            'RESPONSABLE': '<PERSON>',
            'EMAIL': '<EMAIL>'
        },
        {
            'NOM DU SITE': 'Site Test 2',
            'ADRESSE': '456 Avenue Mohammed V, Rabat',
            'TÉLÉPHONE': '+212 537 654321',
            'RESPONSABLE': 'Fatima Benali',
            'EMAIL': '<EMAIL>'
        }
    ]
}

print("🔍 اختبار route استيراد المواقع...")
print(f"URL: {url}")
print(f"البيانات: {len(test_data['data'])} سجل")

try:
    # إرسال طلب POST مع JSON
    response = requests.post(
        url,
        json=test_data,
        headers={'Content-Type': 'application/json'},
        timeout=10
    )
    
    print(f"\n✅ الاستجابة:")
    print(f"Status Code: {response.status_code}")
    print(f"Headers: {dict(response.headers)}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"✅ نجح الاستيراد: {result}")
    else:
        print(f"❌ فشل الاستيراد:")
        print(f"Response: {response.text}")
        
except requests.exceptions.ConnectionError:
    print("❌ خطأ في الاتصال - تأكد من أن الخادم يعمل على المنفذ 5000")
except requests.exceptions.Timeout:
    print("❌ انتهت مهلة الاتصال")
except Exception as e:
    print(f"❌ خطأ غير متوقع: {e}")

print("\n🎯 انتهى الاختبار!")
