#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إعادة تعيين بيانات الشركة بالكامل
"""

import sqlite3
import os

def reset_company_data():
    """إعادة تعيين بيانات الشركة بالكامل"""
    print("🔄 إعادة تعيين بيانات الشركة...")
    print("=" * 50)
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()
        
        # عرض البيانات الحالية
        print("📊 البيانات الحالية:")
        cursor.execute("SELECT * FROM societe")
        rows = cursor.fetchall()
        for i, row in enumerate(rows):
            print(f"   الصف {i+1}: {row}")
        
        # حذف جميع البيانات من جدول الشركة
        print("\n🗑️ حذف جميع البيانات الحالية...")
        cursor.execute("DELETE FROM societe")
        deleted_rows = cursor.rowcount
        print(f"   تم حذف {deleted_rows} صف(وف)")
        
        # إدراج بيانات افتراضية جديدة ونظيفة
        print("\n📝 إدراج بيانات افتراضية جديدة...")
        cursor.execute('''
        INSERT INTO societe (nom, logo, telephone, responsable, email, adresse, if_fiscal, rc, patente, ice, cnss, pied_page)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            'شركتي للصيانة',  # اسم بسيط
            None,  # لا يوجد شعار
            '+212 5XX-XXXXXX',
            'المدير العام',
            '<EMAIL>',
            'العنوان، المدينة، المغرب',
            'IF12345',
            'RC12345',
            'PAT12345',
            'ICE12345',
            'CNSS12345',
            'شركتي للصيانة - 2024'
        ))
        
        # حفظ التغييرات
        conn.commit()
        print("✅ تم إدراج البيانات الجديدة")
        
        # عرض البيانات الجديدة
        print("\n📊 البيانات الجديدة:")
        cursor.execute("SELECT * FROM societe")
        rows = cursor.fetchall()
        for i, row in enumerate(rows):
            print(f"   الصف {i+1}: {row}")
        
        conn.close()
        
        # حذف جميع ملفات الشعار القديمة
        print("\n🗂️ تنظيف مجلد uploads...")
        uploads_dir = 'static/uploads'
        if os.path.exists(uploads_dir):
            files = os.listdir(uploads_dir)
            logo_files = [f for f in files if f.startswith('logo_')]
            
            if logo_files:
                for logo_file in logo_files:
                    file_path = os.path.join(uploads_dir, logo_file)
                    try:
                        os.remove(file_path)
                        print(f"   🗑️ تم حذف {logo_file}")
                    except Exception as e:
                        print(f"   ❌ خطأ في حذف {logo_file}: {e}")
            else:
                print("   📁 لا توجد ملفات شعار للحذف")
        
        print("\n✅ تم إعادة تعيين بيانات الشركة بنجاح!")
        print("🎯 يمكنك الآن:")
        print("   1. تحديث صفحة المتصفح")
        print("   2. تجربة تحميل شعار جديد")
        print("   3. تعديل معلومات الشركة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        return False

def show_current_data():
    """عرض البيانات الحالية فقط"""
    print("📊 البيانات الحالية في قاعدة البيانات:")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM societe")
        rows = cursor.fetchall()
        
        if rows:
            for i, row in enumerate(rows):
                print(f"\n📝 الشركة {i+1}:")
                for key in row.keys():
                    value = row[key]
                    if key == 'logo':
                        status = "موجود" if value else "غير موجود"
                        print(f"   {key}: {value} ({status})")
                    else:
                        print(f"   {key}: {value}")
        else:
            print("   لا توجد بيانات شركة")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    print("🔧 أداة إدارة بيانات الشركة")
    print("=" * 50)
    
    # عرض البيانات الحالية أولاً
    show_current_data()
    
    # السؤال عن إعادة التعيين
    print("\n❓ هل تريد إعادة تعيين بيانات الشركة؟")
    choice = input("اكتب 'نعم' أو 'yes' للمتابعة: ").lower().strip()
    
    if choice in ['نعم', 'yes', 'y']:
        if reset_company_data():
            print("\n🎉 تمت العملية بنجاح!")
        else:
            print("\n❌ فشلت العملية!")
    else:
        print("🚫 تم إلغاء العملية")
