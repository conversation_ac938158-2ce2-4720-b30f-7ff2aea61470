#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إضافة L'AFFICHAGE DYNAMIQUE إلى جميع قوائم Domaine
"""

import os
import re

def add_affichage_dynamique_to_templates():
    """إضافة L'AFFICHAGE DYNAMIQUE إلى جميع القوالب"""
    
    print("🔧 إضافة L'AFFICHAGE DYNAMIQUE إلى قوائم Domaine")
    print("=" * 50)
    
    # قائمة القوالب التي تحتاج تحديث
    templates_to_update = [
        'templates/ajouter_marche.html',
        'templates/modifier_marche.html',
        'templates/marches.html',
        'templates/details_marche.html',
        'templates/ajouter_intervention.html',
        'templates/interventions.html',
        'templates/ajouter_reclamation.html',
        'templates/reclamations.html',
        'templates/voir_reclamation.html'
    ]
    
    updated_files = 0
    
    for template_path in templates_to_update:
        if os.path.exists(template_path):
            print(f"\n📄 معالجة {template_path}...")
            
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 1. إضافة L'AFFICHAGE DYNAMIQUE إلى قوائم select
            # البحث عن قوائم select التي تحتوي على خيارات domaine
            select_patterns = [
                # نمط 1: قائمة select عادية
                (r'(<option value="SYSTEME TELEPHONIQUE"[^>]*>SYSTEME TELEPHONIQUE</option>)',
                 r'\1\n                                <option value="L\'AFFICHAGE DYNAMIQUE">L\'AFFICHAGE DYNAMIQUE</option>'),
                
                # نمط 2: قائمة select مع selected
                (r'(<option value="SYSTEME TELEPHONIQUE"[^>]*selected[^>]*>SYSTEME TELEPHONIQUE</option>)',
                 r'\1\n                                <option value="L\'AFFICHAGE DYNAMIQUE" {% if marche.domaine == "L\'AFFICHAGE DYNAMIQUE" %}selected{% endif %}>L\'AFFICHAGE DYNAMIQUE</option>'),
                
                # نمط 3: قائمة select في الفلاتر
                (r'(<option value="SYSTEME TELEPHONIQUE">SYSTEME TELEPHONIQUE</option>)',
                 r'\1\n                <option value="L\'AFFICHAGE DYNAMIQUE">L\'AFFICHAGE DYNAMIQUE</option>')
            ]
            
            for pattern, replacement in select_patterns:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content)
                    print(f"   ✅ تم إضافة خيار في قائمة select")
            
            # 2. إضافة L'AFFICHAGE DYNAMIQUE إلى عرض badges
            # البحث عن عرض badges في الجداول
            badge_patterns = [
                # نمط 1: في الجداول العادية
                (r"(\{% elif [^%]*domaine == 'SYSTEME TELEPHONIQUE' %\}[^%]*<span class=\"badge bg-success[^\"]*\"[^>]*>SYSTEME TELEPHONIQUE</span>)",
                 r'\1\n                      {% elif marche.domaine == "L\'AFFICHAGE DYNAMIQUE" %}\n                        <span class="badge bg-dark rounded-pill">AFFICHAGE DYN.</span>'),
                
                # نمط 2: في تفاصيل المارشيه
                (r"(\{% elif [^%]*domaine == 'SYSTEME TELEPHONIQUE' %\}[^%]*<span class=\"badge bg-success\"[^>]*>SYSTEME TELEPHONIQUE</span>)",
                 r'\1\n                                    {% elif marche.domaine == "L\'AFFICHAGE DYNAMIQUE" %}\n                                    <span class="badge bg-dark">L\'AFFICHAGE DYNAMIQUE</span>'),
                
                # نمط 3: في الريكلاماسيون
                (r"(\{% elif [^%]*domaine == 'SYSTEME TELEPHONIQUE' %\}[^%]*<span class=\"badge bg-success\"[^>]*>SYSTEME TELEPHONIQUE</span>)",
                 r'\1\n                {% elif reclamation.domaine == "L\'AFFICHAGE DYNAMIQUE" %}\n                <span class="badge bg-dark">L\'AFFICHAGE DYNAMIQUE</span>')
            ]
            
            for pattern, replacement in badge_patterns:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content)
                    print(f"   ✅ تم إضافة badge للعرض")
            
            # 3. إضافة L'AFFICHAGE DYNAMIQUE إلى JavaScript (إذا وجد)
            js_patterns = [
                # في switch statements
                (r"(case 'SYSTEME TELEPHONIQUE':[^}]*break;)",
                 r"\1\n                case 'L\\'AFFICHAGE DYNAMIQUE':\n                    tableId = 'affichage-dynamique-details';\n                    break;")
            ]
            
            for pattern, replacement in js_patterns:
                if re.search(pattern, content):
                    content = re.sub(pattern, replacement, content)
                    print(f"   ✅ تم إضافة JavaScript handling")
            
            # حفظ الملف إذا تم تعديله
            if content != original_content:
                with open(template_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"   💾 تم حفظ التحديثات")
                updated_files += 1
            else:
                print(f"   ℹ️ لا توجد تحديثات مطلوبة")
        else:
            print(f"   ❌ الملف غير موجود: {template_path}")
    
    return updated_files

def update_intervention_template():
    """تحديث خاص لقالب الإنترفنشن لإضافة جدول L'AFFICHAGE DYNAMIQUE"""
    
    print(f"\n🔧 تحديث خاص لقالب الإنترفنشن...")
    
    template_path = 'templates/ajouter_intervention.html'
    if not os.path.exists(template_path):
        print(f"   ❌ الملف غير موجود: {template_path}")
        return False
    
    with open(template_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # إضافة جدول L'AFFICHAGE DYNAMIQUE بعد جدول SYSTEME TELEPHONIQUE
    affichage_table = '''
          <!-- Tableau pour L'AFFICHAGE DYNAMIQUE -->
          <div id="affichage-dynamique-details" class="domain-table" style="display: none;">
            <div class="card mt-4">
              <div class="card-header bg-dark text-white">
                <h5 class="mb-0"><i class="fas fa-desktop me-2"></i>Détails L'AFFICHAGE DYNAMIQUE</h5>
              </div>
              <div class="card-body">
                <div class="table-responsive">
                  <table class="table table-bordered">
                    <thead class="table-dark">
                      <tr>
                        <th>Région</th>
                        <th>Nom du site</th>
                        <th>GPS</th>
                        <th>Situation</th>
                        <th>État du matériel</th>
                        <th>Type d'affichage</th>
                        <th>Résolution</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody id="affichage-dynamique-tbody">
                      <!-- Les lignes seront ajoutées dynamiquement -->
                    </tbody>
                  </table>
                </div>
                <button type="button" class="btn btn-dark btn-sm" onclick="addAffichageRow()">
                  <i class="fas fa-plus me-1"></i>Ajouter une ligne
                </button>
              </div>
            </div>
          </div>'''
    
    # البحث عن نهاية جدول SYSTEME TELEPHONIQUE وإضافة الجدول الجديد
    systeme_tel_end = r'(</div>\s*</div>\s*</div>\s*<!-- Fin Tableau pour SYSTEME TELEPHONIQUE -->)'
    if re.search(systeme_tel_end, content):
        content = re.sub(systeme_tel_end, r'\1' + affichage_table, content)
        print(f"   ✅ تم إضافة جدول L'AFFICHAGE DYNAMIQUE")
    else:
        # إذا لم نجد النمط المحدد، نضيف الجدول في نهاية القسم
        form_end = r'(</form>\s*</div>\s*</div>\s*</div>)'
        content = re.sub(form_end, affichage_table + r'\1', content)
        print(f"   ✅ تم إضافة جدول L'AFFICHAGE DYNAMIQUE في النهاية")
    
    # إضافة JavaScript function لإضافة صفوف
    js_function = '''
    // Function pour ajouter une ligne L'AFFICHAGE DYNAMIQUE
    function addAffichageRow() {
        const tbody = document.getElementById('affichage-dynamique-tbody');
        const rowCount = tbody.children.length;
        
        const row = document.createElement('tr');
        row.innerHTML = `
            <td><input type="text" class="form-control form-control-sm" name="affichage_region_${rowCount}" placeholder="Région"></td>
            <td><input type="text" class="form-control form-control-sm" name="affichage_site_${rowCount}" placeholder="Nom du site"></td>
            <td><input type="text" class="form-control form-control-sm" name="affichage_gps_${rowCount}" placeholder="GPS"></td>
            <td><input type="text" class="form-control form-control-sm" name="affichage_situation_${rowCount}" placeholder="Situation"></td>
            <td>
                <select class="form-select form-select-sm" name="affichage_etat_${rowCount}">
                    <option value="">État</option>
                    <option value="Bon">Bon</option>
                    <option value="Moyen">Moyen</option>
                    <option value="Mauvais">Mauvais</option>
                </select>
            </td>
            <td>
                <select class="form-select form-select-sm" name="affichage_type_${rowCount}">
                    <option value="">Type</option>
                    <option value="LED">LED</option>
                    <option value="LCD">LCD</option>
                    <option value="OLED">OLED</option>
                </select>
            </td>
            <td><input type="text" class="form-control form-control-sm" name="affichage_resolution_${rowCount}" placeholder="1920x1080"></td>
            <td>
                <button type="button" class="btn btn-danger btn-sm" onclick="this.closest('tr').remove()">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        
        tbody.appendChild(row);
    }'''
    
    # إضافة JavaScript في نهاية الملف
    if '</script>' in content:
        content = content.replace('</script>', js_function + '\n</script>')
        print(f"   ✅ تم إضافة JavaScript functions")
    
    # حفظ الملف
    with open(template_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    return True

def verify_updates():
    """التحقق من التحديثات"""
    
    print(f"\n🔍 التحقق من التحديثات...")
    
    templates_to_check = [
        'templates/ajouter_marche.html',
        'templates/modifier_marche.html',
        'templates/marches.html',
        'templates/details_marche.html'
    ]
    
    for template_path in templates_to_check:
        if os.path.exists(template_path):
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if "L'AFFICHAGE DYNAMIQUE" in content:
                print(f"   ✅ {template_path}: يحتوي على L'AFFICHAGE DYNAMIQUE")
            else:
                print(f"   ❌ {template_path}: لا يحتوي على L'AFFICHAGE DYNAMIQUE")
        else:
            print(f"   ❌ {template_path}: غير موجود")

if __name__ == "__main__":
    # إضافة L'AFFICHAGE DYNAMIQUE إلى القوالب
    updated_count = add_affichage_dynamique_to_templates()
    
    # تحديث خاص لقالب الإنترفنشن
    intervention_updated = update_intervention_template()
    
    # التحقق من التحديثات
    verify_updates()
    
    print(f"\n🎉 تم الانتهاء من التحديثات!")
    print("=" * 50)
    print(f"📊 الإحصائيات:")
    print(f"   • ملفات محدثة: {updated_count}")
    print(f"   • قالب الإنترفنشن: {'✅ محدث' if intervention_updated else '❌ لم يتم التحديث'}")
    print(f"\n💡 تم إضافة:")
    print(f"   • خيار 'L'AFFICHAGE DYNAMIQUE' في جميع قوائم Domaine")
    print(f"   • Badge عرض باللون الداكن")
    print(f"   • جدول خاص في قالب الإنترفنشن")
    print(f"   • JavaScript functions للتعامل مع الجدول الجديد")
