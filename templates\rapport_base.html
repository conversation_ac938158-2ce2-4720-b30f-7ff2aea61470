<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Rapport - Système de Gestion de Maintenance{% endblock %}</title>

    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Poppins', sans-serif;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background: #fff;
        }

        .rapport-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.15);
            min-height: 297mm;
            border-radius: 8px;
            position: relative;
        }

        .rapport-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #3498db, #2ecc71, #f39c12, #e74c3c);
            border-radius: 8px 8px 0 0;
        }

        .rapport-header {
            border-bottom: 3px solid #3498db;
            padding: 20px;
            margin-bottom: 30px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            page-break-inside: avoid;
        }

        .company-info {
            flex: 1;
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .company-logo {
            width: 150px;
            height: auto;
            max-height: 120px;
            object-fit: contain;
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            background: white;
            padding: 15px;
        }

        .company-text {
            flex: 1;
        }

        .company-name {
            font-size: 28px;
            font-weight: 700;
            color: #2c3e50;
            margin: 0 0 10px 0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .company-details {
            font-size: 12px;
            color: #555;
            line-height: 1.5;
            background: rgba(52, 152, 219, 0.05);
            padding: 12px;
            border-radius: 6px;
            border-left: 3px solid #3498db;
        }

        .company-details div {
            margin-bottom: 5px;
        }

        .company-details div:last-child {
            margin-bottom: 0;
        }

        .print-info {
            text-align: right;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            border: 1px solid #ecf0f1;
        }

        .print-info .fw-bold {
            color: #2c3e50;
            font-size: 14px;
            margin-bottom: 8px;
        }

        .print-info div:last-child {
            color: #7f8c8d;
            font-size: 13px;
            font-weight: 500;
        }

        .rapport-title {
            text-align: center;
            margin: 40px 0;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.08);
            border: 1px solid #ecf0f1;
            position: relative;
        }

        .rapport-title::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 0 0 10px 10px;
        }

        .rapport-title h1 {
            font-size: 32px;
            font-weight: 700;
            color: #2c3e50;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 2px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .rapport-subtitle {
            font-size: 18px;
            color: #7f8c8d;
            font-weight: 500;
            font-style: italic;
        }

        .rapport-meta {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 35px;
            border-left: 6px solid #3498db;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            position: relative;
        }

        .rapport-meta::after {
            content: '';
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #3498db, #2980b9);
            border-radius: 50%;
            opacity: 0.1;
        }

        .meta-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }

        .meta-row:last-child {
            margin-bottom: 0;
        }

        .meta-label {
            font-weight: 600;
            color: #2c3e50;
            min-width: 150px;
        }

        .meta-value {
            color: #34495e;
            flex: 1;
            text-align: right;
        }

        .rapport-content {
            margin-bottom: 40px;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #2c3e50;
            margin: 30px 0 15px 0;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
        }

        .table-custom {
            border: none;
            border-radius: 15px;
            overflow: hidden;
            margin-bottom: 30px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            background: white;
        }

        .table-custom thead th {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            font-weight: 600;
            padding: 18px 15px;
            border: none;
            font-size: 14px;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            text-shadow: 0 1px 2px rgba(0,0,0,0.2);
        }

        .table-custom thead th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        }

        .table-custom tbody td {
            padding: 12px 10px;
            border: 1px solid #ddd;
            vertical-align: top;
            font-size: 12px;
            transition: all 0.3s ease;
            word-wrap: break-word;
            max-width: 150px;
        }

        .table-custom tbody tr:nth-child(even) {
            background-color: #fafbfc;
        }

        .table-custom tbody tr:hover {
            background-color: #e8f4fd;
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.15);
        }

        .table-custom tbody tr:last-child td {
            border-bottom: none;
        }

        /* Styles pour les badges dans les tableaux */
        .table-custom .badge {
            font-size: 11px;
            padding: 6px 10px;
            border-radius: 20px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .rapport-footer {
            margin-top: 60px;
            padding: 30px 0;
            border-top: 3px solid #ecf0f1;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border-radius: 15px;
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            box-shadow: 0 -4px 15px rgba(0,0,0,0.05);
            position: relative;
        }

        .rapport-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 3px;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 10px 10px 0 0;
        }

        .signature-section {
            text-align: center;
            min-width: 220px;
            background: white;
            padding: 25px 20px;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.08);
            border: 1px solid #ecf0f1;
        }

        .signature-section .fw-bold {
            color: #2c3e50;
            font-size: 16px;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .signature-line {
            border-top: 2px solid #3498db;
            margin-top: 60px;
            padding-top: 8px;
            font-size: 12px;
            color: #7f8c8d;
            font-weight: 500;
        }

        .footer-center {
            text-align: center;
            flex: 1;
            padding: 0 30px;
        }

        .footer-center small {
            display: block;
            line-height: 1.6;
            color: #7f8c8d;
        }

        .badge-custom {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 11px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .badge-success {
            background: linear-gradient(135deg, #2ecc71 0%, #27ae60 100%);
            color: white;
        }

        .badge-warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
            color: white;
        }

        .badge-danger {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
            color: white;
        }

        .badge-info {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
        }

        /* Styles d'impression améliorés */
        @media print {
            body {
                margin: 0;
                padding: 0;
                background: white !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                font-size: 11px !important;
                line-height: 1.4 !important;
            }

            .rapport-container {
                box-shadow: none;
                margin: 0;
                padding: 10mm;
                max-width: none;
                width: 100%;
            }

            .no-print {
                display: none !important;
            }

            .page-break {
                page-break-before: always;
            }

            /* Amélioration des tableaux pour l'impression */
            .table-custom {
                break-inside: avoid;
                border: 2px solid #333 !important;
                font-size: 10px !important;
                margin-bottom: 15px !important;
            }

            .table-custom thead th {
                background: #f0f0f0 !important;
                color: #000 !important;
                border: 1px solid #333 !important;
                padding: 8px 6px !important;
                font-size: 10px !important;
                text-align: center !important;
                font-weight: bold !important;
            }

            .table-custom tbody td {
                border: 1px solid #666 !important;
                padding: 6px 4px !important;
                font-size: 9px !important;
                max-width: 100px !important;
                word-wrap: break-word !important;
                vertical-align: top !important;
            }

            .section-title {
                break-after: avoid;
                font-size: 14px !important;
                margin-top: 15px !important;
                margin-bottom: 8px !important;
            }

            /* Amélioration de l'en-tête pour l'impression */
            .rapport-header {
                padding: 10px !important;
                margin-bottom: 15px !important;
                border-bottom: 2px solid #333 !important;
            }

            .company-logo {
                max-width: 100px !important;
                max-height: 80px !important;
            }

            .company-name {
                font-size: 16px !important;
                font-weight: bold !important;
            }

            .company-details {
                font-size: 10px !important;
                line-height: 1.3 !important;
            }

            /* Amélioration du pied de page */
            .rapport-footer {
                margin-top: 20px !important;
                padding: 10px !important;
                border-top: 2px solid #333 !important;
                font-size: 9px !important;
            }

            .signature-section {
                padding: 10px !important;
                font-size: 10px !important;
            }

            /* Titres pour l'impression */
            h1 {
                font-size: 18px !important;
                margin: 10px 0 !important;
                text-align: center !important;
            }

            h2 {
                font-size: 14px !important;
                margin: 8px 0 !important;
            }

            h3 {
                font-size: 12px !important;
                margin: 6px 0 !important;
            }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .rapport-container {
                padding: 15px;
                margin: 10px;
            }

            .rapport-header {
                flex-direction: column;
                text-align: center;
            }

            .company-info {
                margin-bottom: 20px;
            }

            .meta-row {
                flex-direction: column;
            }

            .meta-value {
                text-align: left;
                margin-top: 5px;
            }

            .rapport-footer {
                flex-direction: column;
                gap: 30px;
            }
        }
    </style>

    {% block extra_css %}{% endblock %}
</head>
<body>
    <div class="rapport-container">
        <!-- Boutons d'action (masqués à l'impression) -->
        <div class="no-print mb-4">
            <div class="header-right">
                <div class="btn-group" role="group">
                    <a href="javascript:history.back()" class="btn btn-primary btn-sm">
                        <i class="fas fa-arrow-left"></i> Retour
                    </a>
                    <button onclick="window.print()" class="btn btn-light btn-sm">
                        <i class="fas fa-print"></i> Imprimer
                    </button>
                    <button onclick="exportToPDF()" class="btn btn-success btn-sm">
                        <i class="fas fa-file-pdf"></i> PDF
                    </button>
                    <button onclick="location.reload()" class="btn btn-info btn-sm">
                        <i class="fas fa-sync-alt"></i> Actualiser
                    </button>
                </div>
            </div>
        </div>

        <!-- En-tête du rapport -->
        <div class="rapport-header">
            <div class="company-info">
                {% if company_info and company_info.logo %}
                    <img src="{{ url_for('static', filename='uploads/' + company_info.logo) }}" alt="Logo de la société" class="company-logo" onerror="this.src='{{ url_for('static', filename='images/logo.png') }}'">
                {% else %}
                    <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Logo par défaut" class="company-logo">
                {% endif %}
                <div class="company-text">
                    <div class="company-name">{{ 'Votre Société' if company_info and company_info.nom == 'MAXAFFAIRE' else (company_info.nom if company_info and company_info.nom else 'Votre Société') }}</div>
                    <div class="company-details">
                        {% if company_info %}
                            {% if company_info.adresse %}
                                <div><i class="fas fa-map-marker-alt"></i> {{ company_info.adresse }}</div>
                            {% endif %}
                            {% if company_info.telephone or company_info.email %}
                                <div>
                                    {% if company_info.telephone %}<i class="fas fa-phone"></i> {{ company_info.telephone }}{% endif %}
                                    {% if company_info.telephone and company_info.email %} • {% endif %}
                                    {% if company_info.email %}<i class="fas fa-envelope"></i> {{ company_info.email }}{% endif %}
                                </div>
                            {% endif %}
                            {% if company_info.if_fiscal or company_info.rc or company_info.ice %}
                                <div class="mt-2">
                                    {% if company_info.if_fiscal %}<span class="badge bg-primary me-1">IF: {{ company_info.if_fiscal }}</span>{% endif %}
                                    {% if company_info.rc %}<span class="badge bg-success me-1">RC: {{ company_info.rc }}</span>{% endif %}
                                    {% if company_info.ice %}<span class="badge bg-info me-1">ICE: {{ company_info.ice }}</span>{% endif %}
                                </div>
                            {% endif %}
                        {% else %}
                            <div class="text-muted">Informations de la société non configurées</div>
                        {% endif %}
                    </div>
                </div>
            </div>
            <div class="print-info">
                <div class="fw-bold"><i class="fas fa-calendar-alt"></i> Date d'impression</div>
                <div id="print-date">{{ moment().format('DD/MM/YYYY HH:mm') if moment else '' }}</div>
                <div class="mt-3">
                    <div class="fw-bold"><i class="fas fa-user"></i> Généré par</div>
                    <div>{{ session.get('nom_complet', 'Système') }}</div>
                </div>
            </div>
        </div>

        <!-- Titre du rapport -->
        <div class="rapport-title">
            <h1>{% block rapport_title %}Rapport{% endblock %}</h1>
            <div class="rapport-subtitle">{% block rapport_subtitle %}{% endblock %}</div>
        </div>

        <!-- Métadonnées du rapport -->
        {% block rapport_meta %}{% endblock %}

        <!-- Contenu principal du rapport -->
        <div class="rapport-content">
            {% block rapport_content %}{% endblock %}
        </div>

        <!-- Pied de page du rapport -->
        <div class="rapport-footer">
            <div class="signature-section">
                <div class="fw-bold"><i class="fas fa-user-tie"></i> Responsable</div>
                <div class="signature-line">Signature et cachet</div>
            </div>
            <div class="footer-center">
                <small class="text-muted">
                    <i class="fas fa-cogs"></i> Généré automatiquement par le Système de Gestion de Maintenance<br>
                    <i class="fas fa-calendar"></i> {{ moment().format('DD/MM/YYYY à HH:mm') if moment else '' }}<br>
                    {% if company_info and company_info.pied_page %}
                        <i class="fas fa-info-circle"></i> {{ company_info.pied_page }}
                    {% endif %}
                </small>
            </div>
            <div class="signature-section">
                <div class="fw-bold"><i class="fas fa-handshake"></i> Client</div>
                <div class="signature-line">Signature et cachet</div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function exportToPDF() {
            // Fonction pour exporter en PDF (peut être implémentée avec jsPDF ou autre)
            alert('Fonctionnalité d\'export PDF à implémenter');
        }

        // Ajouter la date actuelle si pas définie
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const dateStr = now.toLocaleDateString('fr-FR') + ' ' + now.toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'});

            // Mettre à jour la date d'impression
            const printDateElement = document.getElementById('print-date');
            if (printDateElement && !printDateElement.textContent.trim()) {
                printDateElement.textContent = dateStr;
            }

            // Mettre à jour tous les éléments avec data-current-date
            const dateElements = document.querySelectorAll('[data-current-date]');
            dateElements.forEach(el => {
                if (!el.textContent.trim()) {
                    el.textContent = dateStr;
                }
            });

            // Vérifier si le logo existe et le remplacer par défaut si nécessaire
            const logoImg = document.querySelector('.company-logo');
            if (logoImg) {
                logoImg.onerror = function() {
                    this.src = "{{ url_for('static', filename='images/logo.png') }}";
                    this.alt = "Logo par défaut";
                };
            }
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
