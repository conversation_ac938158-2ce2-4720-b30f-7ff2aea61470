# دليل حل مشاكل الشبكة
# Network Troubleshooting Guide

---

## 🚨 **المشكلة: لا يمكن الوصول للبرنامج من حاسوب آخر**

### 🔍 **التشخيص السريع:**

#### **1. فحص إعدادات البرنامج:**
```bash
# تحقق من ملف config.ini
[Settings]
InstallationType=1    # يجب أن يكون 1 أو 2 (شبكي)
MultiUser=True        # يجب أن يكون True
ServerMode=True       # يجب أن يكون True

[Network]
Host=0.0.0.0         # يجب أن يكون 0.0.0.0 (وليس 127.0.0.1)
AllowExternalAccess=True  # يجب أن يكون True
```

#### **2. فحص المنفذ:**
- **المنفذ الافتراضي**: 5000
- **فحص التوفر**: يجب أن يكون المنفذ غير مستخدم
- **جدار الحماية**: يجب السماح بالمنفذ

#### **3. فحص الشبكة:**
- **نفس الشبكة**: جميع الأجهزة متصلة بنفس الراوتر/الشبكة
- **عنوان IP**: معرفة عنوان IP للجهاز المضيف

---

## 🛠️ **الحلول السريعة:**

### **الحل الأول: استخدام ملف التشغيل الشبكي**
```bash
# شغل الملف التالي:
تشغيل_شبكي.bat
```
**هذا الملف سيقوم بـ:**
- ✅ إنشاء إعدادات شبكية صحيحة
- ✅ تكوين جدار الحماية تلقائياً
- ✅ عرض عنوان IP للوصول
- ✅ تشغيل النظام في الوضع الشبكي

### **الحل الثاني: استخدام أداة التكوين**
```bash
# شغل أداة التكوين:
python network_config.py
```
**الأداة ستساعدك في:**
- 🔧 اختيار نوع التكوين المناسب
- 🌐 تحديد المنفذ المطلوب
- 🔥 تكوين جدار الحماية
- 🧪 اختبار الاتصال

### **الحل الثالث: التكوين اليدوي**

#### **خطوة 1: تعديل ملف config.ini**
```ini
[Settings]
InstallationType=1
Port=5000
MultiUser=True
ServerMode=True
Version=1.0.0

[Network]
Host=0.0.0.0
AllowExternalAccess=True

[Database]
Path=maintenance.db
BackupEnabled=True
BackupInterval=24
```

#### **خطوة 2: تكوين جدار الحماية**
```cmd
# شغل Command Prompt كمدير وأدخل:
netsh advfirewall firewall add rule name="Maintenance System" dir=in action=allow protocol=TCP localport=5000
```

#### **خطوة 3: معرفة عنوان IP**
```cmd
# في Command Prompt:
ipconfig
# ابحث عن IPv4 Address
```

#### **خطوة 4: تشغيل البرنامج**
```bash
# شغل الملف التنفيذي:
Maintenance_Management_System.exe
```

---

## 🔍 **استكشاف الأخطاء المتقدم:**

### **مشكلة 1: جدار الحماية يحجب الاتصال**

#### **الأعراض:**
- البرنامج يعمل على الجهاز المضيف
- لا يمكن الوصول من أجهزة أخرى
- رسالة خطأ "Connection refused" أو "Timeout"

#### **الحل:**
```cmd
# تعطيل جدار الحماية مؤقتاً للاختبار:
netsh advfirewall set allprofiles state off

# إذا عمل، أعد تشغيل جدار الحماية وأضف القاعدة:
netsh advfirewall set allprofiles state on
netsh advfirewall firewall add rule name="Maintenance System" dir=in action=allow protocol=TCP localport=5000
```

### **مشكلة 2: المنفذ مستخدم**

#### **الأعراض:**
- رسالة خطأ "Port already in use"
- البرنامج لا يبدأ

#### **الحل:**
```cmd
# فحص المنافذ المستخدمة:
netstat -an | findstr :5000

# إذا كان مستخدم، غير المنفذ في config.ini:
Port=5001
```

### **مشكلة 3: مشاكل الشبكة**

#### **الأعراض:**
- الأجهزة لا ترى بعضها البعض
- عدم وجود اتصال شبكي

#### **الحل:**
```cmd
# اختبار الاتصال بين الأجهزة:
ping [IP_ADDRESS]

# فحص إعدادات الشبكة:
ipconfig /all

# تجديد عنوان IP:
ipconfig /release
ipconfig /renew
```

---

## 📱 **الوصول من أجهزة مختلفة:**

### **من حاسوب آخر:**
```
http://[IP_ADDRESS]:5000
مثال: http://*************:5000
```

### **من هاتف محمول:**
```
1. تأكد من اتصال الهاتف بنفس الشبكة (WiFi)
2. افتح المتصفح
3. اذهب إلى: http://[IP_ADDRESS]:5000
```

### **من تابلت:**
```
نفس خطوات الهاتف المحمول
```

---

## 🔧 **أدوات مساعدة:**

### **معرفة عنوان IP:**
```cmd
# Windows:
ipconfig

# أو استخدم:
ipconfig | findstr IPv4
```

### **فحص المنفذ:**
```cmd
# فحص إذا كان المنفذ مفتوح:
telnet [IP_ADDRESS] 5000

# أو استخدم:
netstat -an | findstr :5000
```

### **اختبار الاتصال:**
```cmd
# اختبار ping:
ping [IP_ADDRESS]

# اختبار المنفذ من جهاز آخر:
telnet [IP_ADDRESS] 5000
```

---

## 📋 **قائمة فحص سريعة:**

### **على الجهاز المضيف:**
- [ ] ملف config.ini يحتوي على Host=0.0.0.0
- [ ] MultiUser=True و ServerMode=True
- [ ] جدار الحماية يسمح بالمنفذ 5000
- [ ] البرنامج يعمل بدون أخطاء
- [ ] المنفذ 5000 غير مستخدم من برامج أخرى

### **على الأجهزة الأخرى:**
- [ ] متصلة بنفس الشبكة
- [ ] يمكن ping الجهاز المضيف
- [ ] استخدام عنوان IP الصحيح
- [ ] المتصفح يدعم JavaScript

---

## 🆘 **إذا لم تنجح الحلول:**

### **خطوات إضافية:**
1. **أعد تشغيل الراوتر**
2. **أعد تشغيل جميع الأجهزة**
3. **تأكد من عدم وجود VPN نشط**
4. **جرب منفذ مختلف (مثل 8080)**
5. **تأكد من عدم وجود برامج أمان تحجب الاتصال**

### **للدعم الفني:**
```
📧 قم بإرسال المعلومات التالية:
- نوع نظام التشغيل
- رسائل الخطأ (إن وجدت)
- محتوى ملف config.ini
- نتيجة ipconfig
- نتيجة netstat -an | findstr :5000
```

---

## ✅ **التحقق من نجاح الحل:**

### **علامات النجاح:**
- ✅ يمكن فتح http://[IP]:5000 من أجهزة أخرى
- ✅ صفحة تسجيل الدخول تظهر بشكل صحيح
- ✅ يمكن تسجيل الدخول باستخدام admin/admin123
- ✅ جميع الوظائف تعمل من الأجهزة الأخرى

### **اختبار نهائي:**
```
1. شغل البرنامج على الجهاز المضيف
2. من جهاز آخر، اذهب إلى http://[IP]:5000
3. سجل الدخول باستخدام admin/admin123
4. جرب إضافة بيانات جديدة
5. تأكد من حفظ البيانات بشكل صحيح
```

**🎉 إذا نجحت جميع الخطوات، فالنظام يعمل بشكل صحيح في الوضع الشبكي!**
