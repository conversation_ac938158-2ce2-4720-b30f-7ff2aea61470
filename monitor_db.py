#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقبة قاعدة البيانات لرؤية تغييرات الشعار في الوقت الفعلي
"""

import sqlite3
import time
import os

def monitor_logo_changes():
    """مراقبة تغييرات الشعار في قاعدة البيانات"""
    print("👁️ مراقبة تغييرات الشعار...")
    print("اضغط Ctrl+C للتوقف")
    print("=" * 50)
    
    last_logo = None
    
    try:
        while True:
            try:
                conn = sqlite3.connect('maintenance.db')
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cursor.execute("SELECT id, nom, logo FROM societe LIMIT 1")
                row = cursor.fetchone()
                
                if row:
                    current_logo = row['logo']
                    
                    if current_logo != last_logo:
                        timestamp = time.strftime("%H:%M:%S")
                        print(f"[{timestamp}] 🔄 تغيير في الشعار:")
                        print(f"   من: {last_logo if last_logo else 'لا يوجد'}")
                        print(f"   إلى: {current_logo if current_logo else 'لا يوجد'}")
                        
                        # التحقق من وجود الملف
                        if current_logo:
                            file_path = os.path.join('static/uploads', current_logo)
                            if os.path.exists(file_path):
                                file_size = os.path.getsize(file_path)
                                print(f"   📁 الملف موجود: {file_size} bytes")
                            else:
                                print(f"   ❌ الملف غير موجود: {file_path}")
                        
                        last_logo = current_logo
                        print("-" * 30)
                
                conn.close()
                
            except Exception as e:
                print(f"❌ خطأ في قراءة قاعدة البيانات: {e}")
            
            time.sleep(2)  # فحص كل ثانيتين
            
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف المراقبة")

if __name__ == "__main__":
    monitor_logo_changes()
