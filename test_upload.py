#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour vérifier le fonctionnement du téléchargement de logo
"""

import os
import sys
import sqlite3
from werkzeug.utils import secure_filename

def test_upload_functionality():
    """Tester la fonctionnalité de téléchargement"""
    print("🧪 Test de la fonctionnalité de téléchargement")
    print("=" * 50)
    
    # Vérifier l'existence des dossiers
    upload_folder = 'static/uploads'
    images_folder = 'static/images'
    
    print(f"📁 Vérification du dossier uploads: {upload_folder}")
    if os.path.exists(upload_folder):
        print(f"✅ Dossier {upload_folder} existe")
        print(f"   Permissions: {oct(os.stat(upload_folder).st_mode)[-3:]}")
    else:
        print(f"❌ Dossier {upload_folder} n'existe pas")
        os.makedirs(upload_folder, exist_ok=True)
        print(f"✅ Dossier {upload_folder} créé")
    
    print(f"📁 Vérification du dossier images: {images_folder}")
    if os.path.exists(images_folder):
        print(f"✅ Dossier {images_folder} existe")
    else:
        print(f"❌ Dossier {images_folder} n'existe pas")
        os.makedirs(images_folder, exist_ok=True)
        print(f"✅ Dossier {images_folder} créé")
    
    # Vérifier la base de données
    print(f"\n💾 Vérification de la base de données")
    if os.path.exists('maintenance.db'):
        print("✅ Base de données existe")
        
        # Vérifier la table société
        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()
        
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='societe'")
            if cursor.fetchone():
                print("✅ Table société existe")
                
                # Vérifier les colonnes
                cursor.execute("PRAGMA table_info(societe)")
                columns = cursor.fetchall()
                column_names = [col[1] for col in columns]
                print(f"   Colonnes: {column_names}")
                
                if 'logo' in column_names:
                    print("✅ Colonne logo existe")
                else:
                    print("❌ Colonne logo manquante")
                
                # Vérifier les données existantes
                cursor.execute("SELECT * FROM societe LIMIT 1")
                societe = cursor.fetchone()
                if societe:
                    print("✅ Données société existent")
                    print(f"   Logo actuel: {societe[2] if len(societe) > 2 else 'Aucun'}")
                else:
                    print("⚠️ Aucune donnée société")
            else:
                print("❌ Table société n'existe pas")
        except Exception as e:
            print(f"❌ Erreur lors de la vérification de la base de données: {e}")
        finally:
            conn.close()
    else:
        print("❌ Base de données n'existe pas")
    
    # Tester la fonction secure_filename
    print(f"\n🔒 Test de secure_filename")
    test_filenames = [
        "logo.png",
        "mon logo.jpg",
        "logo-société.gif",
        "../../../etc/passwd",
        "logo with spaces.png"
    ]
    
    for filename in test_filenames:
        secure_name = secure_filename(filename)
        print(f"   '{filename}' -> '{secure_name}'")
    
    # Vérifier les extensions autorisées
    print(f"\n📄 Test des extensions autorisées")
    allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg'}
    test_files = [
        "logo.png",
        "logo.jpg", 
        "logo.jpeg",
        "logo.gif",
        "logo.bmp",
        "logo.webp",
        "logo.svg",
        "logo.txt",
        "logo.exe"
    ]
    
    for filename in test_files:
        ext = filename.rsplit('.', 1)[1].lower() if '.' in filename else ''
        is_allowed = ext in allowed_extensions
        status = "✅" if is_allowed else "❌"
        print(f"   {status} {filename} (extension: {ext})")
    
    print(f"\n🎯 Résumé du test")
    print("=" * 50)
    print("✅ Test terminé. Vérifiez les résultats ci-dessus.")
    print("📝 Si des erreurs sont détectées, elles doivent être corrigées.")

if __name__ == "__main__":
    test_upload_functionality()
