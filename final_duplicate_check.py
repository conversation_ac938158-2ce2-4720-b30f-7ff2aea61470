#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص نهائي للتأكد من عدم وجود تكرارات في L'AFFICHAGE DYNAMIQUE
"""

import os
import re

def final_duplicate_check():
    """فحص نهائي شامل للتكرارات"""
    
    print("🔍 فحص نهائي للتكرارات في L'AFFICHAGE DYNAMIQUE")
    print("=" * 60)
    
    # قائمة جميع القوالب
    templates_to_check = [
        'templates/ajouter_marche.html',
        'templates/modifier_marche.html',
        'templates/marches.html',
        'templates/details_marche.html',
        'templates/ajouter_intervention.html',
        'templates/interventions.html',
        'templates/ajouter_reclamation.html',
        'templates/reclamations.html',
        'templates/voir_reclamation.html'
    ]
    
    total_clean = 0
    total_files = len(templates_to_check)
    
    for template_path in templates_to_check:
        print(f"\n📄 فحص {template_path}:")
        
        if not os.path.exists(template_path):
            print(f"   ❌ الملف غير موجود")
            continue
        
        with open(template_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # فحص خيارات select
        option_pattern = r'<option[^>]*value="L\'AFFICHAGE DYNAMIQUE"[^>]*>L\'AFFICHAGE DYNAMIQUE</option>'
        option_matches = re.findall(option_pattern, content)
        
        # فحص خيارات select مع اقتباسات عادية
        option_pattern2 = r'<option[^>]*value="L\'AFFICHAGE DYNAMIQUE"[^>]*>L\'AFFICHAGE DYNAMIQUE</option>'
        option_matches2 = re.findall(option_pattern2, content)
        
        # فحص badges
        badge_pattern = r'<span[^>]*badge[^>]*>.*L\'AFFICHAGE DYNAMIQUE.*</span>'
        badge_matches = re.findall(badge_pattern, content)
        
        # إجمالي المطابقات
        total_matches = len(option_matches) + len(option_matches2) + len(badge_matches)
        
        print(f"   📊 خيارات select: {len(option_matches) + len(option_matches2)}")
        print(f"   🎨 badges: {len(badge_matches)}")
        print(f"   📈 إجمالي: {total_matches}")
        
        # فحص التكرارات المحددة
        duplicate_found = False
        
        # البحث عن تكرارات متتالية
        consecutive_pattern = r'(<option[^>]*value="L\'AFFICHAGE DYNAMIQUE"[^>]*>L\'AFFICHAGE DYNAMIQUE</option>)\s*\n[^<]*<option[^>]*value="L\'AFFICHAGE DYNAMIQUE"[^>]*>L\'AFFICHAGE DYNAMIQUE</option>'
        consecutive_matches = re.findall(consecutive_pattern, content)
        
        if consecutive_matches:
            print(f"   ⚠️ تكرارات متتالية: {len(consecutive_matches)}")
            duplicate_found = True
            
            # عرض السياق
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if 'L\'AFFICHAGE DYNAMIQUE' in line and '<option' in line:
                    print(f"      السطر {i+1}: {line.strip()}")
        
        # فحص تكرارات في نفس القسم
        if template_path in ['templates/ajouter_marche.html', 'templates/modifier_marche.html', 'templates/ajouter_intervention.html', 'templates/ajouter_reclamation.html']:
            expected_options = 1
        elif template_path in ['templates/marches.html', 'templates/interventions.html', 'templates/reclamations.html']:
            expected_options = 1  # في الفلاتر
        else:
            expected_options = 0  # في صفحات العرض فقط badges
        
        actual_options = len(option_matches) + len(option_matches2)
        
        if actual_options > expected_options:
            print(f"   ⚠️ خيارات زائدة: متوقع {expected_options}، موجود {actual_options}")
            duplicate_found = True
        
        # النتيجة النهائية للملف
        if not duplicate_found and actual_options <= expected_options:
            print(f"   ✅ نظيف - لا توجد تكرارات")
            total_clean += 1
        else:
            print(f"   ❌ يحتاج إصلاح")
    
    # الإحصائيات النهائية
    print(f"\n📊 الإحصائيات النهائية:")
    print(f"   📁 إجمالي الملفات: {total_files}")
    print(f"   ✅ ملفات نظيفة: {total_clean}")
    print(f"   ❌ ملفات تحتاج إصلاح: {total_files - total_clean}")
    print(f"   📈 نسبة النظافة: {(total_clean/total_files)*100:.1f}%")
    
    # توصيات
    if total_clean == total_files:
        print(f"\n🎉 ممتاز! جميع الملفات نظيفة")
        print(f"✅ لا توجد تكرارات في L'AFFICHAGE DYNAMIQUE")
        print(f"🎯 يمكن استخدام النظام بأمان")
    else:
        print(f"\n⚠️ بعض الملفات تحتاج إصلاح")
        print(f"🔧 راجع الملفات المذكورة أعلاه")
    
    return total_clean == total_files

def show_current_status():
    """عرض الحالة الحالية لكل قالب"""
    
    print(f"\n📋 الحالة الحالية لكل قالب:")
    print("=" * 60)
    
    templates_info = {
        'templates/ajouter_marche.html': 'نموذج إضافة مارشيه',
        'templates/modifier_marche.html': 'نموذج تعديل مارشيه',
        'templates/marches.html': 'صفحة عرض المارشيه + فلتر',
        'templates/details_marche.html': 'صفحة تفاصيل المارشيه',
        'templates/ajouter_intervention.html': 'نموذج إضافة إنترفنشن',
        'templates/interventions.html': 'صفحة عرض الإنترفنشن + فلتر',
        'templates/ajouter_reclamation.html': 'نموذج إضافة ريكلاماسيون',
        'templates/reclamations.html': 'صفحة عرض الريكلاماسيون + فلتر',
        'templates/voir_reclamation.html': 'صفحة عرض ريكلاماسيون واحدة'
    }
    
    for template_path, description in templates_info.items():
        if os.path.exists(template_path):
            with open(template_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # عد المطابقات
            affichage_count = content.count("L'AFFICHAGE DYNAMIQUE")
            option_count = content.count('<option') and content.count("L'AFFICHAGE DYNAMIQUE")
            badge_count = content.count('badge') and content.count("L'AFFICHAGE DYNAMIQUE")
            
            print(f"📄 {description}:")
            print(f"   📁 {template_path}")
            print(f"   📊 إجمالي مرات الظهور: {affichage_count}")
            
            if 'ajouter' in template_path or 'modifier' in template_path:
                print(f"   🎯 متوقع: 1 خيار select")
            elif template_path in ['templates/marches.html', 'templates/interventions.html', 'templates/reclamations.html']:
                print(f"   🎯 متوقع: 1 خيار فلتر + badges")
            else:
                print(f"   🎯 متوقع: badges فقط")
            
            print()

if __name__ == "__main__":
    # فحص نهائي للتكرارات
    is_clean = final_duplicate_check()
    
    # عرض الحالة الحالية
    show_current_status()
    
    print(f"\n🎯 النتيجة النهائية:")
    print("=" * 60)
    
    if is_clean:
        print(f"✅ جميع التكرارات تم إزالتها بنجاح!")
        print(f"🎉 L'AFFICHAGE DYNAMIQUE متاح الآن في جميع القوائم بدون تكرار")
        print(f"🚀 النظام جاهز للاستخدام")
    else:
        print(f"⚠️ لا تزال هناك بعض التكرارات")
        print(f"🔧 يرجى مراجعة الملفات المذكورة أعلاه")
    
    print(f"\n💡 للاستخدام:")
    print(f"   1. اذهب إلى أي نموذج (مارشيه، إنترفنشن، ريكلاماسيون)")
    print(f"   2. اختر 'L'AFFICHAGE DYNAMIQUE' من قائمة Domaine")
    print(f"   3. سيظهر badge داكن اللون في العرض")
    print(f"   4. في الإنترفنشن، ستجد جدول خاص للتفاصيل")
