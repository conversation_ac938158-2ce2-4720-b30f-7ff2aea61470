# 🏢 تكامل معلومات الشركة - نظام إدارة الصيانة

## 📋 نظرة عامة

تم ربط شعار الشركة ومعلوماتها من صفحة "Informations de la Société" مع جميع الأماكن المخصصة لها في التطبيق. الآن جميع القوالب والتقارير تستخدم معلومات الشركة المسجلة تلقائياً.

## ✨ الميزات المنجزة

### 🔄 ربط تلقائي للبيانات
- **Context Processor**: تم إضافة معالج سياق يجعل معلومات الشركة متاحة في جميع القوالب
- **دالة مركزية**: `get_company_info()` لجلب بيانات الشركة من قاعدة البيانات
- **تحديث تلقائي**: أي تغيير في معلومات الشركة يظهر فوراً في جميع أنحاء التطبيق

### 🎨 الأماكن المحدثة

#### 1. القالب الأساسي (base.html)
- **الشعار في الشريط الجانبي**: يظهر شعار الشركة المرفوع
- **اسم الشركة**: يظهر في عنوان الشريط الجانبي
- **عنوان الصفحة**: يتضمن اسم الشركة في عنوان المتصفح

#### 2. صفحة تسجيل الدخول (login.html)
- **الشعار الرئيسي**: شعار الشركة في وسط الصفحة
- **اسم الشركة**: عنوان رئيسي بدلاً من النص العام
- **وصف الشركة**: تذييل الصفحة من معلومات الشركة

#### 3. لوحة التحكم (dashboard.html)
- **العنوان الرئيسي**: يتضمن اسم الشركة
- **الوصف**: يظهر اسم الشركة مع وصف النظام

#### 4. قوالب التقارير
- **قالب التقارير الأساسي (rapport_base.html)**:
  - شعار الشركة في رأس التقرير
  - اسم الشركة وتفاصيل الاتصال
  - معلومات الشركة الضريبية (IF, RC, ICE)
  - تذييل الصفحة في أسفل التقرير

- **تقرير المارشيه (rapport_marche.html)**:
  - عنوان الصفحة يتضمن اسم الشركة

- **تقرير التدخلات (rapport_intervention.html)**:
  - عنوان الصفحة يتضمن اسم الشركة

- **تقرير الشكاوى (rapport_reclamation.html)**:
  - عنوان الصفحة يتضمن اسم الشركة

## 🔧 التحسينات التقنية

### Context Processor
```python
@app.context_processor
def inject_company_info():
    """Injecter les informations de la société dans tous les templates"""
    return dict(company_info=get_company_info())
```

### دالة جلب البيانات
```python
def get_company_info():
    """Récupérer les informations de la société"""
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM societe LIMIT 1")
        company = cursor.fetchone()
        conn.close()
        
        return company
    except Exception as e:
        print(f"Erreur lors de la récupération des informations de la société: {e}")
        return None
```

## 📱 استخدام البيانات في القوالب

### الشعار مع Fallback
```html
{% if company_info and company_info.logo %}
    <img src="{{ url_for('static', filename='uploads/' + company_info.logo) }}" 
         alt="Logo {{ company_info.nom }}" 
         onerror="this.src='{{ url_for('static', filename='images/logo.png') }}'">
{% else %}
    <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Logo">
{% endif %}
```

### اسم الشركة مع Fallback
```html
<h3>{{ company_info.nom if company_info and company_info.nom else 'Gestion de Maintenance' }}</h3>
```

### معلومات الاتصال
```html
{% if company_info %}
    {% if company_info.adresse %}
        <div><i class="fas fa-map-marker-alt"></i> {{ company_info.adresse }}</div>
    {% endif %}
    {% if company_info.telephone %}
        <div><i class="fas fa-phone"></i> {{ company_info.telephone }}</div>
    {% endif %}
    {% if company_info.email %}
        <div><i class="fas fa-envelope"></i> {{ company_info.email }}</div>
    {% endif %}
{% endif %}
```

## 🗂️ الملفات المحدثة

### ملفات Python
- `app.py` - إضافة دالة `get_company_info()` و context processor

### قوالب HTML
- `templates/base.html` - الشريط الجانبي وعنوان الصفحة
- `templates/login.html` - صفحة تسجيل الدخول
- `templates/dashboard.html` - لوحة التحكم
- `templates/rapport_base.html` - قالب التقارير الأساسي
- `templates/rapport_marche.html` - تقرير المارشيه
- `templates/rapport_intervention.html` - تقرير التدخلات
- `templates/rapport_reclamation.html` - تقرير الشكاوى

### ملفات الاختبار
- `test_company_integration.py` - اختبار شامل للتكامل

## 🎯 البيانات المدعومة

### معلومات أساسية
- **الاسم** (`nom`): اسم الشركة
- **الشعار** (`logo`): ملف الشعار المرفوع
- **العنوان** (`adresse`): عنوان الشركة
- **الهاتف** (`telephone`): رقم الهاتف
- **البريد الإلكتروني** (`email`): عنوان البريد

### معلومات ضريبية
- **الرقم الضريبي** (`if_fiscal`): IF
- **السجل التجاري** (`rc`): RC  
- **رقم ICE** (`ice`): ICE

### معلومات إضافية
- **تذييل الصفحة** (`pied_page`): نص يظهر في تذييل التقارير

## 🔄 كيفية التحديث

### 1. تحديث معلومات الشركة
1. اذهب إلى **Informations de la Société**
2. قم بتعديل المعلومات المطلوبة
3. ارفع شعار جديد إذا لزم الأمر
4. احفظ التغييرات

### 2. التحديث التلقائي
- جميع الصفحات ستظهر المعلومات الجديدة فوراً
- لا حاجة لإعادة تشغيل التطبيق
- الشعار الجديد سيظهر في جميع الأماكن

## 🛡️ الأمان والاستقرار

### Fallback للشعار
- إذا لم يكن هناك شعار مرفوع، يظهر الشعار الافتراضي
- إذا فشل تحميل الشعار، يتم التبديل للشعار الافتراضي تلقائياً

### Fallback للنصوص
- إذا لم تكن معلومات الشركة متوفرة، تظهر النصوص الافتراضية
- لا توجد أخطاء في حالة عدم وجود البيانات

### معالجة الأخطاء
- جميع استعلامات قاعدة البيانات محمية بـ try-catch
- رسائل خطأ واضحة في حالة حدوث مشاكل

## 📊 نتائج الاختبار

```
🏢 اختبار تكامل معلومات الشركة
==================================================

1️⃣ فحص قاعدة البيانات:
   ✅ جدول societe موجود
   ✅ بيانات الشركة موجودة

2️⃣ فحص دالة get_company_info:
   ✅ دالة get_company_info موجودة
   ✅ context_processor موجود

3️⃣ فحص القوالب المحدثة:
   ✅ جميع القوالب تستخدم company_info
   ✅ جميع المعلومات مربوطة بشكل صحيح

4️⃣ فحص مجلد uploads:
   ✅ مجلد uploads موجود مع ملفات الشعار

5️⃣ فحص الشعار الافتراضي:
   ✅ الشعار الافتراضي موجود

🎉 تم الانتهاء من اختبار تكامل معلومات الشركة!
```

## 🚀 الاستخدام

### للمطورين
- استخدم `{{ company_info.field_name }}` في أي قالب للوصول لمعلومات الشركة
- تأكد من إضافة fallback للحقول المهمة
- استخدم `onerror` للصور للتبديل للشعار الافتراضي

### للمستخدمين
- قم بتحديث معلومات الشركة من صفحة "Informations de la Société"
- ارفع شعار بصيغة PNG أو JPG للحصول على أفضل جودة
- املأ جميع الحقول للحصول على تقارير احترافية

## 📞 الدعم

في حالة وجود مشاكل:
1. تأكد من وجود بيانات في جدول `societe`
2. تحقق من صحة مسار ملف الشعار
3. راجع logs التطبيق للأخطاء
4. شغل `test_company_integration.py` للتشخيص

---

**تم الانتهاء من ربط معلومات الشركة بنجاح! 🎉**

جميع القوالب والتقارير تستخدم الآن معلومات الشركة المسجلة تلقائياً مع دعم كامل للشعار والمعلومات الأساسية.
