{% extends "base.html" %}

{% block title %}Changer le mot de passe{% endblock %}

{% block page_title %}Changer le mot de passe{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h4><i class="fas fa-key"></i> Changer le mot de passe</h4>
            </div>
            <div class="card-body">
                {% with messages = get_flashed_messages(with_categories=true) %}
                    {% if messages %}
                        {% for category, message in messages %}
                            <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <strong>Conseils de sécurité:</strong>
                    <ul class="mb-0 mt-2">
                        <li>Utilisez au moins 8 caractères</li>
                        <li>Mélangez lettres majuscules et minuscules</li>
                        <li>Incluez des chiffres et des caractères spéciaux</li>
                        <li>Évitez les mots de passe trop simples</li>
                    </ul>
                </div>

                <form method="POST" action="{{ url_for('change_password') }}">
                    <div class="mb-3">
                        <label for="current_password" class="form-label">
                            <i class="fas fa-lock"></i> Mot de passe actuel
                        </label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required>
                    </div>

                    <div class="mb-3">
                        <label for="new_password" class="form-label">
                            <i class="fas fa-key"></i> Nouveau mot de passe
                        </label>
                        <input type="password" class="form-control" id="new_password" name="new_password" required minlength="6">
                        <div class="form-text">Minimum 6 caractères</div>
                    </div>

                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">
                            <i class="fas fa-check-circle"></i> Confirmer le nouveau mot de passe
                        </label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required minlength="6">
                    </div>

                    <div class="d-grid gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Changer le mot de passe
                        </button>
                        <a href="{{ url_for('dashboard') }}" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Annuler
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const newPassword = document.getElementById('new_password');
    const confirmPassword = document.getElementById('confirm_password');
    
    function validatePasswords() {
        if (newPassword.value !== confirmPassword.value) {
            confirmPassword.setCustomValidity('Les mots de passe ne correspondent pas');
        } else {
            confirmPassword.setCustomValidity('');
        }
    }
    
    newPassword.addEventListener('input', validatePasswords);
    confirmPassword.addEventListener('input', validatePasswords);
});
</script>
{% endblock %}
