{% extends "base.html" %}

{% block title %}Gestion de la Base de Données{% endblock %}

{% block page_title %}Gestion de la Base de Données{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-gradient-primary text-white">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h4 class="mb-0">
                                <i class="fas fa-database me-2"></i>
                                Gestion de la Base de Données
                            </h4>
                            <small class="opacity-75">Importation et exportation des données</small>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-light text-dark fs-6">
                                <i class="fas fa-server me-1"></i>
                                Système de Maintenance
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-primary mb-2">
                        <i class="fas fa-building fa-2x"></i>
                    </div>
                    <h5 class="card-title text-primary">{{ sites_count }}</h5>
                    <p class="card-text text-muted">Sites</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-success mb-2">
                        <i class="fas fa-handshake fa-2x"></i>
                    </div>
                    <h5 class="card-title text-success">{{ marches_count }}</h5>
                    <p class="card-text text-muted">Marchés</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-warning mb-2">
                        <i class="fas fa-tools fa-2x"></i>
                    </div>
                    <h5 class="card-title text-warning">{{ interventions_count }}</h5>
                    <p class="card-text text-muted">Interventions</p>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body text-center">
                    <div class="text-danger mb-2">
                        <i class="fas fa-exclamation-triangle fa-2x"></i>
                    </div>
                    <h5 class="card-title text-danger">{{ reclamations_count }}</h5>
                    <p class="card-text text-muted">Réclamations</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <!-- Export Section -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-download me-2"></i>
                        Exportation des Données
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">
                        Exportez toutes les données de la base de données vers différents formats.
                    </p>

                    <!-- Export Options -->
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-success btn-lg" onclick="exportDatabase('sql')">
                                    <i class="fas fa-database me-2"></i>
                                    Exporter en SQL
                                    <small class="d-block">Sauvegarde complète de la base de données</small>
                                </button>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-success btn-lg" onclick="exportDatabase('excel')">
                                    <i class="fas fa-file-excel me-2"></i>
                                    Exporter en Excel
                                    <small class="d-block">Fichier Excel avec toutes les tables</small>
                                </button>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-grid gap-2">
                                <button type="button" class="btn btn-outline-success btn-lg" onclick="exportDatabase('json')">
                                    <i class="fas fa-file-code me-2"></i>
                                    Exporter en JSON
                                    <small class="d-block">Format JSON pour intégration</small>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Export Status -->
                    <div id="export-status" class="mt-3" style="display: none;">
                        <div class="alert alert-info">
                            <i class="fas fa-spinner fa-spin me-2"></i>
                            Exportation en cours...
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Import Section -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-upload me-2"></i>
                        Importation des Données
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-4">
                        Importez des données depuis un fichier de sauvegarde ou un fichier Excel.
                    </p>

                    <!-- Import Form -->
                    <form id="import-form" enctype="multipart/form-data">
                        <div class="mb-3">
                            <label for="import-file" class="form-label">
                                <i class="fas fa-file me-1"></i>
                                Sélectionner un fichier
                            </label>
                            <input type="file" class="form-control form-control-lg" id="import-file"
                                   name="file" accept=".db,.xlsx,.xls,.json" required>
                            <div class="form-text">
                                Formats acceptés: Base de données (.db), Excel (.xlsx, .xls), JSON
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="import-type" class="form-label">
                                <i class="fas fa-cog me-1"></i>
                                Type d'importation
                            </label>
                            <select class="form-select form-select-lg" id="import-type" name="import_type" required>
                                <option value="">Sélectionner le type</option>
                                <option value="replace">Remplacer toutes les données</option>
                                <option value="append">Ajouter aux données existantes</option>
                                <option value="update">Mettre à jour les données existantes</option>
                            </select>
                        </div>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Attention:</strong> L'importation peut modifier ou supprimer des données existantes.
                            Il est recommandé de faire une sauvegarde avant l'importation.
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-upload me-2"></i>
                                Importer les Données
                            </button>
                        </div>
                    </form>

                    <!-- Import Status -->
                    <div id="import-status" class="mt-3" style="display: none;">
                        <div class="alert alert-info">
                            <i class="fas fa-spinner fa-spin me-2"></i>
                            Importation en cours...
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Automatic Backup Configuration -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>
                        Configuration des Sauvegardes Automatiques
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Daily Backup -->
                        <div class="col-lg-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header bg-primary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-calendar-day me-1"></i>
                                        Sauvegarde Quotidienne
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="daily-enabled">
                                        <label class="form-check-label" for="daily-enabled">
                                            Activer la sauvegarde quotidienne
                                        </label>
                                    </div>
                                    <div class="mb-3">
                                        <label for="daily-time" class="form-label">Heure</label>
                                        <input type="time" class="form-control" id="daily-time" value="02:00">
                                    </div>
                                    <div class="mb-3">
                                        <label for="daily-keep" class="form-label">Conserver (jours)</label>
                                        <input type="number" class="form-control" id="daily-keep" value="7" min="1" max="30">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Weekly Backup -->
                        <div class="col-lg-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-calendar-week me-1"></i>
                                        Sauvegarde Hebdomadaire
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="weekly-enabled">
                                        <label class="form-check-label" for="weekly-enabled">
                                            Activer la sauvegarde hebdomadaire
                                        </label>
                                    </div>
                                    <div class="mb-3">
                                        <label for="weekly-day" class="form-label">Jour</label>
                                        <select class="form-select" id="weekly-day">
                                            <option value="sunday">Dimanche</option>
                                            <option value="monday">Lundi</option>
                                            <option value="tuesday">Mardi</option>
                                            <option value="wednesday">Mercredi</option>
                                            <option value="thursday">Jeudi</option>
                                            <option value="friday">Vendredi</option>
                                            <option value="saturday">Samedi</option>
                                        </select>
                                    </div>
                                    <div class="mb-3">
                                        <label for="weekly-time" class="form-label">Heure</label>
                                        <input type="time" class="form-control" id="weekly-time" value="03:00">
                                    </div>
                                    <div class="mb-3">
                                        <label for="weekly-keep" class="form-label">Conserver (semaines)</label>
                                        <input type="number" class="form-control" id="weekly-keep" value="4" min="1" max="12">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Monthly Backup -->
                        <div class="col-lg-4 mb-3">
                            <div class="card h-100">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-calendar-alt me-1"></i>
                                        Sauvegarde Mensuelle
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="form-check mb-3">
                                        <input class="form-check-input" type="checkbox" id="monthly-enabled">
                                        <label class="form-check-label" for="monthly-enabled">
                                            Activer la sauvegarde mensuelle
                                        </label>
                                    </div>
                                    <div class="mb-3">
                                        <label for="monthly-day" class="form-label">Jour du mois</label>
                                        <input type="number" class="form-control" id="monthly-day" value="1" min="1" max="28">
                                    </div>
                                    <div class="mb-3">
                                        <label for="monthly-time" class="form-label">Heure</label>
                                        <input type="time" class="form-control" id="monthly-time" value="04:00">
                                    </div>
                                    <div class="mb-3">
                                        <label for="monthly-keep" class="form-label">Conserver (mois)</label>
                                        <input type="number" class="form-control" id="monthly-keep" value="12" min="1" max="24">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Auto Cleanup Settings -->
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-secondary text-white">
                                    <h6 class="mb-0">
                                        <i class="fas fa-broom me-1"></i>
                                        Nettoyage Automatique
                                    </h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check mb-3">
                                                <input class="form-check-input" type="checkbox" id="cleanup-enabled">
                                                <label class="form-check-label" for="cleanup-enabled">
                                                    Activer le nettoyage automatique
                                                </label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label for="max-size" class="form-label">Taille maximale (GB)</label>
                                                <input type="number" class="form-control" id="max-size" value="5" min="1" max="50" step="0.5">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="d-flex gap-2 justify-content-center">
                                <button type="button" class="btn btn-success" onclick="saveBackupConfig()">
                                    <i class="fas fa-save me-2"></i>
                                    Sauvegarder la Configuration
                                </button>
                                <button type="button" class="btn btn-primary" onclick="createManualBackup()">
                                    <i class="fas fa-download me-2"></i>
                                    Sauvegarde Manuelle
                                </button>
                                <button type="button" class="btn btn-info" onclick="loadBackupConfig()">
                                    <i class="fas fa-sync me-2"></i>
                                    Recharger
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup History -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>
                        Historique des Sauvegardes
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th><i class="fas fa-calendar me-1"></i>Date</th>
                                    <th><i class="fas fa-file me-1"></i>Nom du fichier</th>
                                    <th><i class="fas fa-weight me-1"></i>Taille</th>
                                    <th><i class="fas fa-tag me-1"></i>Type</th>
                                    <th><i class="fas fa-cogs me-1"></i>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="backup-history">
                                <!-- Backup history will be loaded here -->
                                <tr>
                                    <td colspan="5" class="text-center text-muted py-4">
                                        <i class="fas fa-inbox fa-2x mb-2"></i>
                                        <br>Aucune sauvegarde disponible
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div class="modal fade" id="successModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title">
                    <i class="fas fa-check-circle me-2"></i>
                    Opération Réussie
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="success-message">L'opération a été effectuée avec succès.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-success" data-bs-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>

<!-- Error Modal -->
<div class="modal fade" id="errorModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Erreur
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p id="error-message">Une erreur s'est produite.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger" data-bs-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
}

.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.btn-lg {
    padding: 12px 24px;
    font-size: 1.1rem;
}

.alert {
    border: none;
    border-radius: 10px;
}

.table th {
    border-top: none;
    font-weight: 600;
}

.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.progress {
    height: 8px;
    border-radius: 4px;
}

.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Variables globales
let currentOperation = null;

// ==================== EXPORT FUNCTIONS ====================
function exportDatabase(format) {
    if (currentOperation) {
        showError('Une opération est déjà en cours. Veuillez patienter.');
        return;
    }

    currentOperation = 'export';
    showExportStatus(true);

    // Créer un lien de téléchargement vers l'API
    const link = document.createElement('a');
    link.href = `/api/export/${format}`;
    link.style.display = 'none';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Simuler un délai pour l'interface utilisateur
    setTimeout(() => {
        showExportStatus(false);
        showSuccess(`Téléchargement de la sauvegarde ${format.toUpperCase()} initié.`);
        currentOperation = null;

        // Recharger l'historique des sauvegardes
        loadBackupHistory();
    }, 1000);
}

// ==================== IMPORT FUNCTIONS ====================
document.getElementById('import-form').addEventListener('submit', function(e) {
    e.preventDefault();

    if (currentOperation) {
        showError('Une opération est déjà en cours. Veuillez patienter.');
        return;
    }

    const fileInput = document.getElementById('import-file');
    const importType = document.getElementById('import-type').value;

    if (!fileInput.files[0]) {
        showError('Veuillez sélectionner un fichier.');
        return;
    }

    if (!importType) {
        showError('Veuillez sélectionner le type d\'importation.');
        return;
    }

    currentOperation = 'import';
    showImportStatus(true);

    // Créer FormData pour l'upload
    const formData = new FormData();
    formData.append('file', fileInput.files[0]);
    formData.append('import_type', importType);

    // Envoyer la requête à l'API
    fetch('/api/import', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        showImportStatus(false);

        if (data.success) {
            showSuccess(data.message || 'Importation réussie.');

            // Reset form
            document.getElementById('import-form').reset();

            // Refresh page data
            setTimeout(() => {
                location.reload();
            }, 2000);
        } else {
            showError(data.error || 'Erreur lors de l\'importation.');
        }

        currentOperation = null;
    })
    .catch(error => {
        showImportStatus(false);
        showError('Erreur lors de l\'importation: ' + error.message);
        currentOperation = null;
    });
});

// ==================== UI FUNCTIONS ====================
function showExportStatus(show) {
    const statusDiv = document.getElementById('export-status');
    statusDiv.style.display = show ? 'block' : 'none';
}

function showImportStatus(show) {
    const statusDiv = document.getElementById('import-status');
    statusDiv.style.display = show ? 'block' : 'none';
}

function showSuccess(message) {
    document.getElementById('success-message').textContent = message;
    const modal = new bootstrap.Modal(document.getElementById('successModal'));
    modal.show();
}

function showError(message) {
    document.getElementById('error-message').textContent = message;
    const modal = new bootstrap.Modal(document.getElementById('errorModal'));
    modal.show();
}

function addToBackupHistory(filename, type) {
    const tbody = document.getElementById('backup-history');
    const now = new Date();
    const dateStr = now.toLocaleDateString('fr-FR') + ' ' + now.toLocaleTimeString('fr-FR');

    // Remove "no backups" message if it exists
    if (tbody.children.length === 1 && tbody.children[0].children.length === 1) {
        tbody.innerHTML = '';
    }

    const row = document.createElement('tr');
    row.innerHTML = `
        <td>${dateStr}</td>
        <td>${filename}</td>
        <td>-</td>
        <td><span class="badge bg-${type === 'sql' ? 'primary' : type === 'excel' ? 'success' : 'info'}">${type.toUpperCase()}</span></td>
        <td>
            <button class="btn btn-sm btn-outline-primary" onclick="downloadBackup('${filename}')">
                <i class="fas fa-download"></i>
            </button>
            <button class="btn btn-sm btn-outline-danger" onclick="deleteBackup('${filename}')">
                <i class="fas fa-trash"></i>
            </button>
        </td>
    `;
    tbody.insertBefore(row, tbody.firstChild);
}

function downloadBackup(filename) {
    const link = document.createElement('a');
    link.href = `/api/download-backup/${filename}`;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

function deleteBackup(filename) {
    if (confirm(`Êtes-vous sûr de vouloir supprimer la sauvegarde "${filename}" ?`)) {
        // Simuler la suppression
        showSuccess(`Sauvegarde "${filename}" supprimée avec succès.`);
        // Refresh backup history
        location.reload();
    }
}

// ==================== INITIALIZATION ====================
document.addEventListener('DOMContentLoaded', function() {
    console.log('Database Management page loaded');

    // Load backup history and configuration
    loadBackupHistory();
    loadBackupConfig();
});

function loadBackupHistory() {
    fetch('/api/backup-history')
        .then(response => response.json())
        .then(data => {
            const tbody = document.getElementById('backup-history');
            tbody.innerHTML = '';

            if (data.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-2x mb-2"></i>
                            <br>Aucune sauvegarde disponible
                        </td>
                    </tr>
                `;
            } else {
                data.forEach(backup => {
                    const badgeClass = backup.type === 'sql' ? 'primary' :
                                     backup.type === 'excel' ? 'success' : 'info';

                    const row = document.createElement('tr');
                    row.innerHTML = `
                        <td>${backup.modified}</td>
                        <td>${backup.filename}</td>
                        <td>${backup.size_mb} MB</td>
                        <td><span class="badge bg-${badgeClass}">${backup.type.toUpperCase()}</span></td>
                        <td>
                            <button class="btn btn-sm btn-outline-primary" onclick="downloadBackup('${backup.filename}')">
                                <i class="fas fa-download"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteBackup('${backup.filename}')">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    `;
                    tbody.appendChild(row);
                });
            }
        })
        .catch(error => {
            console.error('Erreur lors du chargement de l\'historique:', error);
        });
}

// ==================== BACKUP CONFIGURATION ====================
function loadBackupConfig() {
    fetch('/api/backup-config')
        .then(response => response.json())
        .then(config => {
            // Daily backup
            document.getElementById('daily-enabled').checked = config.daily_backup.enabled;
            document.getElementById('daily-time').value = config.daily_backup.time;
            document.getElementById('daily-keep').value = config.daily_backup.keep_days;

            // Weekly backup
            document.getElementById('weekly-enabled').checked = config.weekly_backup.enabled;
            document.getElementById('weekly-day').value = config.weekly_backup.day;
            document.getElementById('weekly-time').value = config.weekly_backup.time;
            document.getElementById('weekly-keep').value = config.weekly_backup.keep_weeks;

            // Monthly backup
            document.getElementById('monthly-enabled').checked = config.monthly_backup.enabled;
            document.getElementById('monthly-day').value = config.monthly_backup.day;
            document.getElementById('monthly-time').value = config.monthly_backup.time;
            document.getElementById('monthly-keep').value = config.monthly_backup.keep_months;

            // Auto cleanup
            document.getElementById('cleanup-enabled').checked = config.auto_cleanup.enabled;
            document.getElementById('max-size').value = config.auto_cleanup.max_backup_size_gb;
        })
        .catch(error => {
            console.error('Erreur lors du chargement de la configuration:', error);
        });
}

function saveBackupConfig() {
    const config = {
        daily_backup: {
            enabled: document.getElementById('daily-enabled').checked,
            time: document.getElementById('daily-time').value,
            keep_days: parseInt(document.getElementById('daily-keep').value)
        },
        weekly_backup: {
            enabled: document.getElementById('weekly-enabled').checked,
            day: document.getElementById('weekly-day').value,
            time: document.getElementById('weekly-time').value,
            keep_weeks: parseInt(document.getElementById('weekly-keep').value)
        },
        monthly_backup: {
            enabled: document.getElementById('monthly-enabled').checked,
            day: parseInt(document.getElementById('monthly-day').value),
            time: document.getElementById('monthly-time').value,
            keep_months: parseInt(document.getElementById('monthly-keep').value)
        },
        auto_cleanup: {
            enabled: document.getElementById('cleanup-enabled').checked,
            max_backup_size_gb: parseFloat(document.getElementById('max-size').value)
        }
    };

    fetch('/api/backup-config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(config)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showSuccess('Configuration sauvegardée avec succès.');
        } else {
            showError(data.error || 'Erreur lors de la sauvegarde de la configuration.');
        }
    })
    .catch(error => {
        showError('Erreur lors de la sauvegarde de la configuration: ' + error.message);
    });
}

function createManualBackup() {
    if (currentOperation) {
        showError('Une opération est déjà en cours. Veuillez patienter.');
        return;
    }

    currentOperation = 'manual_backup';

    fetch('/api/manual-backup')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuccess(`Sauvegarde manuelle créée: ${data.filename}`);
                loadBackupHistory();
            } else {
                showError(data.error || 'Erreur lors de la création de la sauvegarde.');
            }
            currentOperation = null;
        })
        .catch(error => {
            showError('Erreur lors de la création de la sauvegarde: ' + error.message);
            currentOperation = null;
        });
}
</script>
{% endblock %}
