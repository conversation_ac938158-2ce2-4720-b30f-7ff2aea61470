#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح مشاكل قاعدة البيانات والبرنامج
"""

import sqlite3
import os
import shutil
from datetime import datetime

def backup_database():
    """عمل نسخة احتياطية من قاعدة البيانات"""
    if os.path.exists('maintenance.db'):
        backup_name = f'maintenance_backup_before_fix_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        shutil.copy2('maintenance.db', backup_name)
        print(f"✅ تم حفظ نسخة احتياطية: {backup_name}")
        return backup_name
    return None

def fix_database_structure():
    """إصلاح هيكل قاعدة البيانات"""
    
    conn = sqlite3.connect('maintenance.db')
    cursor = conn.cursor()
    
    print("🔧 إصلاح هيكل قاعدة البيانات...")
    
    try:
        # فحص وإصلاح جدول regions
        cursor.execute("PRAGMA table_info(regions)")
        regions_columns = [column[1] for column in cursor.fetchall()]
        
        if 'nom_region' not in regions_columns:
            print("   🔄 إضافة عمود nom_region إلى جدول regions...")
            cursor.execute("ALTER TABLE regions ADD COLUMN nom_region TEXT")
            
            # نسخ البيانات من nom إلى nom_region إذا كان موجود
            if 'nom' in regions_columns:
                cursor.execute("UPDATE regions SET nom_region = nom WHERE nom_region IS NULL")
        
        # فحص وإصلاح جدول sites
        cursor.execute("PRAGMA table_info(sites)")
        sites_columns = [column[1] for column in cursor.fetchall()]
        
        if 'nom_site' not in sites_columns:
            print("   🔄 إضافة عمود nom_site إلى جدول sites...")
            cursor.execute("ALTER TABLE sites ADD COLUMN nom_site TEXT")
            
            # نسخ البيانات من nom إلى nom_site إذا كان موجود
            if 'nom' in sites_columns:
                cursor.execute("UPDATE sites SET nom_site = nom WHERE nom_site IS NULL")
        
        # إضافة جدول company_info إذا لم يكن موجود
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS company_info (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT,
                address TEXT,
                phone TEXT,
                email TEXT,
                logo_path TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إضافة جدول user_activities إذا لم يكن موجود
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS user_activities (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT,
                activity_type TEXT,
                description TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إضافة جدول backup_logs إذا لم يكن موجود
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS backup_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                backup_type TEXT,
                file_path TEXT,
                file_size INTEGER,
                status TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # إضافة جدول extincteur_details إذا لم يكن موجود
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS extincteur_details (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                intervention_id INTEGER,
                numero_extincteur TEXT,
                type_extincteur TEXT,
                capacite TEXT,
                date_fabrication DATE,
                date_derniere_verification DATE,
                prochaine_verification DATE,
                etat TEXT,
                observations TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (intervention_id) REFERENCES interventions (id)
            )
        """)
        
        conn.commit()
        print("✅ تم إصلاح هيكل قاعدة البيانات")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح قاعدة البيانات: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()
    
    return True

def fix_missing_imports():
    """إصلاح المكتبات المفقودة"""
    
    print("📦 فحص المكتبات المطلوبة...")
    
    required_packages = [
        'flask',
        'flask-login',
        'pandas',
        'openpyxl'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} غير مثبت")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n💡 لتثبيت المكتبات المفقودة:")
        for package in missing_packages:
            print(f"   pip install {package}")
        return False
    
    return True

def create_missing_directories():
    """إنشاء المجلدات المفقودة"""
    
    print("📁 إنشاء المجلدات المطلوبة...")
    
    directories = [
        'static/uploads',
        'backups',
        'uploads',
        'static/images'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory, exist_ok=True)
            print(f"   ✅ تم إنشاء: {directory}")
        else:
            print(f"   ✅ موجود: {directory}")

def test_database_operations():
    """اختبار عمليات قاعدة البيانات"""
    
    print("🧪 اختبار عمليات قاعدة البيانات...")
    
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # اختبار جدول regions
        cursor.execute("SELECT COUNT(*) FROM regions")
        regions_count = cursor.fetchone()[0]
        print(f"   ✅ جدول regions: {regions_count} سجل")
        
        # اختبار جدول sites
        cursor.execute("SELECT COUNT(*) FROM sites")
        sites_count = cursor.fetchone()[0]
        print(f"   ✅ جدول sites: {sites_count} سجل")
        
        # اختبار جدول users
        cursor.execute("SELECT COUNT(*) FROM users")
        users_count = cursor.fetchone()[0]
        print(f"   ✅ جدول users: {users_count} سجل")
        
        # اختبار جدول marches
        cursor.execute("SELECT COUNT(*) FROM marches")
        marches_count = cursor.fetchone()[0]
        print(f"   ✅ جدول marches: {marches_count} سجل")
        
        # اختبار جدول interventions
        cursor.execute("SELECT COUNT(*) FROM interventions")
        interventions_count = cursor.fetchone()[0]
        print(f"   ✅ جدول interventions: {interventions_count} سجل")
        
        # اختبار جدول reclamations
        cursor.execute("SELECT COUNT(*) FROM reclamations")
        reclamations_count = cursor.fetchone()[0]
        print(f"   ✅ جدول reclamations: {reclamations_count} سجل")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ خطأ في اختبار قاعدة البيانات: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    
    print("🔧 إصلاح مشاكل البرنامج")
    print("🔧 Fixing Application Issues")
    print("=" * 60)
    
    # عمل نسخة احتياطية
    backup_file = backup_database()
    
    # إنشاء المجلدات المطلوبة
    create_missing_directories()
    
    # إصلاح هيكل قاعدة البيانات
    if fix_database_structure():
        print("✅ تم إصلاح هيكل قاعدة البيانات")
    else:
        print("❌ فشل في إصلاح هيكل قاعدة البيانات")
        return
    
    # فحص المكتبات
    if fix_missing_imports():
        print("✅ جميع المكتبات متوفرة")
    else:
        print("⚠️ بعض المكتبات مفقودة")
    
    # اختبار قاعدة البيانات
    if test_database_operations():
        print("✅ قاعدة البيانات تعمل بشكل صحيح")
    else:
        print("❌ مشاكل في قاعدة البيانات")
    
    print("\n🎉 تم الانتهاء من الإصلاح!")
    print("🎉 Repair completed!")
    
    if backup_file:
        print(f"\n💾 النسخة الاحتياطية: {backup_file}")
        print(f"💾 Backup file: {backup_file}")
    
    print("\n📋 الخطوات التالية:")
    print("📋 Next steps:")
    print("   1. شغل البرنامج للتأكد من عمله")
    print("   1. Run the application to verify it works")
    print("   2. اختبر وظائف التصدير والاستيراد")
    print("   2. Test export/import functions")
    print("   3. تأكد من عمل جميع النماذج")
    print("   3. Verify all forms work")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
