{% extends "base.html" %}

{% block title %}Carte du Maroc{% endblock %}

{% block page_title %}Carte du Maroc{% endblock %}

{% block content %}
<!-- Hidden data elements for JavaScript -->
<script type="application/json" id="sites-data">{{ sites|tojson|safe if sites else '[]' }}</script>
<script type="application/json" id="marches-data">{{ marches|tojson|safe if marches else '[]' }}</script>
<script type="application/json" id="interventions-data">{{ interventions|tojson|safe if interventions else '[]' }}</script>
<script type="application/json" id="reclamations-data">{{ reclamations|tojson|safe if reclamations else '[]' }}</script>

<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <div class="d-flex justify-content-between align-items-center w-100">
                        <div class="header-left">
                            <h4 class="mb-0"><i class="fas fa-map-marked-alt"></i> Carte du Maroc</h4>
                            <small>Visualisation géographique des données de maintenance</small>
                        </div>
                        <div class="header-right ms-auto">
                            <div class="btn-group" role="group">
                                <button type="button" class="btn btn-success btn-sm" onclick="exportData()">
                                    <i class="fas fa-file-excel"></i> Excel
                                </button>
                                <button type="button" class="btn btn-info btn-sm" onclick="location.reload()">
                                    <i class="fas fa-sync-alt"></i> Actualiser
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body py-2">
                    <div class="text-center">
                        <span class="me-3"><i class="fas fa-filter"></i> Filtres:</span>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-outline-primary btn-sm active" id="btn-all" onclick="showAll()">
                                <i class="fas fa-globe"></i> Tout
                            </button>
                            <button type="button" class="btn btn-outline-primary btn-sm" id="btn-sites" onclick="showSites()">
                                <i class="fas fa-building"></i> Sites
                            </button>
                            <button type="button" class="btn btn-outline-success btn-sm" id="btn-marches" onclick="showMarches()">
                                <i class="fas fa-handshake"></i> Marchés
                            </button>
                            <button type="button" class="btn btn-outline-warning btn-sm" id="btn-interventions" onclick="showInterventions()">
                                <i class="fas fa-tools"></i> Interventions
                            </button>
                            <button type="button" class="btn btn-outline-danger btn-sm" id="btn-reclamations" onclick="showReclamations()">
                                <i class="fas fa-exclamation-triangle"></i> Réclamations
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Map -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-body p-0">
                    <div id="map" style="height: 700px;"></div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics and Legend -->
    <div class="row">
        <div class="col-lg-8">
            <!-- Statistics -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-chart-bar"></i> Statistiques</h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3 col-6">
                            <div class="text-center p-3 bg-primary text-white rounded">
                                <div class="h4 mb-0">{{ sites|length }}</div>
                                <small>Sites</small>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="text-center p-3 bg-success text-white rounded">
                                <div class="h4 mb-0">{{ marches|length }}</div>
                                <small>Marchés</small>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="text-center p-3 bg-warning text-white rounded">
                                <div class="h4 mb-0">{{ interventions|length }}</div>
                                <small>Interventions</small>
                            </div>
                        </div>
                        <div class="col-md-3 col-6">
                            <div class="text-center p-3 bg-danger text-white rounded">
                                <div class="h4 mb-0">{{ reclamations|length }}</div>
                                <small>Réclamations</small>
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="text-center">
                        <div class="h3 mb-0 text-primary">{{ (sites|length) + (marches|length) + (interventions|length) + (reclamations|length) }}</div>
                        <small class="text-muted">Total des éléments</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Legend -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-map-signs"></i> Légende</h6>
                </div>
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <span class="badge bg-primary me-3" style="width: 20px; height: 20px; border-radius: 50%; display: inline-block;"></span>
                        <span class="fw-bold">Sites</span>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <span class="badge bg-success me-3" style="width: 20px; height: 20px; border-radius: 50%; display: inline-block;"></span>
                        <span class="fw-bold">Marchés</span>
                    </div>
                    <div class="d-flex align-items-center mb-3">
                        <span class="badge bg-warning me-3" style="width: 20px; height: 20px; border-radius: 50%; display: inline-block;"></span>
                        <span class="fw-bold">Interventions</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <span class="badge bg-danger me-3" style="width: 20px; height: 20px; border-radius: 50%; display: inline-block;"></span>
                        <span class="fw-bold">Réclamations</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
<style>
    /* Header layout - buttons on far right */
    .card-header .d-flex {
        width: 100% !important;
    }

    .card-header .header-left {
        flex: 0 0 auto;
    }

    .card-header .header-right {
        flex: 0 0 auto;
        margin-left: auto !important;
    }

    .card-header .btn-group {
        white-space: nowrap;
        display: inline-flex;
    }

    /* Ensure buttons stay on the absolute right */
    .card-header .ms-auto {
        margin-left: auto !important;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .card-header .d-flex {
            flex-direction: column;
            align-items: flex-start;
        }

        .card-header .header-right {
            margin-left: 0 !important;
            margin-top: 15px;
            align-self: flex-end;
            width: auto;
        }

        .card-header .btn-group {
            justify-content: flex-end;
        }
    }

    @media (max-width: 576px) {
        .card-header .btn-group {
            flex-direction: column;
            align-items: flex-end;
        }

        .card-header .btn-group .btn {
            margin-bottom: 5px;
            width: auto;
        }

        .card-header .btn-group .btn:last-child {
            margin-bottom: 0;
        }
    }

    /* Map container improvements */
    #map {
        border-radius: 0.375rem;
    }

    /* Statistics cards hover effect */
    .bg-primary:hover, .bg-success:hover, .bg-warning:hover, .bg-danger:hover {
        transform: translateY(-2px);
        transition: transform 0.2s ease;
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }

    /* Additional spacing for better layout */
    .card-header {
        padding: 1rem 1.5rem;
    }

    .header-left h4 {
        margin-bottom: 0.25rem;
    }

    .header-left small {
        opacity: 0.9;
    }
</style>
{% endblock %}

{% block extra_js %}
<!-- Leaflet JS -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
// Variables globales
let map;
let allMarkers = [];

// Données de la carte
let mapData = {};

// Initialiser les données depuis le serveur
function initMapData() {
    try {
        // Récupérer les données depuis les éléments cachés
        const sitesData = document.getElementById('sites-data');
        const marchesData = document.getElementById('marches-data');
        const interventionsData = document.getElementById('interventions-data');
        const reclamationsData = document.getElementById('reclamations-data');

        mapData = {
            sites: sitesData ? JSON.parse(sitesData.textContent) : [],
            marches: marchesData ? JSON.parse(marchesData.textContent) : [],
            interventions: interventionsData ? JSON.parse(interventionsData.textContent) : [],
            reclamations: reclamationsData ? JSON.parse(reclamationsData.textContent) : []
        };
    } catch (e) {
        console.error('Erreur lors du chargement des données:', e);
        mapData = {
            sites: [],
            marches: [],
            interventions: [],
            reclamations: []
        };
    }
}

// Villes du Maroc avec coordonnées
const cities = {
    'Casablanca': [33.5731, -7.5898],
    'Rabat': [34.0209, -6.8416],
    'Fès': [34.0181, -5.0078],
    'Marrakech': [31.6295, -7.9811],
    'Agadir': [30.4278, -9.5981],
    'Tanger': [35.7595, -5.8340],
    'Meknès': [33.8935, -5.5473],
    'Oujda': [34.6814, -1.9086],
    'Kenitra': [34.2610, -6.5802],
    'Tétouan': [35.5889, -5.3626],
    'Safi': [32.2994, -9.2372],
    'El Jadida': [33.2316, -8.5007],
    'Beni Mellal': [32.3373, -6.3498],
    'Errachidia': [31.9314, -4.4240],
    'Taza': [34.2133, -4.0103],
    'Essaouira': [31.5085, -9.7595],
    'Khouribga': [32.8811, -6.9063],
    'Ouarzazate': [30.9335, -6.9370],
    'Settat': [33.0018, -7.6160],
    'Larache': [35.1932, -6.1563]
};

// Fonction pour obtenir les coordonnées
function getCoordinates(item) {
    // Si GPS existe et est valide
    if (item.gps && item.gps.includes(',')) {
        const coords = item.gps.split(',');
        const lat = parseFloat(coords[0].trim());
        const lng = parseFloat(coords[1].trim());
        if (!isNaN(lat) && !isNaN(lng)) {
            return [lat, lng];
        }
    }

    // Chercher par lieu/ville
    if (item.lieu) {
        for (const [city, coords] of Object.entries(cities)) {
            if (item.lieu.toLowerCase().includes(city.toLowerCase()) ||
                city.toLowerCase().includes(item.lieu.toLowerCase())) {
                return coords;
            }
        }
    }

    // Coordonnées par défaut (centre du Maroc)
    return [31.7917, -7.0926];
}

// Fonction pour créer un marqueur
function createMarker(item, type, color) {
    const coords = getCoordinates(item);
    const marker = L.circleMarker(coords, {
        radius: 8,
        fillColor: color,
        color: '#fff',
        weight: 2,
        opacity: 1,
        fillOpacity: 0.8
    });

    // Popup
    let popupContent = `<strong>${item.nom || item.objet || 'Sans nom'}</strong><br>`;
    if (item.lieu) popupContent += `Lieu: ${item.lieu}<br>`;
    if (item.client) popupContent += `Client: ${item.client}<br>`;
    if (item.domaine) popupContent += `Domaine: ${item.domaine}<br>`;

    marker.bindPopup(popupContent);
    marker.itemType = type;

    return marker;
}

// Initialisation de la carte
function initMap() {
    console.log('Initialisation de la carte...');

    // Initialiser les données
    initMapData();

    // Créer la carte
    map = L.map('map').setView([31.7917, -7.0926], 6);

    // Ajouter les tuiles
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // Ajouter les marqueurs
    addMarkers();
}

// Ajouter les marqueurs
function addMarkers() {
    console.log('Ajout des marqueurs...');

    // Sites (bleu)
    if (mapData.sites) {
        mapData.sites.forEach(site => {
            const marker = createMarker(site, 'site', '#007bff');
            marker.addTo(map);
            allMarkers.push(marker);
        });
    }

    // Marchés (vert)
    if (mapData.marches) {
        mapData.marches.forEach(marche => {
            const marker = createMarker(marche, 'marche', '#28a745');
            marker.addTo(map);
            allMarkers.push(marker);
        });
    }

    // Interventions (jaune)
    if (mapData.interventions) {
        mapData.interventions.forEach(intervention => {
            const marker = createMarker(intervention, 'intervention', '#ffc107');
            marker.addTo(map);
            allMarkers.push(marker);
        });
    }

    // Réclamations (rouge)
    if (mapData.reclamations) {
        mapData.reclamations.forEach(reclamation => {
            const marker = createMarker(reclamation, 'reclamation', '#dc3545');
            marker.addTo(map);
            allMarkers.push(marker);
        });
    }

    console.log(`${allMarkers.length} marqueurs ajoutés`);
}

// Fonctions de filtrage
function showAll() {
    setActiveButton('btn-all');
    allMarkers.forEach(marker => marker.addTo(map));
}

function showSites() {
    setActiveButton('btn-sites');
    allMarkers.forEach(marker => {
        if (marker.itemType === 'site') {
            marker.addTo(map);
        } else {
            map.removeLayer(marker);
        }
    });
}

function showMarches() {
    setActiveButton('btn-marches');
    allMarkers.forEach(marker => {
        if (marker.itemType === 'marche') {
            marker.addTo(map);
        } else {
            map.removeLayer(marker);
        }
    });
}

function showInterventions() {
    setActiveButton('btn-interventions');
    allMarkers.forEach(marker => {
        if (marker.itemType === 'intervention') {
            marker.addTo(map);
        } else {
            map.removeLayer(marker);
        }
    });
}

function showReclamations() {
    setActiveButton('btn-reclamations');
    allMarkers.forEach(marker => {
        if (marker.itemType === 'reclamation') {
            marker.addTo(map);
        } else {
            map.removeLayer(marker);
        }
    });
}

function setActiveButton(activeId) {
    document.querySelectorAll('[id^="btn-"]').forEach(btn => {
        btn.classList.remove('active');
    });
    document.getElementById(activeId).classList.add('active');
}

// Fonction d'export
function exportData() {
    const data = [];
    allMarkers.forEach(marker => {
        const coords = marker.getLatLng();
        data.push({
            Type: marker.itemType,
            Latitude: coords.lat.toFixed(6),
            Longitude: coords.lng.toFixed(6)
        });
    });

    let csvContent = "data:text/csv;charset=utf-8,";
    csvContent += "Type,Latitude,Longitude\n";
    data.forEach(row => {
        csvContent += `${row.Type},${row.Latitude},${row.Longitude}\n`;
    });

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "carte_maroc.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
}

// Initialisation quand la page est chargée
document.addEventListener('DOMContentLoaded', function() {
    console.log('Chargement de la carte...');
    initMap();
});
</script>
{% endblock %}
