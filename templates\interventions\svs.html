{% extends "base.html" %}

{% block title %}Détails de l'intervention SVS - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Détails de l'intervention SVS{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Détails de l'intervention SVS</h5>
                    <div>
                        <button type="button" class="btn btn-primary" onclick="window.print()">
                            <i class="fas fa-print"></i> Imprimer
                        </button>
                        <a href="{{ url_for('interventions') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 40%">Client</th>
                                <td>{{ intervention.client }}</td>
                            </tr>
                            <tr>
                                <th>N° de marche</th>
                                <td>{{ intervention.numero_marche }}</td>
                            </tr>
                            <tr>
                                <th>Période d'intervention</th>
                                <td>{{ intervention.periode_interventions }}</td>
                            </tr>
                            <tr>
                                <th>Lieu</th>
                                <td>{{ intervention.lieu }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <th style="width: 40%">Domaine</th>
                                <td>{{ intervention.domaine }}</td>
                            </tr>
                            <tr>
                                <th>Objet de marché</th>
                                <td>{{ intervention.objet_marche }}</td>
                            </tr>
                            <tr>
                                <th>Période du Marche</th>
                                <td>{{ intervention.periode_marche }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-bordered table-striped">
                        <thead class="table-light">
                            <tr>
                                <th>N°</th>
                                <th>Région</th>
                                <th>Nom de site</th>
                                <th>Nbr de système</th>
                                <th>Date d'intervention</th>
                                <th>Technicien</th>
                                <th>Situation</th>
                                <th>État de matériel</th>
                                <th>Observation</th>
                                <th>Téléphone chef de site</th>
                                <th>Téléphone de sécurité</th>
                                <th>Technicien à contacter</th>
                                <th>GPS</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if details %}
                                {% for detail in details %}
                                <tr>
                                    <td>{{ loop.index }}</td>
                                    <td>{{ detail.region }}</td>
                                    <td>{{ detail.nom_site }}</td>
                                    <td>{{ detail.nbr_systeme }}</td>
                                    <td>{{ detail.date_intervention }}</td>
                                    <td>{{ detail.technicien }}</td>
                                    <td>
                                        {% if detail.situation == 'Réglé' %}
                                        <span class="badge bg-success">Réglé</span>
                                        {% elif detail.situation == 'Pas encore' %}
                                        <span class="badge bg-warning">Pas encore</span>
                                        {% elif detail.situation == 'Problème' %}
                                        <span class="badge bg-danger">Problème</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ detail.situation }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if detail.etat_materiel == 'Opérationnel' %}
                                        <span class="badge bg-success">Opérationnel</span>
                                        {% elif detail.etat_materiel == 'Non Opérationnel' %}
                                        <span class="badge bg-danger">Non Opérationnel</span>
                                        {% else %}
                                        <span class="badge bg-secondary">{{ detail.etat_materiel }}</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ detail.observation }}</td>
                                    <td>{{ detail.telephone_chef_site }}</td>
                                    <td>{{ detail.telephone_securite }}</td>
                                    <td>{{ detail.technicien_contact }}</td>
                                    <td>{{ detail.gps }}</td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-xs btn-info" data-bs-toggle="modal" data-bs-target="#viewDetailModal{{ detail.id }}" title="Voir les détails" style="padding: 2px 6px; font-size: 10px;">
                                                <i class="fas fa-eye" style="font-size: 10px;"></i>
                                            </button>
                                            <a href="{{ url_for('modifier_detail_intervention', id=detail.id) }}" class="btn btn-xs btn-warning" title="Modifier" style="padding: 2px 6px; font-size: 10px;">
                                                <i class="fas fa-edit" style="font-size: 10px;"></i>
                                            </a>
                                            <button type="button" class="btn btn-xs btn-danger" onclick="if(confirm('Êtes-vous sûr de vouloir supprimer ce détail d\'intervention ?\n\nCette action est irréversible !')) { window.location.href='{{ url_for('supprimer_detail_intervention', id=detail.id) }}'; }" title="Supprimer" style="padding: 2px 6px; font-size: 10px;">
                                                <i class="fas fa-trash" style="font-size: 10px;"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="14" class="text-center">Aucun détail d'intervention enregistré</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>

                <div class="mt-3">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDetailModal">
                        <i class="fas fa-plus"></i> Ajouter une ligne
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour ajouter un détail -->
<div class="modal fade" id="addDetailModal" tabindex="-1" aria-labelledby="addDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addDetailModalLabel">Ajouter un détail d'intervention</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('details_intervention', id=intervention.id) }}">
                <input type="hidden" name="ajouter_detail" value="1">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="region" class="form-label">Région</label>
                            <select class="form-select" id="region" name="region" required>
                                <option value="">Sélectionner une région</option>
                                {% for region in regions %}
                                <option value="{{ region.nom_region }}">{{ region.nom_region }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="nom_site" class="form-label">Nom du site</label>
                            <select class="form-select" id="nom_site" name="nom_site" required>
                                <option value="">Sélectionner un site</option>
                                {% for site in sites %}
                                <option value="{{ site.site }}" data-region="{{ site.region }}">{{ site.site }}</option>
                                {% endfor %}
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="nbr_systeme" class="form-label">Nombre de système</label>
                            <input type="number" class="form-control" id="nbr_systeme" name="nbr_systeme" value="1" min="1">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="date_intervention" class="form-label">Date d'intervention</label>
                            <input type="date" class="form-control" id="date_intervention" name="date_intervention" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="technicien" class="form-label">Technicien</label>
                            <input type="text" class="form-control" id="technicien" name="technicien" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="situation" class="form-label">Situation</label>
                            <select class="form-select" id="situation" name="situation" required>
                                <option value="">Sélectionner une situation</option>
                                <option value="Réglé">Réglé</option>
                                <option value="Pas encore">Pas encore</option>
                                <option value="Problème">Problème</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="etat_materiel" class="form-label">État du matériel</label>
                            <select class="form-select" id="etat_materiel" name="etat_materiel" required>
                                <option value="">Sélectionner un état</option>
                                <option value="Opérationnel">Opérationnel</option>
                                <option value="Non Opérationnel">Non Opérationnel</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="observation" class="form-label">Observation</label>
                            <textarea class="form-control" id="observation" name="observation" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="telephone_chef_site" class="form-label">Téléphone chef de site</label>
                            <input type="text" class="form-control" id="telephone_chef_site" name="telephone_chef_site">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="telephone_securite" class="form-label">Téléphone de sécurité</label>
                            <input type="text" class="form-control" id="telephone_securite" name="telephone_securite">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="technicien_contact" class="form-label">Technicien à contacter</label>
                            <input type="text" class="form-control" id="technicien_contact" name="technicien_contact">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="gps" class="form-label">GPS</label>
                            <input type="text" class="form-control" id="gps" name="gps" placeholder="Ex: 31.6295, -7.9811">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modals pour afficher les détails -->
{% if details %}
    {% for detail in details %}
    <div class="modal fade" id="viewDetailModal{{ detail.id }}" tabindex="-1" aria-labelledby="viewDetailModalLabel{{ detail.id }}" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="viewDetailModalLabel{{ detail.id }}">Détail #{{ detail.id }} - {{ detail.nom_site }}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Région:</strong> {{ detail.region }}</p>
                            <p><strong>Site:</strong> {{ detail.nom_site }}</p>
                            <p><strong>Nombre de systèmes:</strong> {{ detail.nbr_systeme }}</p>
                            <p><strong>Date d'intervention:</strong> {{ detail.date_intervention }}</p>
                            <p><strong>Technicien:</strong> {{ detail.technicien }}</p>
                            <p><strong>Situation:</strong>
                                {% if detail.situation == 'Réglé' %}
                                <span class="badge bg-success">Réglé</span>
                                {% elif detail.situation == 'Pas encore' %}
                                <span class="badge bg-warning">Pas encore</span>
                                {% elif detail.situation == 'Problème' %}
                                <span class="badge bg-danger">Problème</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ detail.situation }}</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>État du matériel:</strong>
                                {% if detail.etat_materiel == 'Opérationnel' %}
                                <span class="badge bg-success">Opérationnel</span>
                                {% elif detail.etat_materiel == 'Non Opérationnel' %}
                                <span class="badge bg-danger">Non Opérationnel</span>
                                {% else %}
                                <span class="badge bg-secondary">{{ detail.etat_materiel }}</span>
                                {% endif %}
                            </p>
                            <p><strong>Téléphone chef de site:</strong> {{ detail.telephone_chef_site }}</p>
                            <p><strong>Téléphone sécurité:</strong> {{ detail.telephone_securite }}</p>
                            <p><strong>Technicien à contacter:</strong> {{ detail.technicien_contact }}</p>
                            <p><strong>GPS:</strong> {{ detail.gps }}</p>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Observation:</h6>
                            <p class="border p-2 rounded bg-light">{{ detail.observation }}</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <a href="{{ url_for('modifier_detail_intervention', id=detail.id) }}" class="btn btn-warning">
                        <i class="fas fa-edit"></i> Modifier
                    </a>
                </div>
            </div>
        </div>
    </div>
    {% endfor %}
{% endif %}
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Définir la date du jour par défaut
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('date_intervention').value = today;

        // Filtrage des sites selon la région sélectionnée
        const regionSelect = document.getElementById('region');
        const siteSelect = document.getElementById('nom_site');
        const allSiteOptions = Array.from(siteSelect.options);

        regionSelect.addEventListener('change', function() {
            const selectedRegion = this.value;

            // Vider la liste des sites
            siteSelect.innerHTML = '<option value="">Sélectionner un site</option>';

            if (selectedRegion) {
                // Filtrer et ajouter les sites de la région sélectionnée
                allSiteOptions.forEach(option => {
                    if (option.dataset.region === selectedRegion) {
                        siteSelect.appendChild(option.cloneNode(true));
                    }
                });
            } else {
                // Ajouter tous les sites si aucune région n'est sélectionnée
                allSiteOptions.forEach(option => {
                    if (option.value !== '') {
                        siteSelect.appendChild(option.cloneNode(true));
                    }
                });
            }
        });
    });


</script>
{% endblock %}
