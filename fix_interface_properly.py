#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح الواجهة بشكل صحيح - استعادة الشعار والفرنسية
"""

import sqlite3
import os
import shutil

def restore_original_data():
    """استعادة البيانات الأصلية مع إخفاء MAXAFFAIRE فقط من العرض"""
    
    print("🔄 استعادة البيانات الأصلية...")
    print("=" * 40)
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()
        
        # حذف البيانات الحالية
        cursor.execute("DELETE FROM societe")
        
        # إدراج البيانات الأصلية (مع MAXAFFAIRE في قاعدة البيانات)
        print("📝 استعادة البيانات الأصلية...")
        cursor.execute('''
        INSERT INTO societe (nom, logo, telephone, responsable, email, adresse, if_fiscal, rc, patente, ice, cnss, pied_page)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            'MAXAFFAIRE',  # الاسم الأصلي في قاعدة البيانات
            'logo_44b3172a.jpg',  # الشعار الأصلي
            '+212 537 29 50 31',  # الهاتف الأصلي
            'nihad bouazzaoui',  # المسؤول الأصلي
            '<EMAIL>',  # البريد الأصلي
            '',  # العنوان
            '7547457',  # IF الأصلي
            '574574584',  # RC الأصلي
            '767978',  # patente الأصلي
            '11223344',  # ICE الأصلي
            '4635744',  # CNSS الأصلي
            'Système de Gestion de Maintenance'   # تذييل بالفرنسية
        ))
        
        # حفظ التغييرات
        conn.commit()
        print("✅ تم استعادة البيانات الأصلية")
        
        # عرض البيانات المستعادة
        print("\n📊 البيانات المستعادة:")
        cursor.execute("SELECT nom, email, telephone, logo FROM societe LIMIT 1")
        row = cursor.fetchone()
        if row:
            print(f"   📛 الاسم: {row[0]}")
            print(f"   📧 البريد: {row[1]}")
            print(f"   📞 الهاتف: {row[2]}")
            print(f"   🖼️ الشعار: {row[3]}")
        
        conn.close()
        
        # استعادة الشعار الأصلي
        print("\n🖼️ استعادة الشعار الأصلي...")
        original_logo_path = 'static/uploads/logo_44b3172a.jpg'
        
        # إنشاء مجلد uploads إذا لم يكن موجود
        os.makedirs('static/uploads', exist_ok=True)
        
        # نسخ الشعار الافتراضي كشعار أصلي إذا لم يكن موجود
        if not os.path.exists(original_logo_path):
            default_logo = 'static/images/logo.png'
            if os.path.exists(default_logo):
                shutil.copy2(default_logo, original_logo_path)
                print(f"   ✅ تم إنشاء الشعار الأصلي: {original_logo_path}")
            else:
                print(f"   ⚠️ الشعار الافتراضي غير موجود: {default_logo}")
        else:
            print(f"   ✅ الشعار الأصلي موجود: {original_logo_path}")
        
        print("\n✅ تم استعادة جميع البيانات الأصلية!")
        
    except Exception as e:
        print(f"❌ خطأ في استعادة البيانات: {e}")

def update_templates_display_only():
    """تحديث القوالب لإخفاء MAXAFFAIRE من العرض فقط"""
    
    print("\n🎨 تحديث عرض القوالب...")
    
    # تحديث قالب تسجيل الدخول
    login_template = 'templates/login.html'
    if os.path.exists(login_template):
        with open(login_template, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # استبدال عرض اسم الشركة فقط
        old_line = "{{ company_info.nom if company_info and company_info.nom else 'Gestion de Maintenance' }}"
        new_line = "{{ 'Gestion de Maintenance' if company_info and company_info.nom == 'MAXAFFAIRE' else (company_info.nom if company_info and company_info.nom else 'Gestion de Maintenance') }}"
        
        content = content.replace(old_line, new_line)
        
        with open(login_template, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("   ✅ تم تحديث قالب تسجيل الدخول")
    
    # تحديث القالب الأساسي
    base_template = 'templates/base.html'
    if os.path.exists(base_template):
        with open(base_template, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # استبدال عرض اسم الشركة في الشريط الجانبي
        old_line = "{{ company_info.nom if company_info and company_info.nom else 'Gestion de Maintenance' }}"
        new_line = "{{ 'Gestion de Maintenance' if company_info and company_info.nom == 'MAXAFFAIRE' else (company_info.nom if company_info and company_info.nom else 'Gestion de Maintenance') }}"
        
        content = content.replace(old_line, new_line)
        
        with open(base_template, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("   ✅ تم تحديث القالب الأساسي")
    
    # تحديث قوالب التقارير
    rapport_template = 'templates/rapport_base.html'
    if os.path.exists(rapport_template):
        with open(rapport_template, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # استبدال عرض اسم الشركة في التقارير
        old_line = "{{ company_info.nom if company_info and company_info.nom else 'Votre Société' }}"
        new_line = "{{ 'Votre Société' if company_info and company_info.nom == 'MAXAFFAIRE' else (company_info.nom if company_info and company_info.nom else 'Votre Société') }}"
        
        content = content.replace(old_line, new_line)
        
        with open(rapport_template, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("   ✅ تم تحديث قالب التقارير")

def verify_restoration():
    """التحقق من استعادة البيانات"""
    
    print("\n🔍 التحقق من الاستعادة...")
    
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("SELECT * FROM societe LIMIT 1")
        company = cursor.fetchone()
        
        if company:
            print("   📊 البيانات في قاعدة البيانات:")
            print(f"      📛 الاسم: {company['nom']}")
            print(f"      📧 البريد: {company['email']}")
            print(f"      📞 الهاتف: {company['telephone']}")
            print(f"      🖼️ الشعار: {company['logo']}")
            print(f"      👤 المسؤول: {company['responsable']}")
            
            # فحص الشعار
            if company['logo']:
                logo_path = f"static/uploads/{company['logo']}"
                if os.path.exists(logo_path):
                    print(f"      ✅ ملف الشعار موجود: {logo_path}")
                else:
                    print(f"      ❌ ملف الشعار غير موجود: {logo_path}")
            
            print("\n   🎭 العرض في الواجهة:")
            if company['nom'] == 'MAXAFFAIRE':
                print("      📛 سيظهر: 'Gestion de Maintenance' (بدلاً من MAXAFFAIRE)")
            else:
                print(f"      📛 سيظهر: {company['nom']}")
        else:
            print("   ❌ لا توجد بيانات شركة")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ خطأ في التحقق: {e}")

if __name__ == "__main__":
    restore_original_data()
    update_templates_display_only()
    verify_restoration()
    
    print("\n🎉 تم الإصلاح بشكل صحيح!")
    print("=" * 40)
    print("\n💡 الآن:")
    print("   • البيانات الأصلية محفوظة في قاعدة البيانات")
    print("   • الشعار الأصلي مستعاد")
    print("   • المعلومات الأصلية محفوظة")
    print("   • MAXAFFAIRE مخفي من العرض فقط")
    print("   • الواجهة بالفرنسية كما كانت")
    print("   • وظيفة 'Mot de passe oublié?' تعمل")
