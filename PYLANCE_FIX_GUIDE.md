# 🔧 دليل إصلاح مشكلة Pylance - Import "werkzeug.security" could not be resolved

## 🎯 المشكلة
VS Code/Pylance يظهر خطأ: `Import "werkzeug.security" could not be resolved`

## ✅ الحل السريع

### الخطوة 1: تأكد من تفعيل البيئة الافتراضية
```bash
# في Terminal
.venv\Scripts\activate
```

### الخطوة 2: اختيار Python Interpreter في VS Code
1. اضغط `Ctrl + Shift + P`
2. اكتب `Python: Select Interpreter`
3. اختر `./.venv/Scripts/python.exe`

### الخطوة 3: إعادة تحميل VS Code
1. اضغط `Ctrl + Shift + P`
2. اكتب `Developer: Reload Window`
3. أو أغلق وأعد فتح VS Code

## 🛠️ الحلول المتقدمة

### إذا لم تعمل الخطوات السابقة:

#### الحل 1: تحديث إعدادات VS Code
تم إنشاء ملف `.vscode/settings.json` مع الإعدادات الصحيحة:
```json
{
    "python.defaultInterpreter": "./.venv/Scripts/python.exe",
    "python.pythonPath": "./.venv/Scripts/python.exe",
    "python.terminal.activateEnvironment": true
}
```

#### الحل 2: تكوين Pyright
تم إنشاء ملف `pyrightconfig.json` مع التكوين الصحيح:
```json
{
    "venvPath": ".",
    "venv": ".venv",
    "pythonVersion": "3.11"
}
```

#### الحل 3: إعادة تثبيت المكتبات
```bash
# تفعيل البيئة الافتراضية
.venv\Scripts\activate

# إعادة تثبيت المكتبات
pip install --force-reinstall werkzeug flask

# أو إعادة تثبيت جميع المتطلبات
pip install --force-reinstall -r requirements.txt
```

## 🔍 التحقق من الحل

### اختبار في Terminal:
```bash
python -c "from werkzeug.security import generate_password_hash; print('werkzeug works!')"
```

### اختبار في VS Code:
1. افتح ملف `app.py`
2. تأكد من عدم وجود خطوط حمراء تحت `from werkzeug.security import`
3. يجب أن يعمل الـ IntelliSense بشكل صحيح

## 🚨 مشاكل شائعة وحلولها

### المشكلة: "Python interpreter not found"
**الحل:**
```bash
# تأكد من وجود البيئة الافتراضية
ls .venv/Scripts/python.exe

# إذا لم توجد، أنشئها مرة أخرى
python -m venv .venv
.venv\Scripts\activate
pip install -r requirements.txt
```

### المشكلة: "Multiple Python interpreters"
**الحل:**
1. `Ctrl + Shift + P`
2. `Python: Clear Cache and Reload Window`
3. اختر المفسر الصحيح مرة أخرى

### المشكلة: "Pylance server crashed"
**الحل:**
1. `Ctrl + Shift + P`
2. `Python: Restart Language Server`
3. أو `Developer: Reload Window`

## 📋 قائمة التحقق النهائية

- [ ] البيئة الافتراضية مفعلة
- [ ] Python Interpreter محدد بشكل صحيح
- [ ] werkzeug مثبت في البيئة الافتراضية
- [ ] VS Code تم إعادة تحميله
- [ ] لا توجد أخطاء في ملف app.py
- [ ] IntelliSense يعمل بشكل صحيح

## 🎯 ملاحظات مهمة

1. **استخدم دائماً البيئة الافتراضية**: `.venv\Scripts\activate`
2. **تأكد من اختيار المفسر الصحيح** في VS Code
3. **أعد تحميل VS Code** بعد أي تغيير في الإعدادات
4. **لا تحذف ملفات التكوين** (.vscode/settings.json, pyrightconfig.json)

## ✅ النتيجة المتوقعة

بعد تطبيق هذه الخطوات:
- ✅ لا توجد أخطاء Pylance
- ✅ IntelliSense يعمل بشكل مثالي
- ✅ جميع الاستيرادات تعمل بشكل صحيح
- ✅ البرنامج يعمل بدون مشاكل

---

**💡 نصيحة**: إذا استمرت المشكلة، أعد تشغيل VS Code بالكامل وتأكد من تفعيل البيئة الافتراضية قبل فتح المشروع.
