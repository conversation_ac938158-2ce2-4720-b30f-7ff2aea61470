#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

# اختبار تسجيل الدخول أولاً
session = requests.Session()

print("🔍 اختبار تسجيل الدخول...")

# 1. تسجيل الدخول
login_url = 'http://127.0.0.1:5000/login'
login_data = {
    'username': 'admin',
    'password': 'admin123'
}

try:
    # محاولة تسجيل الدخول
    login_response = session.post(login_url, data=login_data)
    print(f"Login Status: {login_response.status_code}")
    
    if login_response.status_code == 200:
        print("✅ تم تسجيل الدخول بنجاح")
        
        # 2. اختبار route استيراد المواقع
        import_url = 'http://127.0.0.1:5000/api/import/sites'
        test_data = {
            'data': [
                {
                    'NOM DU SITE': 'Site Test 1',
                    'ADRESSE': '123 Rue Test, Casablanca',
                    'TÉLÉPHONE': '+212 522 123456',
                    'RESPONSABLE': '<PERSON>',
                    'EMAIL': '<EMAIL>'
                }
            ]
        }
        
        print(f"\n🔍 اختبار route استيراد المواقع...")
        import_response = session.post(
            import_url,
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Import Status: {import_response.status_code}")
        print(f"Import Response: {import_response.text}")
        
        if import_response.status_code == 200:
            print("✅ نجح الاستيراد!")
        else:
            print("❌ فشل الاستيراد")
            
    else:
        print("❌ فشل تسجيل الدخول")
        print(f"Response: {login_response.text[:200]}")
        
except Exception as e:
    print(f"❌ خطأ: {e}")

print("\n🎯 انتهى الاختبار!")
