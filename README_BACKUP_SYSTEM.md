# 🔄 Système de Sauvegarde Automatique - Application de Maintenance

## 📋 Vue d'ensemble

Ce système offre une solution complète de sauvegarde automatique et manuelle pour l'application de gestion de maintenance. Il permet de programmer des sauvegardes automatiques quotidiennes, hebdomadaires et mensuelles, ainsi que d'effectuer des sauvegardes manuelles et d'importer/exporter des données.

## ✨ Fonctionnalités

### 🔄 Sauvegarde Automatique
- **Sauvegarde Quotidienne** : Programmable à une heure spécifique
- **Sauvegarde Hebdomadaire** : Programmable un jour spécifique de la semaine
- **Sauvegarde Mensuelle** : Programmable un jour spécifique du mois
- **Nettoyage Automatique** : Suppression automatique des anciennes sauvegardes selon la taille limite

### 📤 Export de Données
- **Format SQL** : Copie complète de la base de données SQLite
- **Format Excel** : Export de toutes les tables dans un fichier Excel multi-feuilles
- **Format JSON** : Export structuré en format JSON

### 📥 Import de Données
- **Base de données SQLite** : Restauration complète ou partielle
- **Fichiers Excel** : Import depuis des fichiers Excel structurés
- **Fichiers JSON** : Import depuis des fichiers JSON
- **Types d'import** :
  - Remplacer toutes les données
  - Ajouter aux données existantes
  - Mettre à jour les données existantes

### 📊 Gestion et Monitoring
- **Historique des sauvegardes** : Visualisation de toutes les sauvegardes créées
- **Statistiques en temps réel** : Nombre d'enregistrements par table
- **Interface intuitive** : Configuration facile via interface web

## 🚀 Installation et Configuration

### Prérequis
```bash
pip install schedule pandas openpyxl
```

### Démarrage Automatique
Le système de sauvegarde se lance automatiquement avec l'application Flask :

```python
# Dans app.py
if __name__ == '__main__':
    # Le système de sauvegarde démarre automatiquement
    app.run(debug=True, host='0.0.0.0', port=5000)
```

### Configuration par Défaut
```json
{
    "daily_backup": {
        "enabled": true,
        "time": "02:00",
        "keep_days": 7
    },
    "weekly_backup": {
        "enabled": true,
        "day": "sunday",
        "time": "03:00",
        "keep_weeks": 4
    },
    "monthly_backup": {
        "enabled": true,
        "day": 1,
        "time": "04:00",
        "keep_months": 12
    },
    "auto_cleanup": {
        "enabled": true,
        "max_backup_size_gb": 5.0
    }
}
```

## 🎯 Utilisation

### Accès à l'Interface
1. Connectez-vous en tant qu'administrateur
2. Accédez à **Gestion Base de Données** dans le menu
3. URL directe : `http://127.0.0.1:5000/database-management`

### Configuration des Sauvegardes Automatiques

#### Sauvegarde Quotidienne
- ✅ **Activer** : Cocher la case pour activer
- 🕐 **Heure** : Définir l'heure d'exécution (format 24h)
- 📅 **Rétention** : Nombre de jours à conserver (1-30)

#### Sauvegarde Hebdomadaire
- ✅ **Activer** : Cocher la case pour activer
- 📅 **Jour** : Sélectionner le jour de la semaine
- 🕐 **Heure** : Définir l'heure d'exécution
- 📦 **Rétention** : Nombre de semaines à conserver (1-12)

#### Sauvegarde Mensuelle
- ✅ **Activer** : Cocher la case pour activer
- 📅 **Jour du mois** : Jour du mois (1-28)
- 🕐 **Heure** : Définir l'heure d'exécution
- 📦 **Rétention** : Nombre de mois à conserver (1-24)

#### Nettoyage Automatique
- ✅ **Activer** : Cocher la case pour activer
- 💾 **Taille maximale** : Limite en GB (1-50)

### Export Manuel

#### Export SQL
```
Bouton : "Exporter en SQL"
Résultat : Fichier .db (copie complète de la base)
Usage : Sauvegarde complète, restauration
```

#### Export Excel
```
Bouton : "Exporter en Excel"
Résultat : Fichier .xlsx avec toutes les tables
Usage : Analyse, reporting, partage
```

#### Export JSON
```
Bouton : "Exporter en JSON"
Résultat : Fichier .json structuré
Usage : Intégration, API, développement
```

### Import de Données

#### Étapes d'Import
1. **Sélectionner le fichier** : Formats acceptés (.db, .xlsx, .json)
2. **Choisir le type d'import** :
   - **Remplacer** : Supprime toutes les données existantes
   - **Ajouter** : Ajoute aux données existantes
   - **Mettre à jour** : Met à jour les enregistrements existants
3. **Confirmer l'import** : Cliquer sur "Importer les Données"

⚠️ **Attention** : Il est recommandé de faire une sauvegarde avant tout import

## 📁 Structure des Fichiers

```
📦 Application
├── 📄 app.py                    # Application principale
├── 📄 backup_scheduler.py       # Système de sauvegarde automatique
├── 📄 backup_config.json        # Configuration des sauvegardes
├── 📄 backup_scheduler.log      # Journal des sauvegardes
├── 📂 backups/                  # Dossier des sauvegardes
│   ├── 📄 maintenance_daily_*.db
│   ├── 📄 maintenance_weekly_*.db
│   ├── 📄 maintenance_monthly_*.db
│   └── 📄 maintenance_manual_*.db
├── 📂 uploads/                  # Dossier temporaire pour imports
└── 📂 templates/
    └── 📄 database_management.html
```

## 🔧 API Endpoints

### Export
```
GET /api/export/sql     # Export base de données
GET /api/export/excel   # Export Excel
GET /api/export/json    # Export JSON
```

### Import
```
POST /api/import        # Import de fichier
```

### Configuration
```
GET  /api/backup-config    # Récupérer la configuration
POST /api/backup-config    # Sauvegarder la configuration
```

### Historique
```
GET /api/backup-history    # Historique des sauvegardes
GET /api/manual-backup     # Créer une sauvegarde manuelle
```

## 📊 Monitoring et Logs

### Journal des Activités
Le système enregistre toutes les activités dans :
- **Base de données** : Table `logs_utilisateurs`
- **Fichier log** : `backup_scheduler.log`

### Types d'événements enregistrés
- ✅ Sauvegardes réussies
- ❌ Échecs de sauvegarde
- 🔄 Modifications de configuration
- 🗑️ Nettoyages automatiques
- 📥 Imports/Exports

## 🛡️ Sécurité

### Contrôle d'Accès
- **Administrateurs uniquement** : Seuls les utilisateurs avec le rôle `admin` peuvent accéder
- **Authentification requise** : Connexion obligatoire
- **Journalisation** : Toutes les actions sont enregistrées

### Validation des Fichiers
- **Types autorisés** : .db, .xlsx, .xls, .json uniquement
- **Taille limitée** : Limite configurable
- **Vérification d'intégrité** : Validation des fichiers importés

## 🔍 Dépannage

### Problèmes Courants

#### Le planificateur ne démarre pas
```bash
# Vérifier les logs
tail -f backup_scheduler.log

# Vérifier les dépendances
pip install schedule pandas openpyxl
```

#### Erreur d'export
```bash
# Vérifier les permissions du dossier backups
mkdir -p backups
chmod 755 backups
```

#### Erreur d'import
```bash
# Vérifier le format du fichier
# Vérifier la taille du fichier
# Vérifier les permissions
```

### Logs Utiles
```bash
# Journal du planificateur
tail -f backup_scheduler.log

# Journal de l'application
# Voir la console Flask pour les erreurs
```

## 📈 Bonnes Pratiques

### Configuration Recommandée
- **Sauvegarde quotidienne** : 02:00, conserver 7 jours
- **Sauvegarde hebdomadaire** : Dimanche 03:00, conserver 4 semaines
- **Sauvegarde mensuelle** : 1er du mois 04:00, conserver 12 mois
- **Nettoyage automatique** : Activé, limite 5 GB

### Stratégie de Sauvegarde
1. **3-2-1 Rule** : 3 copies, 2 supports différents, 1 hors site
2. **Test régulier** : Tester les restaurations périodiquement
3. **Monitoring** : Surveiller les logs et l'espace disque
4. **Documentation** : Maintenir une documentation des procédures

### Maintenance
- **Vérification mensuelle** : Contrôler l'espace disque
- **Test de restauration** : Tester trimestriellement
- **Mise à jour** : Maintenir les dépendances à jour
- **Surveillance** : Monitorer les logs d'erreur

## 🆘 Support

Pour toute question ou problème :
1. Consulter les logs : `backup_scheduler.log`
2. Vérifier la configuration : `backup_config.json`
3. Tester manuellement : Bouton "Sauvegarde Manuelle"
4. Redémarrer le service si nécessaire

---

**Version** : 1.0  
**Dernière mise à jour** : Janvier 2025  
**Compatibilité** : Python 3.7+, Flask 2.0+
