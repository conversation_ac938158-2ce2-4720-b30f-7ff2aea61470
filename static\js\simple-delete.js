/**
 * نظام حذف بسيط وفعال
 * Simple and effective delete system
 */

console.log('🗑️ تحميل نظام الحذف البسيط...');

// دالة الحذف الرئيسية
function confirmDelete(itemName, deleteUrl) {
    console.log('confirmDelete called:', itemName, deleteUrl);
    
    // إنشاء حوار تأكيد مخصص
    const modal = document.createElement('div');
    modal.className = 'delete-confirmation-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10000;
        animation: fadeIn 0.3s ease;
    `;
    
    modal.innerHTML = `
        <div style="
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease;
        ">
            <div style="color: #dc3545; font-size: 48px; margin-bottom: 20px;">
                <i class="fas fa-exclamation-triangle"></i>
            </div>
            <h4 style="color: #333; margin-bottom: 15px;">تأكيد الحذف</h4>
            <p style="color: #666; margin-bottom: 25px; font-size: 16px;">
                هل أنت متأكد من حذف: <strong style="color: #dc3545;">${itemName}</strong>؟
                <br><br>
                <small style="color: #999;">هذا الإجراء لا يمكن التراجع عنه</small>
            </p>
            <div style="display: flex; gap: 15px; justify-content: center;">
                <button onclick="cancelDelete()" style="
                    background: #6c757d;
                    color: white;
                    border: none;
                    padding: 12px 25px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: all 0.3s;
                ">
                    <i class="fas fa-times"></i> إلغاء
                </button>
                <button onclick="proceedDelete('${deleteUrl}')" style="
                    background: #dc3545;
                    color: white;
                    border: none;
                    padding: 12px 25px;
                    border-radius: 8px;
                    cursor: pointer;
                    font-size: 14px;
                    transition: all 0.3s;
                ">
                    <i class="fas fa-trash"></i> حذف
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    
    // إضافة الأنماط
    if (!document.getElementById('delete-modal-styles')) {
        const styles = document.createElement('style');
        styles.id = 'delete-modal-styles';
        styles.textContent = `
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
            @keyframes slideIn {
                from { transform: translateY(-50px); opacity: 0; }
                to { transform: translateY(0); opacity: 1; }
            }
            .delete-confirmation-modal button:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            }
        `;
        document.head.appendChild(styles);
    }
    
    // حفظ المرجع للحذف لاحقاً
    window.currentDeleteModal = modal;
}

// دالة إلغاء الحذف
function cancelDelete() {
    if (window.currentDeleteModal) {
        window.currentDeleteModal.style.animation = 'fadeOut 0.3s ease';
        setTimeout(() => {
            if (window.currentDeleteModal && window.currentDeleteModal.parentNode) {
                window.currentDeleteModal.parentNode.removeChild(window.currentDeleteModal);
            }
            window.currentDeleteModal = null;
        }, 300);
    }
}

// دالة تنفيذ الحذف
function proceedDelete(deleteUrl) {
    // إغلاق الحوار
    cancelDelete();
    
    // عرض مؤشر التحميل
    showLoadingIndicator();
    
    // الانتقال إلى رابط الحذف
    window.location.href = deleteUrl;
}

// عرض مؤشر التحميل
function showLoadingIndicator() {
    const loading = document.createElement('div');
    loading.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10001;
    `;
    
    loading.innerHTML = `
        <div style="
            background: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        ">
            <div style="color: #dc3545; font-size: 32px; margin-bottom: 15px;">
                <i class="fas fa-spinner fa-spin"></i>
            </div>
            <div style="color: #333; font-size: 16px;">جاري الحذف...</div>
        </div>
    `;
    
    document.body.appendChild(loading);
}

// إضافة دعم مفتاح Escape
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape' && window.currentDeleteModal) {
        cancelDelete();
    }
});

// إضافة دعم النقر خارج الحوار
document.addEventListener('click', function(e) {
    if (window.currentDeleteModal && e.target === window.currentDeleteModal) {
        cancelDelete();
    }
});

console.log('✅ نظام الحذف البسيط جاهز');
