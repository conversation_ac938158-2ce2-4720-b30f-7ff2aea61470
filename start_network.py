#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل مباشر وبسيط للوضع الشبكي
بدون أي تعقيدات أو ملفات خارجية
"""

import os
import sys
import socket

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "*************"

def main():
    """تشغيل مباشر للوضع الشبكي"""
    
    print("🌐 تشغيل نظام إدارة الصيانة - الوضع الشبكي")
    print("=" * 60)
    
    # الحصول على عنوان IP المحلي
    local_ip = get_local_ip()
    port = 5000
    
    print(f"📡 إعدادات الشبكة:")
    print(f"   🔌 المنفذ: {port}")
    print(f"   🌐 المضيف: 0.0.0.0 (جميع الشبكات)")
    print(f"   📍 عنوان IP المحلي: {local_ip}")
    print()
    
    print(f"🔗 روابط الوصول:")
    print(f"   🏠 محلي: http://127.0.0.1:{port}")
    print(f"   🌍 شبكي: http://{local_ip}:{port}")
    print(f"   📱 هاتف: http://{local_ip}:{port}")
    print()
    
    print(f"📋 للوصول من أجهزة أخرى:")
    print(f"   1. تأكد من اتصال الجهاز بنفس الشبكة")
    print(f"   2. افتح المتصفح واذهب إلى: http://{local_ip}:{port}")
    print(f"   3. استخدم admin/admin123 لتسجيل الدخول")
    print()
    
    # إضافة مسار التطبيق
    if getattr(sys, 'frozen', False):
        app_dir = os.path.dirname(sys.executable)
    else:
        app_dir = os.path.dirname(os.path.abspath(__file__))
    
    sys.path.insert(0, app_dir)
    
    try:
        # استيراد وتشغيل التطبيق
        from app import app
        
        print("🚀 تشغيل الخادم...")
        print("🌍 يمكن الوصول من أي جهاز في الشبكة المحلية")
        print("⏹️ لإيقاف النظام: اضغط Ctrl+C")
        print("=" * 60)
        
        # تشغيل التطبيق مع إعدادات شبكية مباشرة
        app.run(
            host='0.0.0.0',    # قبول الاتصالات من أي جهاز
            port=port,         # المنفذ 5000
            debug=False,       # بدون debug
            threaded=True,     # دعم عدة مستخدمين
            use_reloader=False # بدون إعادة تحميل
        )
        
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف النظام بواسطة المستخدم")
    except ImportError as e:
        print(f"\n❌ خطأ في استيراد التطبيق: {e}")
        print("💡 تأكد من وجود ملف app.py في نفس المجلد")
        input("\nاضغط Enter للخروج...")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل النظام: {e}")
        print("\n💡 تأكد من:")
        print("   - عدم استخدام المنفذ 5000 من برنامج آخر")
        print("   - تثبيت جميع المكتبات المطلوبة")
        print("   - صحة ملفات التطبيق")
        input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
