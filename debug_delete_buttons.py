#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص فوري لمشكلة أزرار الحذف
Immediate diagnosis for delete buttons issue
"""

import os

def check_files():
    """فحص الملفات المطلوبة"""
    
    print("🔍 فحص الملفات...")
    
    files_to_check = [
        'static/js/direct-delete.js',
        'templates/base.html',
        'templates/utilisateurs.html',
        'templates/marches.html'
    ]
    
    for file_path in files_to_check:
        if os.path.exists(file_path):
            size = os.path.getsize(file_path)
            print(f"   ✅ {file_path} ({size} bytes)")
        else:
            print(f"   ❌ {file_path} - غير موجود")

def check_base_html():
    """فحص base.html"""
    
    print("\n🔍 فحص base.html...")
    
    try:
        with open('templates/base.html', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('direct-delete.js', 'ملف الحذف المباشر'),
            ('confirmDelete', 'دالة confirmDelete'),
            ('🚨 تحميل الحل المباشر', 'الحل المباشر في base.html')
        ]
        
        for check, description in checks:
            if check in content:
                print(f"   ✅ {description}")
            else:
                print(f"   ❌ {description} - غير موجود")
        
    except Exception as e:
        print(f"   ❌ خطأ في قراءة base.html: {e}")

def check_button_syntax():
    """فحص صيغة أزرار الحذف"""
    
    print("\n🔍 فحص صيغة أزرار الحذف...")
    
    templates = [
        'templates/utilisateurs.html',
        'templates/marches.html',
        'templates/interventions.html',
        'templates/reclamations.html',
        'templates/regions.html',
        'templates/sites.html'
    ]
    
    for template in templates:
        if os.path.exists(template):
            try:
                with open(template, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # البحث عن أزرار الحذف
                if 'confirmDelete(' in content:
                    print(f"   ✅ {template} - يحتوي على confirmDelete")
                    
                    # فحص الصيغة
                    import re
                    matches = re.findall(r'onclick="confirmDelete\([^)]+\)"', content)
                    if matches:
                        print(f"      📋 عدد الأزرار: {len(matches)}")
                        for i, match in enumerate(matches[:2]):  # عرض أول زرين فقط
                            print(f"      🔹 زر {i+1}: {match}")
                    else:
                        print(f"      ⚠️ لا توجد أزرار حذف صحيحة")
                else:
                    print(f"   ❌ {template} - لا يحتوي على confirmDelete")
                    
            except Exception as e:
                print(f"   ❌ خطأ في قراءة {template}: {e}")
        else:
            print(f"   ❌ {template} - غير موجود")

def create_test_page():
    """إنشاء صفحة اختبار بسيطة"""
    
    print("\n🔧 إنشاء صفحة اختبار...")
    
    test_html = '''<!DOCTYPE html>
<html>
<head>
    <title>اختبار أزرار الحذف</title>
    <meta charset="utf-8">
    <script>
        console.log('🚨 بدء اختبار أزرار الحذف...');
        
        function confirmDelete(itemName, deleteUrl) {
            console.log('🚨 confirmDelete called:', itemName, deleteUrl);
            alert('تم استدعاء confirmDelete بنجاح!\\nالعنصر: ' + itemName + '\\nالرابط: ' + deleteUrl);
            return false;
        }
        
        window.onload = function() {
            console.log('🚨 الصفحة محملة، confirmDelete متاح:', typeof confirmDelete);
        };
    </script>
</head>
<body>
    <h1>اختبار أزرار الحذف</h1>
    
    <h2>اختبار 1: زر بسيط</h2>
    <button onclick="confirmDelete('اختبار 1', '/test/delete/1')">حذف اختبار 1</button>
    
    <h2>اختبار 2: زر مع كلاس</h2>
    <button class="btn btn-danger" onclick="confirmDelete('اختبار 2', '/test/delete/2')">حذف اختبار 2</button>
    
    <h2>اختبار 3: رابط</h2>
    <a href="#" onclick="confirmDelete('اختبار 3', '/test/delete/3'); return false;">حذف اختبار 3</a>
    
    <script>
        console.log('🚨 نهاية الصفحة، جميع العناصر محملة');
    </script>
</body>
</html>'''
    
    try:
        with open('test_delete.html', 'w', encoding='utf-8') as f:
            f.write(test_html)
        print("   ✅ تم إنشاء test_delete.html")
        print("   🌐 افتح الملف في المتصفح لاختبار أزرار الحذف")
    except Exception as e:
        print(f"   ❌ خطأ في إنشاء صفحة الاختبار: {e}")

def main():
    """الدالة الرئيسية"""
    
    print("🚨 تشخيص فوري لمشكلة أزرار الحذف")
    print("🚨 Immediate Diagnosis for Delete Buttons Issue")
    print("=" * 70)
    
    # فحص الملفات
    check_files()
    
    # فحص base.html
    check_base_html()
    
    # فحص صيغة الأزرار
    check_button_syntax()
    
    # إنشاء صفحة اختبار
    create_test_page()
    
    print("\n🎯 التوصيات:")
    print("🎯 Recommendations:")
    print("=" * 50)
    
    print("1. 🌐 افتح test_delete.html في المتصفح")
    print("   Open test_delete.html in browser")
    
    print("2. 🔍 افتح Developer Tools (F12)")
    print("   Open Developer Tools (F12)")
    
    print("3. 🖱️ انقر على أزرار الاختبار")
    print("   Click on test buttons")
    
    print("4. 📋 راقب رسائل console")
    print("   Monitor console messages")
    
    print("5. 🚨 إذا لم تعمل أزرار الاختبار، المشكلة في المتصفح")
    print("   If test buttons don't work, the issue is in the browser")
    
    print("6. 🚨 إذا عملت أزرار الاختبار، المشكلة في القوالب")
    print("   If test buttons work, the issue is in templates")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
