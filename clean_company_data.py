#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تنظيف بيانات الشركة وإزالة المعلومات غير المرغوب فيها
"""

import sqlite3
import os

def clean_company_data():
    """تنظيف بيانات الشركة"""
    
    print("🧹 تنظيف بيانات الشركة")
    print("=" * 40)
    
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()
        
        # عرض البيانات الحالية
        print("\n📊 البيانات الحالية:")
        cursor.execute("SELECT * FROM societe")
        rows = cursor.fetchall()
        
        if rows:
            for i, row in enumerate(rows):
                print(f"   الصف {i+1}: {row}")
        else:
            print("   لا توجد بيانات")
        
        # حذف جميع البيانات الحالية
        print("\n🗑️ حذف البيانات الحالية...")
        cursor.execute("DELETE FROM societe")
        deleted_rows = cursor.rowcount
        print(f"   تم حذف {deleted_rows} صف(وف)")
        
        # إدراج بيانات نظيفة وافتراضية
        print("\n📝 إدراج بيانات نظيفة...")
        cursor.execute('''
        INSERT INTO societe (nom, logo, telephone, responsable, email, adresse, if_fiscal, rc, patente, ice, cnss, pied_page)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            '',  # اسم فارغ
            None,  # لا يوجد شعار
            '',  # هاتف فارغ
            '',  # مسؤول فارغ
            '',  # بريد فارغ
            '',  # عنوان فارغ
            '',  # IF فارغ
            '',  # RC فارغ
            '',  # patente فارغ
            '',  # ICE فارغ
            '',  # CNSS فارغ
            ''   # تذييل فارغ
        ))
        
        # حفظ التغييرات
        conn.commit()
        print("✅ تم إدراج البيانات النظيفة")
        
        # عرض البيانات الجديدة
        print("\n📊 البيانات بعد التنظيف:")
        cursor.execute("SELECT * FROM societe")
        rows = cursor.fetchall()
        for i, row in enumerate(rows):
            print(f"   الصف {i+1}: {row}")
        
        conn.close()
        
        # تنظيف مجلد uploads
        print("\n🗂️ تنظيف مجلد uploads...")
        uploads_dir = 'static/uploads'
        if os.path.exists(uploads_dir):
            files = os.listdir(uploads_dir)
            logo_files = [f for f in files if f.startswith('logo_')]
            
            if logo_files:
                for logo_file in logo_files:
                    file_path = os.path.join(uploads_dir, logo_file)
                    try:
                        os.remove(file_path)
                        print(f"   🗑️ تم حذف {logo_file}")
                    except Exception as e:
                        print(f"   ❌ خطأ في حذف {logo_file}: {e}")
            else:
                print("   📁 لا توجد ملفات شعار للحذف")
        else:
            print("   📁 مجلد uploads غير موجود")
        
        print("\n✅ تم تنظيف بيانات الشركة بنجاح!")
        print("💡 يمكنك الآن إدخال معلومات الشركة الجديدة من صفحة 'Informations de la Société'")
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف البيانات: {e}")

if __name__ == "__main__":
    clean_company_data()
