{% extends "base.html" %}

{% block title %}Ajouter un Utilisateur - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Ajouter un Utilisateur{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-user-plus"></i> Ajouter un nouvel utilisateur
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('ajouter_utilisateur') }}">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="nom" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="nom" name="nom" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="prenom" class="form-label">Prénom</label>
                            <input type="text" class="form-control" id="prenom" name="prenom" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="role" class="form-label">Rôle</label>
                            <select class="form-select" id="role" name="role" required>
                                <option value="user">Utilisateur</option>
                                <option value="admin">Administrateur</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="nom_utilisateur" class="form-label">Nom d'utilisateur</label>
                            <input type="text" class="form-control" id="nom_utilisateur" name="nom_utilisateur" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="mot_de_passe" class="form-label">Mot de passe</label>
                            <input type="password" class="form-control" id="mot_de_passe" name="mot_de_passe" required>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">Permissions</label>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm_societe" name="permissions[]" value="societe" checked>
                                    <label class="form-check-label" for="perm_societe">
                                        Informations Société
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm_marches" name="permissions[]" value="marches" checked>
                                    <label class="form-check-label" for="perm_marches">
                                        Marchés
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm_interventions" name="permissions[]" value="interventions" checked>
                                    <label class="form-check-label" for="perm_interventions">
                                        Interventions
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="perm_reclamations" name="permissions[]" value="reclamations" checked>
                                    <label class="form-check-label" for="perm_reclamations">
                                        Réclamations
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer
                        </button>
                        <a href="{{ url_for('utilisateurs') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Générer automatiquement un nom d'utilisateur à partir du prénom et du nom
    document.getElementById('prenom').addEventListener('input', generateUsername);
    document.getElementById('nom').addEventListener('input', generateUsername);
    
    function generateUsername() {
        const prenom = document.getElementById('prenom').value.toLowerCase();
        const nom = document.getElementById('nom').value.toLowerCase();
        
        if (prenom && nom) {
            const username = prenom.charAt(0) + nom.replace(/\s+/g, '');
            document.getElementById('nom_utilisateur').value = username;
        }
    }
</script>
{% endblock %}
