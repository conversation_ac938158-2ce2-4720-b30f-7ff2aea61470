[Setup]
; Basic Information
AppName=Maintenance Management System
AppVersion=1.0.0
AppPublisher=Maintenance Management Solutions
AppPublisherURL=https://maintenance-system.com
AppSupportURL=https://maintenance-system.com/support
AppUpdatesURL=https://maintenance-system.com/updates
AppCopyright=Copyright (C) 2024 Maintenance Management Solutions

; Installation Settings
DefaultDirName={autopf}\Maintenance Management System
DefaultGroupName=Maintenance Management System
AllowNoIcons=yes
OutputDir=installer_output
OutputBaseFilename=MaintenanceSystemSetup
SetupIconFile=maintenance_icon.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern

; System Requirements
MinVersion=6.1
ArchitecturesAllowed=x86 x64
ArchitecturesInstallIn64BitMode=x64

; Privileges
PrivilegesRequired=admin

; Appearance
DisableProgramGroupPage=yes
DisableReadyPage=no
DisableFinishedPage=no
DisableWelcomePage=no

; Uninstall
UninstallDisplayName=Maintenance Management System
UninstallDisplayIcon={app}\Maintenance_Management_System.exe
CreateUninstallRegKey=yes

[Languages]
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; OnlyBelowVersion: 6.1
Name: "multiuser"; Description: "Configuration multi-utilisateurs (accès réseau)"; GroupDescription: "Options de configuration"
Name: "servermode"; Description: "Mode serveur (pour plusieurs ordinateurs)"; GroupDescription: "Options de configuration"
Name: "configfirewall"; Description: "Configurer le pare-feu automatiquement"; GroupDescription: "Options de configuration"

[Files]
; Main executable
Source: "dist\Maintenance_Management_System.exe"; DestDir: "{app}"; Flags: ignoreversion
; Icon file
Source: "maintenance_icon.ico"; DestDir: "{app}"; Flags: ignoreversion
; Support files
Source: "dist\README.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "dist\تشغيل_النظام.bat"; DestDir: "{app}"; Flags: ignoreversion
Source: "تشغيل_مع_متصفح.bat"; DestDir: "{app}"; Flags: ignoreversion
Source: "تشغيل_شبكي.bat"; DestDir: "{app}"; Flags: ignoreversion
Source: "تشغيل_شبكي_مباشر.bat"; DestDir: "{app}"; Flags: ignoreversion
; Network configuration tools
Source: "network_config.py"; DestDir: "{app}"; Flags: ignoreversion
Source: "config_loader.py"; DestDir: "{app}"; Flags: ignoreversion
Source: "config.ini"; DestDir: "{app}"; Flags: ignoreversion
Source: "run_network_mode.py"; DestDir: "{app}"; Flags: ignoreversion
Source: "start_network.py"; DestDir: "{app}"; Flags: ignoreversion
Source: "dist\run_app.py"; DestDir: "{app}"; Flags: ignoreversion
; Database cleanup tools
Source: "create_clean_database.py"; DestDir: "{app}"; Flags: ignoreversion
Source: "تنظيف_قاعدة_البيانات.bat"; DestDir: "{app}"; Flags: ignoreversion
Source: "fix_database_issues.py"; DestDir: "{app}"; Flags: ignoreversion
; Documentation
Source: "BROWSER_FIX_GUIDE.md"; DestDir: "{app}\docs"; Flags: ignoreversion
Source: "SOLUTION_SUMMARY.md"; DestDir: "{app}\docs"; Flags: ignoreversion
Source: "INSTALLATION_GUIDE.md"; DestDir: "{app}\docs"; Flags: ignoreversion
Source: "NETWORK_TROUBLESHOOTING.md"; DestDir: "{app}\docs"; Flags: ignoreversion
; Database (if exists)
Source: "maintenance.db"; DestDir: "{app}"; Flags: ignoreversion skipifsourcedoesntexist
; Create data directory
Source: "static\uploads\*"; DestDir: "{app}\uploads"; Flags: ignoreversion createallsubdirs recursesubdirs skipifsourcedoesntexist

[Icons]
; Start Menu
Name: "{group}\Maintenance Management System"; Filename: "{app}\Maintenance_Management_System.exe"; WorkingDir: "{app}"; Comment: "Maintenance Management System"; IconFilename: "{app}\maintenance_icon.ico"
Name: "{group}\Run with Browser"; Filename: "{app}\تشغيل_مع_متصفح.bat"; WorkingDir: "{app}"; Comment: "Run system with browser"; IconFilename: "{app}\maintenance_icon.ico"
Name: "{group}\Network Mode"; Filename: "{app}\تشغيل_شبكي.bat"; WorkingDir: "{app}"; Comment: "Run in network mode for multiple users"; IconFilename: "{app}\maintenance_icon.ico"
Name: "{group}\Network Mode (Simple)"; Filename: "{app}\تشغيل_شبكي_مباشر.bat"; WorkingDir: "{app}"; Comment: "Simple direct network mode"; IconFilename: "{app}\maintenance_icon.ico"
Name: "{group}\Network Mode (Python)"; Filename: "python"; Parameters: """{app}\start_network.py"""; WorkingDir: "{app}"; Comment: "Python network mode execution"; IconFilename: "{app}\maintenance_icon.ico"
Name: "{group}\Network Configuration"; Filename: "python"; Parameters: """{app}\network_config.py"""; WorkingDir: "{app}"; Comment: "Configure network settings"
Name: "{group}\Clean Database"; Filename: "{app}\تنظيف_قاعدة_البيانات.bat"; WorkingDir: "{app}"; Comment: "Clean database and remove test data"
Name: "{group}\Fix Database Issues"; Filename: "python"; Parameters: """{app}\fix_database_issues.py"""; WorkingDir: "{app}"; Comment: "Fix database and application issues"
Name: "{group}\User Guide"; Filename: "{app}\docs\INSTALLATION_GUIDE.md"; Comment: "Installation and usage guide"
Name: "{group}\Network Troubleshooting"; Filename: "{app}\docs\NETWORK_TROUBLESHOOTING.md"; Comment: "Network troubleshooting guide"
Name: "{group}\Browser Fix Guide"; Filename: "{app}\docs\BROWSER_FIX_GUIDE.md"; Comment: "Browser opening fix guide"
Name: "{group}\{cm:UninstallProgram,Maintenance Management System}"; Filename: "{uninstallexe}"

; Desktop Icon
Name: "{autodesktop}\Maintenance Management System"; Filename: "{app}\Maintenance_Management_System.exe"; WorkingDir: "{app}"; Tasks: desktopicon; Comment: "Maintenance Management System"; IconFilename: "{app}\maintenance_icon.ico"

; Quick Launch
Name: "{userappdata}\Microsoft\Internet Explorer\Quick Launch\Maintenance Management System"; Filename: "{app}\Maintenance_Management_System.exe"; WorkingDir: "{app}"; Tasks: quicklaunchicon; IconFilename: "{app}\maintenance_icon.ico"

[Run]
; Run after installation
Filename: "{app}\Maintenance_Management_System.exe"; Description: "{cm:LaunchProgram,Maintenance Management System}"; Flags: nowait postinstall skipifsilent

[UninstallRun]
; Clean up processes before uninstall
Filename: "{cmd}"; Parameters: "/C taskkill /F /IM Maintenance_Management_System.exe"; Flags: runhidden

[Code]
procedure CurStepChanged(CurStep: TSetupStep);
var
  ConfigFile: string;
  ConfigContent: TStringList;
  InstallationType: string;
  MultiUser: string;
  ServerMode: string;
  ResultCode: Integer;
begin
  if CurStep = ssPostInstall then
  begin
    ConfigFile := ExpandConstant('{app}\config.ini');
    ConfigContent := TStringList.Create;
    try
      // Determine configuration based on user choices
      if WizardIsTaskSelected('multiuser') then
      begin
        InstallationType := '1';  // Network mode
        MultiUser := 'True';
      end
      else
      begin
        InstallationType := '0';  // Local mode
        MultiUser := 'False';
      end;

      if WizardIsTaskSelected('servermode') then
        ServerMode := 'True'
      else
        ServerMode := 'False';

      // Create configuration content
      ConfigContent.Add('[Settings]');
      ConfigContent.Add('InstallationType=' + InstallationType);
      ConfigContent.Add('Port=5000');
      ConfigContent.Add('MultiUser=' + MultiUser);
      ConfigContent.Add('ServerMode=' + ServerMode);
      ConfigContent.Add('Version=1.0.0');
      ConfigContent.Add('');
      ConfigContent.Add('[Network]');
      if WizardIsTaskSelected('multiuser') then
        ConfigContent.Add('Host=0.0.0.0')
      else
        ConfigContent.Add('Host=127.0.0.1');
      ConfigContent.Add('AllowExternalAccess=' + MultiUser);
      ConfigContent.Add('');
      ConfigContent.Add('[Database]');
      ConfigContent.Add('Path=maintenance.db');
      ConfigContent.Add('BackupEnabled=True');
      ConfigContent.Add('BackupInterval=24');

      // Save configuration
      ConfigContent.SaveToFile(ConfigFile);

      // Configure firewall if selected
      if WizardIsTaskSelected('configfirewall') then
      begin
        Exec('netsh', 'advfirewall firewall delete rule name="Maintenance System Port 5000"', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
        Exec('netsh', 'advfirewall firewall add rule name="Maintenance System Port 5000" dir=in action=allow protocol=TCP localport=5000', '', SW_HIDE, ewWaitUntilTerminated, ResultCode);
      end;

    finally
      ConfigContent.Free;
    end;
  end;
end;
