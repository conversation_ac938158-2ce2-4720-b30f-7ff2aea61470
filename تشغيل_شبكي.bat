@echo off
title تشغيل شبكي - نظام إدارة الصيانة
color 0A
echo.
echo ========================================
echo    تشغيل شبكي - نظام إدارة الصيانة
echo    Network Mode - Maintenance System
echo ========================================
echo.

echo 🌐 إعداد التشغيل الشبكي...
echo 🌐 Setting up network mode...

REM إنشاء ملف إعدادات شبكي
echo [Settings] > config.ini
echo InstallationType=1 >> config.ini
echo Port=5000 >> config.ini
echo MultiUser=True >> config.ini
echo ServerMode=True >> config.ini
echo Version=1.0.0 >> config.ini
echo. >> config.ini
echo [Network] >> config.ini
echo Host=0.0.0.0 >> config.ini
echo AllowExternalAccess=True >> config.ini
echo. >> config.ini
echo [Database] >> config.ini
echo Path=maintenance.db >> config.ini
echo BackupEnabled=True >> config.ini
echo BackupInterval=24 >> config.ini

echo ✅ تم إنشاء إعدادات التشغيل الشبكي
echo ✅ Network configuration created

echo.
echo 🔥 تكوين جدار الحماية...
echo 🔥 Configuring firewall...

REM تكوين جدار الحماية للمنفذ 5000
netsh advfirewall firewall delete rule name="Maintenance System Port 5000" >nul 2>&1
netsh advfirewall firewall add rule name="Maintenance System Port 5000" dir=in action=allow protocol=TCP localport=5000 >nul 2>&1

if errorlevel 1 (
    echo ⚠️ قد تحتاج لتشغيل هذا الملف كمدير لتكوين جدار الحماية
    echo ⚠️ You may need to run this file as administrator to configure firewall
) else (
    echo ✅ تم تكوين جدار الحماية بنجاح
    echo ✅ Firewall configured successfully
)

echo.
echo 📡 الحصول على عنوان IP المحلي...
echo 📡 Getting local IP address...

REM الحصول على عنوان IP المحلي
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4"') do (
    set "ip=%%a"
    goto :found_ip
)
:found_ip
set ip=%ip: =%

echo.
echo 🚀 تشغيل النظام...
echo 🚀 Starting system...
echo.
echo 📊 معلومات الاتصال:
echo 📊 Connection information:
echo    🏠 محلي / Local: http://127.0.0.1:5000
echo    🌐 شبكي / Network: http://%ip%:5000
echo.
echo 📋 تعليمات للأجهزة الأخرى:
echo 📋 Instructions for other devices:
echo    1. تأكد من أن الأجهزة متصلة بنفس الشبكة
echo    1. Make sure devices are on the same network
echo    2. افتح المتصفح واذهب إلى: http://%ip%:5000
echo    2. Open browser and go to: http://%ip%:5000
echo    3. استخدم admin/admin123 لتسجيل الدخول
echo    3. Use admin/admin123 to login
echo.
echo ⏰ انتظار تشغيل الخادم...
echo ⏰ Waiting for server to start...
echo.

REM تشغيل البرنامج
if exist "Maintenance_Management_System.exe" (
    start /B Maintenance_Management_System.exe
    echo ✅ تم تشغيل النظام بنجاح
    echo ✅ System started successfully
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    echo ❌ Executable file not found
    echo 💡 تأكد من وجود Maintenance_Management_System.exe في نفس المجلد
    echo 💡 Make sure Maintenance_Management_System.exe is in the same folder
    pause
    exit /b 1
)

echo.
echo 🎯 النظام يعمل الآن في الوضع الشبكي!
echo 🎯 System is now running in network mode!
echo.
echo 📱 للوصول من الهاتف المحمول:
echo 📱 To access from mobile phone:
echo    - تأكد من اتصال الهاتف بنفس الشبكة
echo    - Make sure phone is on same network
echo    - اذهب إلى: http://%ip%:5000
echo    - Go to: http://%ip%:5000
echo.
echo 🔄 لإيقاف النظام: أغلق هذه النافذة أو اضغط Ctrl+C
echo 🔄 To stop system: Close this window or press Ctrl+C
echo.

REM انتظار لمدة 5 ثوان ثم فتح المتصفح
timeout /t 5 /nobreak >nul
start http://127.0.0.1:5000

echo 🌐 تم فتح المتصفح تلقائياً
echo 🌐 Browser opened automatically
echo.
echo ✨ النظام جاهز للاستخدام!
echo ✨ System ready for use!
echo.

REM إبقاء النافذة مفتوحة
:loop
timeout /t 30 /nobreak >nul
echo 💓 النظام يعمل... %date% %time%
echo 💓 System running... %date% %time%
goto loop
