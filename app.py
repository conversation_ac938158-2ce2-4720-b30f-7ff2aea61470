from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify, send_from_directory
import os
import sys
import shutil
from werkzeug.security import check_password_hash, generate_password_hash
from werkzeug.utils import secure_filename
import sqlite3
from functools import wraps

import uuid
from forms import SocieteForm

# Support for PyInstaller
if getattr(sys, 'frozen', False):
    # Running as compiled executable
    application_path = sys._MEIPASS
    template_folder = os.path.join(application_path, 'templates')
    static_folder = os.path.join(application_path, 'static')
    os.chdir(application_path)
else:
    # Running as script
    application_path = os.path.dirname(os.path.abspath(__file__))
    template_folder = 'templates'
    static_folder = 'static'

app = Flask(__name__, template_folder=template_folder, static_folder=static_folder)
app.secret_key = os.urandom(24)
app.config['SECRET_KEY'] = app.secret_key  # Pour Flask-WTF
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 50 * 1024 * 1024  # 50MB max file size

# Assurez-vous que le dossier d'uploads existe
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

# Extensions de fichiers autorisées pour les images
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp', 'svg'}

def allowed_file(filename):
    """Vérifier si l'extension du fichier est autorisée"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def save_uploaded_file(file, prefix=''):
    """Sauvegarder un fichier téléchargé avec un nom unique"""
    try:
        if not file or not file.filename:
            print("❌ Aucun fichier fourni")
            return None

        if not allowed_file(file.filename):
            print(f"❌ Type de fichier non autorisé: {file.filename}")
            return None

        # Vérifier la taille du fichier (max 10MB)
        file.seek(0, 2)  # Aller à la fin du fichier
        file_size = file.tell()
        file.seek(0)  # Revenir au début

        if file_size > 10 * 1024 * 1024:  # 10MB
            print(f"❌ Fichier trop volumineux: {file_size} bytes (max 10MB)")
            return None

        # Générer un nom de fichier unique
        filename = secure_filename(file.filename)
        _, ext = os.path.splitext(filename)
        unique_filename = f"{prefix}_{uuid.uuid4().hex[:8]}{ext}"

        # Créer le dossier s'il n'existe pas
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)

        # Chemin complet du fichier
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], unique_filename)
        print(f"💾 Sauvegarde vers: {filepath}")

        # Sauvegarder le fichier
        try:
            print(f"💾 Tentative de sauvegarde vers: {filepath}")
            file.save(filepath)
            print(f"✅ Fichier sauvegardé avec succès")
        except Exception as e:
            print(f"❌ Erreur lors de la sauvegarde: {e}")
            return None

        # Vérifier que le fichier a été sauvegardé
        if os.path.exists(filepath):
            actual_size = os.path.getsize(filepath)
            print(f"✅ Fichier confirmé: {unique_filename} ({actual_size} bytes)")

            # Optimiser l'image si c'est une image bitmap
            if unique_filename.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.webp')):
                try:
                    from PIL import Image
                    with Image.open(filepath) as img:
                        # Convertir en RGB si nécessaire
                        if img.mode in ('RGBA', 'P'):
                            img = img.convert('RGB')

                        # Redimensionner si l'image est trop grande
                        max_size = (1200, 800)
                        if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
                            # Utiliser LANCZOS au lieu de Image.Resampling.LANCZOS pour compatibilité
                            img.thumbnail(max_size, Image.LANCZOS)
                            print(f"🔄 Image redimensionnée de {img.size} à {max_size}")

                        # Sauvegarder avec compression
                        img.save(filepath, optimize=True, quality=85)
                        print(f"🗜️ Image optimisée avec compression")
                except ImportError:
                    print("⚠️ PIL non disponible, image non optimisée")
                except Exception as e:
                    print(f"⚠️ Erreur lors de l'optimisation de l'image: {e}")

            return unique_filename
        else:
            print("❌ Le fichier n'a pas été sauvegardé")
            return None

    except Exception as e:
        print(f"❌ Erreur lors de la sauvegarde du fichier: {e}")
        import traceback
        traceback.print_exc()
        return None

def get_company_info():
    """Récupérer les informations de la société"""
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM societe LIMIT 1")
        company = cursor.fetchone()
        conn.close()

        return company
    except Exception as e:
        print(f"Erreur lors de la récupération des informations de la société: {e}")
        return None

def log_user_activity(action, module, details=None):
    """Enregistrer l'activité d'un utilisateur"""
    if 'utilisateur_id' not in session:
        return

    try:
        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        # Obtenir l'adresse IP et user agent
        adresse_ip = request.environ.get('HTTP_X_FORWARDED_FOR', request.environ.get('REMOTE_ADDR', 'Unknown'))
        user_agent = request.environ.get('HTTP_USER_AGENT', 'Unknown')

        cursor.execute('''
        INSERT INTO logs_utilisateurs (utilisateur_id, nom_utilisateur, nom_complet, action, module, details, adresse_ip, user_agent)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            session['utilisateur_id'],
            session['nom_utilisateur'],
            session['nom_complet'],
            action,
            module,
            details,
            adresse_ip,
            user_agent
        ))

        conn.commit()
        conn.close()
    except Exception as e:
        print(f"Erreur lors de l'enregistrement de l'activité: {e}")

# Context processor pour rendre les informations de la société disponibles dans tous les templates
@app.context_processor
def inject_company_info():
    """Injecter les informations de la société dans tous les templates"""
    return dict(company_info=get_company_info())

# Création de la base de données si elle n'existe pas
def init_db():
    conn = sqlite3.connect('maintenance.db')
    cursor = conn.cursor()

    # Création de la table utilisateurs
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS utilisateurs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        prenom TEXT NOT NULL,
        email TEXT UNIQUE NOT NULL,
        nom_utilisateur TEXT UNIQUE NOT NULL,
        mot_de_passe TEXT NOT NULL,
        role TEXT NOT NULL,
        date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Table société
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS societe (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL,
        logo TEXT,
        telephone TEXT,
        responsable TEXT,
        email TEXT,
        adresse TEXT,
        if_fiscal TEXT,
        rc TEXT,
        patente TEXT,
        ice TEXT,
        cnss TEXT,
        pied_page TEXT
    )
    ''')

    # Table pour le journal des activités des utilisateurs
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS logs_utilisateurs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        utilisateur_id INTEGER NOT NULL,
        nom_utilisateur TEXT NOT NULL,
        nom_complet TEXT NOT NULL,
        action TEXT NOT NULL,
        module TEXT NOT NULL,
        details TEXT,
        adresse_ip TEXT,
        user_agent TEXT,
        date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (utilisateur_id) REFERENCES utilisateurs (id)
    )
    ''')

    # Table marchés
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS marches (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        numero TEXT NOT NULL,
        domaine TEXT NOT NULL,
        date DATE NOT NULL,
        objet TEXT NOT NULL,
        client TEXT NOT NULL,
        montant REAL NOT NULL,
        delai_execution TEXT,
        periode_interventions TEXT,
        lieu TEXT,
        date_ordre_service DATE,
        caution_definitif TEXT,
        mode_paiement TEXT,
        gps TEXT
    )
    ''')

    # Ajouter la colonne gps si elle n'existe pas déjà
    try:
        cursor.execute("ALTER TABLE marches ADD COLUMN gps TEXT")
    except sqlite3.OperationalError:
        # La colonne existe déjà
        pass

    # Table interventions
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS interventions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        client TEXT NOT NULL,
        numero_marche TEXT NOT NULL,
        objet_marche TEXT NOT NULL,
        delai_execution TEXT,
        domaine TEXT NOT NULL,
        periode_interventions TEXT,
        semestre TEXT,
        periode_marche TEXT,
        lieu TEXT,
        date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Ajouter la colonne semestre si elle n'existe pas déjà
    try:
        cursor.execute("ALTER TABLE interventions ADD COLUMN semestre TEXT")
    except sqlite3.OperationalError:
        # La colonne existe déjà
        pass

    # Table détails interventions
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS details_interventions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        intervention_id INTEGER NOT NULL,
        region TEXT,
        nom_site TEXT,
        nbr_systeme INTEGER,
        date_intervention DATE,
        technicien TEXT,
        situation TEXT,
        etat_materiel TEXT,
        observation TEXT,
        telephone_chef_site TEXT,
        telephone_securite TEXT,
        technicien_contact TEXT,
        gps TEXT,
        FOREIGN KEY (intervention_id) REFERENCES interventions (id)
    )
    ''')

    # Table détails extincteurs (spécifique au domaine EXTINCTEUR)
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS details_extincteurs (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        intervention_id INTEGER NOT NULL,
        abc_6kg INTEGER DEFAULT 0,
        abc_9kg INTEGER DEFAULT 0,
        abc_1kg INTEGER DEFAULT 0,
        eau_pulverise_6l INTEGER DEFAULT 0,
        eau_pulverise_2l INTEGER DEFAULT 0,
        eau_pulverise_9l INTEGER DEFAULT 0,
        eau_pulverise_10l INTEGER DEFAULT 0,
        co2_2kg INTEGER DEFAULT 0,
        co2_5kg INTEGER DEFAULT 0,
        co2_6kg INTEGER DEFAULT 0,
        co2_10kg INTEGER DEFAULT 0,
        FOREIGN KEY (intervention_id) REFERENCES interventions (id)
    )
    ''')

    # Table réclamations
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS reclamations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        client TEXT NOT NULL,
        numero_marche TEXT NOT NULL,
        objet_marche TEXT NOT NULL,
        delai_execution TEXT,
        domaine TEXT NOT NULL,
        periode_interventions TEXT,
        periode_marche TEXT,
        lieu TEXT,
        date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Table détails réclamations
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS details_reclamations (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        reclamation_id INTEGER NOT NULL,
        region TEXT,
        nom_site TEXT,
        nbr_systeme INTEGER,
        date_intervention DATE,
        date_reclamation DATE,
        technicien TEXT,
        situation TEXT,
        etat_materiel TEXT,
        observation TEXT,
        telephone_chef_site TEXT,
        telephone_securite TEXT,
        technicien_contact TEXT,
        gps TEXT,
        FOREIGN KEY (reclamation_id) REFERENCES reclamations (id)
    )
    ''')

    # Table régions
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS regions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        nom TEXT NOT NULL UNIQUE,
        nom_region TEXT,
        description TEXT,
        date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    )
    ''')

    # Ajouter la colonne nom_region si elle n'existe pas déjà
    try:
        cursor.execute("ALTER TABLE regions ADD COLUMN nom_region TEXT")
        # Copier les valeurs de nom vers nom_region
        cursor.execute("UPDATE regions SET nom_region = nom WHERE nom_region IS NULL")
    except sqlite3.OperationalError:
        # La colonne existe déjà
        pass

    # Table sites
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS sites (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        region_id INTEGER NOT NULL,
        nom TEXT NOT NULL,
        nom_site TEXT,
        adresse TEXT,
        telephone TEXT,
        responsable TEXT,
        email TEXT,
        gps TEXT,
        date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (region_id) REFERENCES regions (id),
        UNIQUE(region_id, nom)
    )
    ''')

    # Ajouter la colonne nom_site si elle n'existe pas déjà
    try:
        cursor.execute("ALTER TABLE sites ADD COLUMN nom_site TEXT")
        # Copier les valeurs de nom vers nom_site
        cursor.execute("UPDATE sites SET nom_site = nom WHERE nom_site IS NULL")
    except sqlite3.OperationalError:
        # La colonne existe déjà
        pass

    # Vérifier si un administrateur existe déjà
    cursor.execute("SELECT * FROM utilisateurs WHERE role = 'admin' LIMIT 1")
    admin = cursor.fetchone()

    # Créer un administrateur par défaut si aucun n'existe
    if not admin:
        admin_password = generate_password_hash('admin123')
        cursor.execute('''
        INSERT INTO utilisateurs (nom, prenom, email, nom_utilisateur, mot_de_passe, role)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', ('Admin', 'Système', '<EMAIL>', 'admin', admin_password, 'admin'))

    # Vérifier si les informations de la société existent déjà
    cursor.execute("SELECT * FROM societe LIMIT 1")
    societe = cursor.fetchone()

    # Créer des informations de société par défaut si aucune n'existe
    if not societe:
        cursor.execute('''
        INSERT INTO societe (nom, telephone, responsable, email, adresse, if_fiscal, rc, patente, ice, cnss, pied_page)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', ('Votre Société', '+212 5XX-XXXXXX', 'Directeur', '<EMAIL>', 'Adresse de la société',
              'IF12345', 'RC12345', 'PAT12345', 'ICE12345', 'CNSS12345', 'Pied de page par défaut'))

    conn.commit()
    conn.close()

# Initialiser la base de données au démarrage
init_db()

# Décorateur pour vérifier si l'utilisateur est connecté
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'utilisateur_id' not in session:
            # إذا كان طلب API، أرجع JSON error
            if request.path.startswith('/api/'):
                return jsonify({'error': 'Non autorisé - veuillez vous connecter'}), 401
            # وإلا أعد توجيه لصفحة تسجيل الدخول
            flash('Veuillez vous connecter pour accéder à cette page', 'danger')
            return redirect(url_for('login'))
        return f(*args, **kwargs)
    return decorated_function

# Décorateur pour vérifier si l'utilisateur est admin
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'role' not in session or session['role'] != 'admin':
            flash('Accès réservé aux administrateurs', 'danger')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

# Route pour la page d'accueil
@app.route('/')
def index():
    if 'utilisateur_id' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

# Route pour la page de connexion
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        nom_utilisateur = request.form['nom_utilisateur']
        mot_de_passe = request.form['mot_de_passe']

        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        cursor.execute("SELECT * FROM utilisateurs WHERE nom_utilisateur = ?", (nom_utilisateur,))
        utilisateur = cursor.fetchone()

        if utilisateur and check_password_hash(utilisateur['mot_de_passe'], mot_de_passe):
            session['utilisateur_id'] = utilisateur['id']
            session['nom_utilisateur'] = utilisateur['nom_utilisateur']
            session['role'] = utilisateur['role']
            session['nom_complet'] = f"{utilisateur['prenom']} {utilisateur['nom']}"

            # Enregistrer l'activité de connexion
            log_user_activity("Connexion", "Authentification", f"Connexion réussie pour {utilisateur['nom_utilisateur']}")

            flash('Connexion réussie!', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('Nom d\'utilisateur ou mot de passe incorrect', 'danger')

        conn.close()

    return render_template('login.html')

# Route pour la déconnexion
@app.route('/logout')
def logout():
    session.clear()
    flash('Vous avez été déconnecté', 'info')
    return redirect(url_for('login'))

# Route pour mot de passe oublié
@app.route('/forgot-password', methods=['GET', 'POST'])
def forgot_password():
    if request.method == 'POST':
        email = request.form['email']

        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Vérifier si l'email existe
        cursor.execute("SELECT * FROM utilisateurs WHERE email = ?", (email,))
        utilisateur = cursor.fetchone()

        if utilisateur:
            # Générer un nouveau mot de passe temporaire
            import random
            import string
            temp_password = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
            temp_password_hash = generate_password_hash(temp_password)

            # Mettre à jour le mot de passe dans la base de données
            cursor.execute("UPDATE utilisateurs SET mot_de_passe = ? WHERE email = ?",
                         (temp_password_hash, email))
            conn.commit()

            # Enregistrer l'activité
            log_user_activity("Réinitialisation", "Mot de passe", f"Réinitialisation du mot de passe pour {email}")

            flash(f'Un nouveau mot de passe temporaire a été généré: {temp_password}. Veuillez vous connecter et le changer immédiatement.', 'success')
        else:
            flash('Aucun compte trouvé avec cette adresse email', 'danger')

        conn.close()
        return redirect(url_for('login'))

    return render_template('forgot_password.html')

# Route pour changer le mot de passe
@app.route('/change-password', methods=['GET', 'POST'])
@login_required
def change_password():
    if request.method == 'POST':
        current_password = request.form['current_password']
        new_password = request.form['new_password']
        confirm_password = request.form['confirm_password']

        if new_password != confirm_password:
            flash('Les nouveaux mots de passe ne correspondent pas', 'danger')
            return redirect(url_for('change_password'))

        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Vérifier le mot de passe actuel
        cursor.execute("SELECT * FROM utilisateurs WHERE id = ?", (session['utilisateur_id'],))
        utilisateur = cursor.fetchone()

        if not check_password_hash(utilisateur['mot_de_passe'], current_password):
            flash('Mot de passe actuel incorrect', 'danger')
            conn.close()
            return redirect(url_for('change_password'))

        # Mettre à jour le mot de passe
        new_password_hash = generate_password_hash(new_password)
        cursor.execute("UPDATE utilisateurs SET mot_de_passe = ? WHERE id = ?",
                     (new_password_hash, session['utilisateur_id']))
        conn.commit()
        conn.close()

        # Enregistrer l'activité
        log_user_activity("Modification", "Mot de passe", "Changement de mot de passe")

        flash('Mot de passe changé avec succès', 'success')
        return redirect(url_for('dashboard'))

    return render_template('change_password.html')

# Route pour le tableau de bord
@app.route('/dashboard')
@login_required
def dashboard():
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer les statistiques
    stats = {}

    # Nombre de marchés
    cursor.execute("SELECT COUNT(*) as count FROM marches")
    stats['marches'] = cursor.fetchone()['count']

    # Nombre d'interventions
    cursor.execute("SELECT COUNT(*) as count FROM interventions")
    stats['interventions'] = cursor.fetchone()['count']

    # Nombre de réclamations
    cursor.execute("SELECT COUNT(*) as count FROM reclamations")
    stats['reclamations'] = cursor.fetchone()['count']

    # Nombre de sites
    cursor.execute("SELECT COUNT(*) as count FROM sites")
    stats['sites'] = cursor.fetchone()['count']

    conn.close()

    # Enregistrer l'activité
    log_user_activity("Consultation", "Tableau de bord", "Consultation du tableau de bord principal")

    return render_template('dashboard.html', nom_utilisateur=session.get('nom_complet'), stats=stats)

# Routes pour les informations de la société
@app.route('/societe', methods=['GET', 'POST'])
@login_required
def societe():
    form = SocieteForm()

    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer les informations actuelles
    cursor.execute("SELECT * FROM societe LIMIT 1")
    info = cursor.fetchone()

    if form.validate_on_submit():
        try:
            print("🔄 Formulaire validé avec succès")

            # Récupérer les informations actuelles pour le logo
            current_logo = info['logo'] if info else None
            print(f"📷 Logo actuel dans DB: {current_logo}")

            # Récupérer les données du formulaire
            nom = form.nom.data
            telephone = form.telephone.data
            responsable = form.responsable.data
            email = form.email.data
            adresse = form.adresse.data
            if_fiscal = form.if_fiscal.data
            rc = form.rc.data
            patente = form.patente.data
            ice = form.ice.data
            cnss = form.cnss.data
            pied_page = form.pied_de_pages.data

            print(f"📝 Données du formulaire reçues pour: {nom}")

            # Gérer le téléchargement du logo
            logo_filename = current_logo  # Garder le logo actuel par défaut

            if form.logo.data:
                file = form.logo.data
                print(f"📁 Fichier reçu via form: {file.filename}")

                if file.filename != '':
                    print(f"📁 Détails du fichier: nom={file.filename}")

                    # Sauvegarder le nouveau logo
                    print("💾 Tentative de sauvegarde du logo...")
                    new_logo = save_uploaded_file(file, 'logo')
                    if new_logo:
                        print(f"✅ Logo sauvegardé avec succès: {new_logo}")

                        # Supprimer l'ancien logo s'il existe
                        if current_logo and current_logo != new_logo:
                            old_logo_path = os.path.join(app.config['UPLOAD_FOLDER'], current_logo)
                            if os.path.exists(old_logo_path):
                                try:
                                    os.remove(old_logo_path)
                                    print(f"🗑️ Ancien logo supprimé: {current_logo}")
                                except Exception as e:
                                    print(f"⚠️ Erreur lors de la suppression de l'ancien logo: {e}")

                        logo_filename = new_logo
                        flash('Logo téléchargé et mis à jour avec succès!', 'success')
                    else:
                        error_msg = 'Erreur lors du téléchargement du logo. Vérifiez le format et la taille.'
                        flash(error_msg, 'danger')
                        print(f"❌ {error_msg}")
                else:
                    print("📁 Aucun fichier sélectionné pour le logo")
            else:
                print("📁 Aucun fichier logo dans le formulaire")

            # Mettre à jour la base de données
            print(f"💾 Mise à jour de la base de données avec logo: {logo_filename}")

            # Vérifier s'il y a déjà une entrée dans la table société
            cursor.execute("SELECT COUNT(*) FROM societe")
            count = cursor.fetchone()[0]
            print(f"📊 Nombre d'entrées dans la table société: {count}")

            if count == 0:
                # Insérer une nouvelle entrée
                print("📝 Insertion d'une nouvelle entrée...")
                cursor.execute('''
                INSERT INTO societe (nom, logo, telephone, responsable, email, adresse, if_fiscal, rc, patente, ice, cnss, pied_page)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (nom, logo_filename, telephone, responsable, email, adresse, if_fiscal, rc, patente, ice, cnss, pied_page))
                print("📝 Nouvelle entrée créée dans la table société")
            else:
                # Mettre à jour l'entrée existante
                print("🔄 Mise à jour de l'entrée existante...")
                cursor.execute('''
                UPDATE societe SET
                    nom = ?, logo = ?, telephone = ?, responsable = ?, email = ?, adresse = ?,
                    if_fiscal = ?, rc = ?, patente = ?, ice = ?, cnss = ?, pied_page = ?
                WHERE id = (SELECT MIN(id) FROM societe)
                ''', (nom, logo_filename, telephone, responsable, email, adresse, if_fiscal, rc, patente, ice, cnss, pied_page))
                affected_rows = cursor.rowcount
                print(f"🔄 Entrée existante mise à jour ({affected_rows} lignes affectées)")

            # Vérifier que la mise à jour a bien eu lieu
            cursor.execute("SELECT logo FROM societe LIMIT 1")
            updated_logo = cursor.fetchone()['logo']
            print(f"🔍 Logo après mise à jour: {updated_logo}")

            conn.commit()
            print("✅ Base de données mise à jour avec succès")
            flash('Informations de la société mises à jour avec succès', 'success')

            # Enregistrer l'activité
            log_user_activity("Modification", "Informations Société", f"Mise à jour des informations de la société: {nom}")

        except Exception as e:
            conn.rollback()
            flash(f'Erreur lors de la mise à jour: {str(e)}', 'danger')
            print(f"Erreur dans route societe: {e}")

    # Pré-remplir le formulaire avec les données existantes si c'est un GET
    if request.method == 'GET' and info:
        form.nom.data = info['nom']
        form.telephone.data = info['telephone']
        form.responsable.data = info['responsable']
        form.email.data = info['email']
        form.adresse.data = info['adresse']
        form.if_fiscal.data = info['if_fiscal']
        form.rc.data = info['rc']
        form.patente.data = info['patente']
        form.ice.data = info['ice']
        form.cnss.data = info['cnss']
        form.pied_de_pages.data = info['pied_page']

    conn.close()

    # Enregistrer l'activité de consultation
    if request.method == 'GET':
        log_user_activity("Consultation", "Informations Société", "Consultation des informations de la société")

    return render_template('societe.html', form=form, info=info)

# Routes pour la gestion des utilisateurs
@app.route('/utilisateurs')
@login_required
@admin_required
def utilisateurs():
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    cursor.execute("SELECT * FROM utilisateurs ORDER BY nom")
    utilisateurs = cursor.fetchall()
    conn.close()

    return render_template('utilisateurs.html', utilisateurs=utilisateurs)

@app.route('/utilisateurs/ajouter', methods=['GET', 'POST'])
@login_required
@admin_required
def ajouter_utilisateur():
    if request.method == 'POST':
        nom = request.form['nom']
        prenom = request.form['prenom']
        email = request.form['email']
        nom_utilisateur = request.form['nom_utilisateur']
        mot_de_passe = request.form['mot_de_passe']
        role = request.form['role']

        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        # Vérifier si le nom d'utilisateur existe déjà
        cursor.execute("SELECT * FROM utilisateurs WHERE nom_utilisateur = ?", (nom_utilisateur,))
        if cursor.fetchone():
            flash('Ce nom d\'utilisateur existe déjà', 'danger')
            conn.close()
            return redirect(url_for('ajouter_utilisateur'))

        # Vérifier si l'email existe déjà
        cursor.execute("SELECT * FROM utilisateurs WHERE email = ?", (email,))
        if cursor.fetchone():
            flash('Cet email existe déjà', 'danger')
            conn.close()
            return redirect(url_for('ajouter_utilisateur'))

        # Hacher le mot de passe
        mot_de_passe_hash = generate_password_hash(mot_de_passe)

        # Insérer le nouvel utilisateur
        cursor.execute('''
        INSERT INTO utilisateurs (nom, prenom, email, nom_utilisateur, mot_de_passe, role)
        VALUES (?, ?, ?, ?, ?, ?)
        ''', (nom, prenom, email, nom_utilisateur, mot_de_passe_hash, role))

        conn.commit()
        conn.close()

        flash('Utilisateur ajouté avec succès', 'success')
        return redirect(url_for('utilisateurs'))

    return render_template('ajouter_utilisateur.html')

@app.route('/utilisateurs/modifier/<int:id>', methods=['GET', 'POST'])
@login_required
@admin_required
def modifier_utilisateur(id):
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer l'utilisateur
    cursor.execute("SELECT * FROM utilisateurs WHERE id = ?", (id,))
    utilisateur = cursor.fetchone()

    if not utilisateur:
        flash('Utilisateur non trouvé', 'danger')
        conn.close()
        return redirect(url_for('utilisateurs'))

    if request.method == 'POST':
        nom = request.form['nom']
        prenom = request.form['prenom']
        email = request.form['email']
        role = request.form['role']

        # Vérifier si l'email existe déjà pour un autre utilisateur
        cursor.execute("SELECT * FROM utilisateurs WHERE email = ? AND id != ?", (email, id))
        if cursor.fetchone():
            flash('Cet email est déjà utilisé par un autre utilisateur', 'danger')
        else:
            # Mettre à jour l'utilisateur
            cursor.execute('''
            UPDATE utilisateurs SET nom = ?, prenom = ?, email = ?, role = ?
            WHERE id = ?
            ''', (nom, prenom, email, role, id))

            # Si un nouveau mot de passe est fourni, le mettre à jour
            if request.form['mot_de_passe']:
                mot_de_passe_hash = generate_password_hash(request.form['mot_de_passe'])
                cursor.execute("UPDATE utilisateurs SET mot_de_passe = ? WHERE id = ?", (mot_de_passe_hash, id))

            conn.commit()
            flash('Utilisateur mis à jour avec succès', 'success')
            return redirect(url_for('utilisateurs'))

    conn.close()
    return render_template('modifier_utilisateur.html', utilisateur=utilisateur)

@app.route('/utilisateurs/supprimer/<int:id>')
@login_required
@admin_required
def supprimer_utilisateur(id):
    try:
        print(f"🗑️ Tentative de suppression de l'utilisateur: {id}")

        # Empêcher la suppression de l'utilisateur connecté
        if session.get('utilisateur_id') == id:
            print(f"❌ Tentative de suppression de son propre compte")
            flash('Vous ne pouvez pas supprimer votre propre compte', 'danger')
            return redirect(url_for('utilisateurs'))

        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        # Vérifier si c'est le dernier administrateur
        cursor.execute("SELECT COUNT(*) FROM utilisateurs WHERE role = 'admin'")
        count_admin = cursor.fetchone()[0]
        print(f"📊 Nombre d'administrateurs: {count_admin}")

        cursor.execute("SELECT role FROM utilisateurs WHERE id = ?", (id,))
        user_result = cursor.fetchone()

        if not user_result:
            print(f"❌ Utilisateur {id} non trouvé")
            flash('Utilisateur non trouvé', 'danger')
            conn.close()
            return redirect(url_for('utilisateurs'))

        role = user_result[0]
        print(f"👤 Rôle de l'utilisateur: {role}")

        if count_admin <= 1 and role == 'admin':
            print(f"⚠️ Impossible de supprimer le dernier administrateur")
            flash('Impossible de supprimer le dernier administrateur', 'danger')
        else:
            print(f"🗑️ Suppression de l'utilisateur {id}")
            cursor.execute("DELETE FROM utilisateurs WHERE id = ?", (id,))
            conn.commit()
            print(f"✅ Utilisateur {id} supprimé avec succès")
            flash('Utilisateur supprimé avec succès', 'success')

        conn.close()
        return redirect(url_for('utilisateurs'))

    except Exception as e:
        print(f"❌ Erreur dans supprimer_utilisateur: {e}")
        import traceback
        traceback.print_exc()
        flash(f'Erreur lors de la suppression: {str(e)}', 'danger')
        return redirect(url_for('utilisateurs'))

# Routes pour la gestion des marchés
@app.route('/marches')
@login_required
def marches():
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Récupérer les filtres s'ils existent
        domaine = request.args.get('domaine', '')
        client = request.args.get('client', '')
        date_debut = request.args.get('date_debut', '')
        date_fin = request.args.get('date_fin', '')

        # Construire la requête SQL avec les filtres
        query = "SELECT * FROM marches WHERE 1=1"
        params = []

        if domaine:
            query += " AND domaine = ?"
            params.append(domaine)

        if client:
            query += " AND client LIKE ?"
            params.append(f"%{client}%")

        if date_debut:
            query += " AND date >= ?"
            params.append(date_debut)

        if date_fin:
            query += " AND date <= ?"
            params.append(date_fin)

        query += " ORDER BY date DESC"

        cursor.execute(query, params)
        marches = cursor.fetchall()

        # Récupérer la liste des clients pour le filtre (si la colonne existe)
        try:
            cursor.execute("SELECT DISTINCT client FROM marches WHERE client IS NOT NULL ORDER BY client")
            clients = [row['client'] for row in cursor.fetchall()]
        except sqlite3.OperationalError:
            # Si la colonne client n'existe pas, utiliser une liste vide
            clients = []

        conn.close()

        return render_template('marches.html', marches=marches, clients=clients,
                              filtres={'domaine': domaine, 'client': client,
                                      'date_debut': date_debut, 'date_fin': date_fin})

    except Exception as e:
        print(f"Erreur dans /marches: {e}")
        flash(f'Erreur lors du chargement des marchés: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/marches/ajouter', methods=['GET', 'POST'])
@login_required
def ajouter_marche():
    if request.method == 'POST':
        numero = request.form['numero']
        domaine = request.form['domaine']
        date = request.form['date']
        objet = request.form['objet']
        client = request.form['client']
        montant = request.form['montant']
        delai_execution = request.form['delai_execution']
        periode_interventions = request.form['periode_interventions']
        lieu = request.form['lieu']

        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        # Vérifier si le numéro de marché existe déjà
        cursor.execute("SELECT * FROM marches WHERE numero = ?", (numero,))
        if cursor.fetchone():
            flash('Ce numéro de marché existe déjà', 'danger')
            conn.close()
            return redirect(url_for('ajouter_marche'))

        # Insérer le nouveau marché
        cursor.execute('''
        INSERT INTO marches (numero, domaine, date, objet, client, montant, delai_execution, periode_interventions, lieu)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (numero, domaine, date, objet, client, montant, delai_execution, periode_interventions, lieu))

        conn.commit()
        conn.close()

        flash('Marché ajouté avec succès', 'success')
        return redirect(url_for('marches'))

    return render_template('ajouter_marche.html')

@app.route('/marches/modifier/<string:numero>', methods=['GET', 'POST'])
@login_required
def modifier_marche(numero):
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer le marché
    cursor.execute("SELECT * FROM marches WHERE numero = ?", (numero,))
    marche = cursor.fetchone()

    if not marche:
        flash('Marché non trouvé', 'danger')
        conn.close()
        return redirect(url_for('marches'))

    if request.method == 'POST':
        domaine = request.form['domaine']
        date = request.form['date']
        objet = request.form['objet']
        client = request.form['client']
        montant = request.form['montant']
        delai_execution = request.form['delai_execution']
        periode_interventions = request.form['periode_interventions']
        lieu = request.form['lieu']

        # Mettre à jour le marché
        cursor.execute('''
        UPDATE marches SET
            domaine = ?, date = ?, objet = ?, client = ?, montant = ?,
            delai_execution = ?, periode_interventions = ?, lieu = ?
        WHERE numero = ?
        ''', (domaine, date, objet, client, montant, delai_execution, periode_interventions, lieu, numero))

        conn.commit()
        flash('Marché mis à jour avec succès', 'success')
        return redirect(url_for('marches'))

    conn.close()
    return render_template('modifier_marche.html', marche=marche)

@app.route('/marches/supprimer/<string:numero>')
@login_required
def supprimer_marche(numero):
    print(f"🗑️ DEBUT - Suppression marché: {numero}")

    try:
        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        print(f"🔍 Connexion DB établie")

        # Simple delete without checks for testing
        cursor.execute("DELETE FROM marches WHERE numero = ?", (numero,))
        rows_affected = cursor.rowcount

        print(f"🗑️ Lignes supprimées: {rows_affected}")

        if rows_affected > 0:
            conn.commit()
            print(f"✅ Marché {numero} supprimé avec succès")
            flash('Marché supprimé avec succès', 'success')
        else:
            print(f"❌ Aucune ligne supprimée pour {numero}")
            flash('Marché non trouvé', 'danger')

        conn.close()
        print(f"🔚 FIN - Redirection vers marches")
        return redirect(url_for('marches'))

    except Exception as e:
        print(f"❌ ERREUR dans supprimer_marche: {e}")
        import traceback
        traceback.print_exc()
        flash(f'Erreur: {str(e)}', 'danger')
        return redirect(url_for('marches'))

@app.route('/marches/details/<string:numero>')
@login_required
def details_marche(numero):
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer le marché
    cursor.execute("SELECT * FROM marches WHERE numero = ?", (numero,))
    marche = cursor.fetchone()

    if not marche:
        flash('Marché non trouvé', 'danger')
        conn.close()
        return redirect(url_for('marches'))

    # Récupérer les interventions liées à ce marché
    cursor.execute("SELECT * FROM interventions WHERE numero_marche = ? ORDER BY date_creation DESC", (numero,))
    interventions = cursor.fetchall()

    # Récupérer les réclamations liées à ce marché
    cursor.execute("SELECT * FROM reclamations WHERE numero_marche = ? ORDER BY date_creation DESC", (numero,))
    reclamations = cursor.fetchall()

    conn.close()

    return render_template('details_marche.html', marche=marche,
                          interventions=interventions, reclamations=reclamations)

# Routes pour la gestion des interventions
@app.route('/interventions')
@login_required
def interventions():
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Récupérer les filtres s'ils existent
        domaine = request.args.get('domaine', '')
        client = request.args.get('client', '')
        date_debut = request.args.get('date_debut', '')
        date_fin = request.args.get('date_fin', '')

        # Construire la requête SQL avec les filtres
        query = "SELECT i.*, m.numero, m.objet FROM interventions i LEFT JOIN marches m ON i.numero_marche = m.numero WHERE 1=1"
        params = []

        if domaine:
            query += " AND i.domaine = ?"
            params.append(domaine)

        if client:
            query += " AND i.client LIKE ?"
            params.append(f"%{client}%")

        if date_debut:
            query += " AND i.date_creation >= ?"
            params.append(date_debut)

        if date_fin:
            query += " AND i.date_creation <= ?"
            params.append(date_fin)

        query += " ORDER BY i.date_creation DESC"

        cursor.execute(query, params)
        interventions = cursor.fetchall()

        # Récupérer la liste des clients pour le filtre
        cursor.execute("SELECT DISTINCT client FROM interventions ORDER BY client")
        clients = [row['client'] for row in cursor.fetchall()]

        conn.close()

        return render_template('interventions.html', interventions=interventions, clients=clients,
                              filtres={'domaine': domaine, 'client': client,
                                      'date_debut': date_debut, 'date_fin': date_fin})

    except Exception as e:
        print(f"Erreur dans /interventions: {e}")
        flash(f'Erreur lors du chargement des interventions: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/interventions/ajouter', methods=['GET', 'POST'])
@login_required
def ajouter_intervention():
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer la liste des marchés pour le formulaire
    cursor.execute("SELECT * FROM marches ORDER BY date DESC")
    marches = cursor.fetchall()

    if request.method == 'POST':
        client = request.form['client']
        numero_marche = request.form['numero_marche']
        objet_marche = request.form['objet_marche']
        delai_execution = request.form['delai_execution']
        domaine = request.form['domaine']
        periode_interventions = request.form['periode_interventions']
        semestre = request.form.get('semestre', '')
        periode_marche = request.form['periode_marche']
        lieu = request.form['lieu']

        # Insérer la nouvelle intervention
        cursor.execute('''
        INSERT INTO interventions (client, numero_marche, objet_marche, delai_execution,
                                 domaine, periode_interventions, semestre, periode_marche, lieu)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (client, numero_marche, objet_marche, delai_execution,
             domaine, periode_interventions, semestre, periode_marche, lieu))

        intervention_id = cursor.lastrowid

        # Si c'est une intervention de type EXTINCTEUR, ajouter les détails spécifiques
        if domaine == 'EXTINCTEUR':
            cursor.execute('''
            INSERT INTO details_extincteurs (intervention_id)
            VALUES (?)
            ''', (intervention_id,))

        conn.commit()
        flash('Intervention ajoutée avec succès', 'success')

        # Rediriger vers la page de détails de l'intervention pour ajouter les détails
        return redirect(url_for('details_intervention', id=intervention_id))

    conn.close()
    return render_template('ajouter_intervention.html', marches=marches)

@app.route('/interventions/details/<int:id>', methods=['GET', 'POST'])
@login_required
def details_intervention(id):
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer l'intervention
    cursor.execute("SELECT * FROM interventions WHERE id = ?", (id,))
    intervention = cursor.fetchone()

    if not intervention:
        flash('Intervention non trouvée', 'danger')
        conn.close()
        return redirect(url_for('interventions'))

    # Récupérer les détails de l'intervention
    cursor.execute("SELECT * FROM details_interventions WHERE intervention_id = ? ORDER BY id", (id,))
    details = cursor.fetchall()

    # Si c'est une intervention de type EXTINCTEUR, récupérer les détails spécifiques
    extincteur_details = None
    if intervention['domaine'] == 'EXTINCTEUR':
        cursor.execute("SELECT * FROM details_extincteurs WHERE intervention_id = ?", (id,))
        extincteur_details = cursor.fetchone()

    # Récupérer les régions et sites pour les listes déroulantes
    cursor.execute("SELECT DISTINCT nom_region FROM regions ORDER BY nom_region")
    regions = cursor.fetchall()

    cursor.execute("""
        SELECT DISTINCT s.nom_site as site, r.nom_region as region
        FROM sites s
        LEFT JOIN regions r ON s.region_id = r.id
        ORDER BY s.nom_site
    """)
    sites = cursor.fetchall()

    if request.method == 'POST':
        # Traitement de l'ajout d'une ligne de détail
        if 'ajouter_detail' in request.form:
            region = request.form['region']
            nom_site = request.form['nom_site']
            nbr_systeme = request.form.get('nbr_systeme', 0)
            date_intervention = request.form['date_intervention']
            technicien = request.form['technicien']
            situation = request.form['situation']
            etat_materiel = request.form['etat_materiel']
            observation = request.form.get('observation', '')
            telephone_chef_site = request.form.get('telephone_chef_site', '')
            telephone_securite = request.form.get('telephone_securite', '')
            technicien_contact = request.form.get('technicien_contact', '')
            gps = request.form.get('gps', '')

            cursor.execute('''
            INSERT INTO details_interventions (intervention_id, region, nom_site, nbr_systeme,
                                             date_intervention, technicien, situation, etat_materiel,
                                             observation, telephone_chef_site, telephone_securite,
                                             technicien_contact, gps)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (id, region, nom_site, nbr_systeme, date_intervention, technicien,
                 situation, etat_materiel, observation, telephone_chef_site,
                 telephone_securite, technicien_contact, gps))

            conn.commit()
            flash('Détail ajouté avec succès', 'success')
            return redirect(url_for('details_intervention', id=id))

        # Traitement de la mise à jour des détails d'extincteurs
        elif 'update_extincteurs' in request.form and intervention['domaine'] == 'EXTINCTEUR':
            abc_6kg = request.form.get('abc_6kg', 0)
            abc_9kg = request.form.get('abc_9kg', 0)
            abc_1kg = request.form.get('abc_1kg', 0)
            eau_pulverise_6l = request.form.get('eau_pulverise_6l', 0)
            eau_pulverise_2l = request.form.get('eau_pulverise_2l', 0)
            eau_pulverise_9l = request.form.get('eau_pulverise_9l', 0)
            eau_pulverise_10l = request.form.get('eau_pulverise_10l', 0)
            co2_2kg = request.form.get('co2_2kg', 0)
            co2_5kg = request.form.get('co2_5kg', 0)
            co2_6kg = request.form.get('co2_6kg', 0)
            co2_10kg = request.form.get('co2_10kg', 0)

            cursor.execute('''
            UPDATE details_extincteurs SET
                abc_6kg = ?, abc_9kg = ?, abc_1kg = ?,
                eau_pulverise_6l = ?, eau_pulverise_2l = ?, eau_pulverise_9l = ?, eau_pulverise_10l = ?,
                co2_2kg = ?, co2_5kg = ?, co2_6kg = ?, co2_10kg = ?
            WHERE intervention_id = ?
            ''', (abc_6kg, abc_9kg, abc_1kg, eau_pulverise_6l, eau_pulverise_2l,
                 eau_pulverise_9l, eau_pulverise_10l, co2_2kg, co2_5kg, co2_6kg, co2_10kg, id))

            conn.commit()
            flash('Détails des extincteurs mis à jour avec succès', 'success')
            return redirect(url_for('details_intervention', id=id))

    conn.close()

    # Déterminer quel template utiliser en fonction du domaine
    template = f"interventions/{intervention['domaine'].lower()}.html"
    try:
        return render_template(template, intervention=intervention, details=details,
                             extincteur_details=extincteur_details, regions=regions, sites=sites)
    except:
        # Si le template spécifique n'existe pas, utiliser le template générique
        return render_template('interventions/details.html', intervention=intervention, details=details,
                             extincteur_details=extincteur_details, regions=regions, sites=sites)

@app.route('/interventions/supprimer_detail/<int:id>')
@login_required
def supprimer_detail_intervention(id):
    conn = sqlite3.connect('maintenance.db')
    cursor = conn.cursor()

    # Récupérer l'intervention_id avant de supprimer le détail
    cursor.execute("SELECT intervention_id FROM details_interventions WHERE id = ?", (id,))
    result = cursor.fetchone()

    if not result:
        flash('Détail non trouvé', 'danger')
        conn.close()
        return redirect(url_for('interventions'))

    intervention_id = result[0]

    # Supprimer le détail
    cursor.execute("DELETE FROM details_interventions WHERE id = ?", (id,))
    conn.commit()

    flash('Détail supprimé avec succès', 'success')
    conn.close()

    return redirect(url_for('details_intervention', id=intervention_id))

@app.route('/interventions/modifier/<int:id>', methods=['GET', 'POST'])
@login_required
def modifier_intervention(id):
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer l'intervention
    cursor.execute("SELECT * FROM interventions WHERE id = ?", (id,))
    intervention = cursor.fetchone()

    if not intervention:
        flash('Intervention non trouvée', 'danger')
        conn.close()
        return redirect(url_for('interventions'))

    # Récupérer la liste des marchés pour le formulaire
    cursor.execute("SELECT * FROM marches ORDER BY date DESC")
    marches = cursor.fetchall()

    if request.method == 'POST':
        # Récupérer les données du formulaire
        client = request.form['client']
        numero_marche = request.form['numero_marche']
        objet_marche = request.form['objet_marche']
        delai_execution = request.form['delai_execution']
        domaine = request.form['domaine']
        periode_interventions = request.form['periode_interventions']
        semestre = request.form.get('semestre', '')
        periode_marche = request.form['periode_marche']
        lieu = request.form['lieu']

        # Mettre à jour l'intervention
        cursor.execute('''
        UPDATE interventions
        SET client = ?, numero_marche = ?, objet_marche = ?, delai_execution = ?,
            domaine = ?, periode_interventions = ?, semestre = ?, periode_marche = ?, lieu = ?
        WHERE id = ?
        ''', (client, numero_marche, objet_marche, delai_execution, domaine,
              periode_interventions, semestre, periode_marche, lieu, id))

        conn.commit()
        flash('Intervention modifiée avec succès', 'success')
        return redirect(url_for('details_intervention', id=id))

    conn.close()
    return render_template('modifier_intervention.html', intervention=intervention, marches=marches)

@app.route('/interventions/supprimer/<int:id>')
@login_required
def supprimer_intervention(id):
    conn = sqlite3.connect('maintenance.db')
    cursor = conn.cursor()

    # Vérifier si l'intervention existe
    cursor.execute("SELECT * FROM interventions WHERE id = ?", (id,))
    if not cursor.fetchone():
        flash('Intervention non trouvée', 'danger')
        conn.close()
        return redirect(url_for('interventions'))

    # Supprimer les détails associés
    cursor.execute("DELETE FROM details_interventions WHERE intervention_id = ?", (id,))

    # Supprimer les détails d'extincteurs si présents
    cursor.execute("DELETE FROM details_extincteurs WHERE intervention_id = ?", (id,))

    # Supprimer l'intervention
    cursor.execute("DELETE FROM interventions WHERE id = ?", (id,))

    conn.commit()
    flash('Intervention supprimée avec succès', 'success')
    conn.close()

    return redirect(url_for('interventions'))

# Routes pour la gestion des réclamations
@app.route('/reclamations')
@login_required
def reclamations():
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Récupérer les filtres s'ils existent
        domaine = request.args.get('domaine', '')
        client = request.args.get('client', '')
        date_debut = request.args.get('date_debut', '')
        date_fin = request.args.get('date_fin', '')

        # Construire la requête SQL avec les filtres
        query = "SELECT r.*, m.numero, m.objet FROM reclamations r LEFT JOIN marches m ON r.numero_marche = m.numero WHERE 1=1"
        params = []

        if domaine:
            query += " AND r.domaine = ?"
            params.append(domaine)

        if client:
            query += " AND r.client LIKE ?"
            params.append(f"%{client}%")

        if date_debut:
            query += " AND r.date_creation >= ?"
            params.append(date_debut)

        if date_fin:
            query += " AND r.date_creation <= ?"
            params.append(date_fin)

        query += " ORDER BY r.date_creation DESC"

        cursor.execute(query, params)
        reclamations = cursor.fetchall()

        # Récupérer la liste des clients pour le filtre
        cursor.execute("SELECT DISTINCT client FROM reclamations ORDER BY client")
        clients = [row['client'] for row in cursor.fetchall()]

        conn.close()

        return render_template('reclamations.html', reclamations=reclamations, clients=clients,
                              filtres={'domaine': domaine, 'client': client,
                                      'date_debut': date_debut, 'date_fin': date_fin})

    except Exception as e:
        print(f"Erreur dans /reclamations: {e}")
        flash(f'Erreur lors du chargement des réclamations: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/reclamations/ajouter', methods=['GET', 'POST'])
@login_required
def ajouter_reclamation():
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer la liste des marchés pour le formulaire
    cursor.execute("SELECT * FROM marches ORDER BY date DESC")
    marches = cursor.fetchall()

    if request.method == 'POST':
        # Récupérer les données du formulaire
        client = request.form['client']
        numero_marche = request.form['numero_marche']
        objet_marche = request.form['objet_marche']
        delai_execution = request.form['delai_execution']
        domaine = request.form['domaine']
        periode_interventions = request.form['periode_interventions']
        periode_marche = request.form['periode_marche']
        lieu = request.form['lieu']

        # Insérer la nouvelle réclamation
        cursor.execute('''
        INSERT INTO reclamations (client, numero_marche, objet_marche, delai_execution,
                               domaine, periode_interventions, periode_marche, lieu, date_creation)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
        ''', (client, numero_marche, objet_marche, delai_execution,
             domaine, periode_interventions, periode_marche, lieu))

        # Récupérer l'ID de la réclamation nouvellement créée
        reclamation_id = cursor.lastrowid

        conn.commit()
        conn.close()

        flash('Réclamation ajoutée avec succès', 'success')
        return redirect(url_for('voir_reclamation', id=reclamation_id))

    conn.close()
    return render_template('ajouter_reclamation.html', marches=marches)

@app.route('/reclamations/voir/<int:id>')
@login_required
def voir_reclamation(id):
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer la réclamation
    cursor.execute('''
    SELECT r.*, m.objet
    FROM reclamations r
    LEFT JOIN marches m ON r.numero_marche = m.numero
    WHERE r.id = ?
    ''', (id,))
    reclamation = cursor.fetchone()

    if not reclamation:
        flash('Réclamation non trouvée', 'danger')
        conn.close()
        return redirect(url_for('reclamations'))

    # Récupérer les détails de la réclamation
    cursor.execute('''
    SELECT * FROM details_reclamations
    WHERE reclamation_id = ?
    ORDER BY id
    ''', (id,))
    details = cursor.fetchall()

    conn.close()

    return render_template('voir_reclamation.html', reclamation=reclamation, details=details)

@app.route('/reclamations/modifier/<int:id>', methods=['GET', 'POST'])
@login_required
def modifier_reclamation(id):
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer la réclamation
    cursor.execute("SELECT * FROM reclamations WHERE id = ?", (id,))
    reclamation = cursor.fetchone()

    if not reclamation:
        flash('Réclamation non trouvée', 'danger')
        conn.close()
        return redirect(url_for('reclamations'))

    # Récupérer la liste des marchés pour le formulaire
    cursor.execute("SELECT * FROM marches ORDER BY date DESC")
    marches = cursor.fetchall()

    if request.method == 'POST':
        # Récupérer les données du formulaire
        client = request.form['client']
        numero_marche = request.form['numero_marche']
        objet_marche = request.form['objet_marche']
        delai_execution = request.form['delai_execution']
        domaine = request.form['domaine']
        periode_interventions = request.form['periode_interventions']
        semestre = request.form.get('semestre', '')
        periode_marche = request.form['periode_marche']
        lieu = request.form['lieu']

        # Mettre à jour la réclamation
        cursor.execute('''
        UPDATE reclamations
        SET client = ?, numero_marche = ?, objet_marche = ?, delai_execution = ?,
            domaine = ?, periode_interventions = ?, semestre = ?, periode_marche = ?, lieu = ?
        WHERE id = ?
        ''', (client, numero_marche, objet_marche, delai_execution, domaine,
              periode_interventions, semestre, periode_marche, lieu, id))

        conn.commit()
        flash('Réclamation modifiée avec succès', 'success')
        return redirect(url_for('voir_reclamation', id=id))

    conn.close()
    return render_template('modifier_reclamation.html', reclamation=reclamation, marches=marches)

@app.route('/reclamations/supprimer/<int:id>')
@login_required
def supprimer_reclamation(id):
    conn = sqlite3.connect('maintenance.db')
    cursor = conn.cursor()

    # Vérifier si la réclamation existe
    cursor.execute("SELECT * FROM reclamations WHERE id = ?", (id,))
    if not cursor.fetchone():
        flash('Réclamation non trouvée', 'danger')
        conn.close()
        return redirect(url_for('reclamations'))

    # Supprimer les détails de la réclamation
    cursor.execute("DELETE FROM details_reclamations WHERE reclamation_id = ?", (id,))

    # Supprimer la réclamation
    cursor.execute("DELETE FROM reclamations WHERE id = ?", (id,))

    conn.commit()
    flash('Réclamation supprimée avec succès', 'success')

    conn.close()
    return redirect(url_for('reclamations'))

@app.route('/reclamations/ajouter_detail/<int:id>', methods=['GET', 'POST'])
@login_required
def ajouter_detail_reclamation(id):
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Vérifier si la réclamation existe
    cursor.execute("SELECT * FROM reclamations WHERE id = ?", (id,))
    reclamation = cursor.fetchone()

    if not reclamation:
        flash('Réclamation non trouvée', 'danger')
        conn.close()
        return redirect(url_for('reclamations'))

    # Récupérer les régions et sites pour les listes déroulantes
    cursor.execute("SELECT DISTINCT nom_region FROM regions ORDER BY nom_region")
    regions = cursor.fetchall()

    cursor.execute("""
        SELECT DISTINCT s.nom_site as site, r.nom_region as region
        FROM sites s
        LEFT JOIN regions r ON s.region_id = r.id
        ORDER BY s.nom_site
    """)
    sites = cursor.fetchall()

    if request.method == 'POST':
        # Récupérer les données du formulaire
        region = request.form['region']
        nom_site = request.form['nom_site']
        nbr_systeme = request.form['nbr_systeme']
        date_intervention = request.form['date_intervention']
        date_reclamation = request.form['date_reclamation']
        technicien = request.form['technicien']
        situation = request.form['situation']
        etat_materiel = request.form['etat_materiel']
        observation = request.form['observation']
        telephone_chef_site = request.form['telephone_chef_site']
        telephone_securite = request.form['telephone_securite']
        technicien_contact = request.form['technicien_contact']
        gps = request.form['gps']

        # Insérer le nouveau détail
        cursor.execute('''
        INSERT INTO details_reclamations (reclamation_id, region, nom_site, nbr_systeme,
                                      date_intervention, date_reclamation, technicien, situation,
                                      etat_materiel, observation, telephone_chef_site,
                                      telephone_securite, technicien_contact, gps)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (id, region, nom_site, nbr_systeme, date_intervention, date_reclamation,
             technicien, situation, etat_materiel, observation, telephone_chef_site,
             telephone_securite, technicien_contact, gps))

        conn.commit()
        flash('Détail ajouté avec succès', 'success')
        return redirect(url_for('voir_reclamation', id=id))

    conn.close()
    return render_template('ajouter_detail_reclamation.html', reclamation=reclamation, regions=regions, sites=sites)

@app.route('/reclamations/modifier_detail/<int:id>', methods=['GET', 'POST'])
@login_required
def modifier_detail_reclamation(id):
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer le détail de réclamation
    cursor.execute("SELECT * FROM details_reclamations WHERE id = ?", (id,))
    detail = cursor.fetchone()

    if not detail:
        flash('Détail de réclamation non trouvé', 'danger')
        conn.close()
        return redirect(url_for('reclamations'))

    # Récupérer la réclamation associée
    cursor.execute("SELECT * FROM reclamations WHERE id = ?", (detail['reclamation_id'],))
    reclamation = cursor.fetchone()

    # Récupérer la liste des régions pour le formulaire
    cursor.execute("SELECT * FROM regions ORDER BY nom")
    regions = cursor.fetchall()

    if request.method == 'POST':
        region = request.form['region']
        nom_site = request.form['nom_site']
        nbr_systeme = request.form['nbr_systeme']
        date_intervention = request.form['date_intervention']
        date_reclamation = request.form['date_reclamation']
        technicien = request.form['technicien']
        situation = request.form['situation']
        etat_materiel = request.form['etat_materiel']
        observation = request.form['observation']
        telephone_chef_site = request.form['telephone_chef_site']
        telephone_securite = request.form['telephone_securite']
        technicien_contact = request.form['technicien_contact']
        gps = request.form['gps']

        # Mettre à jour le détail
        cursor.execute('''
        UPDATE details_reclamations SET
            region = ?, nom_site = ?, nbr_systeme = ?, date_intervention = ?,
            date_reclamation = ?, technicien = ?, situation = ?, etat_materiel = ?,
            observation = ?, telephone_chef_site = ?, telephone_securite = ?,
            technicien_contact = ?, gps = ?
        WHERE id = ?
        ''', (region, nom_site, nbr_systeme, date_intervention, date_reclamation,
             technicien, situation, etat_materiel, observation, telephone_chef_site,
             telephone_securite, technicien_contact, gps, id))

        conn.commit()
        flash('Détail mis à jour avec succès', 'success')
        return redirect(url_for('voir_reclamation', id=detail['reclamation_id']))

    conn.close()
    return render_template('modifier_detail_reclamation.html', detail=detail, reclamation=reclamation, regions=regions)

@app.route('/reclamations/supprimer_detail/<int:id>')
@login_required
def supprimer_detail_reclamation(id):
    conn = sqlite3.connect('maintenance.db')
    cursor = conn.cursor()

    # Récupérer la reclamation_id avant de supprimer le détail
    cursor.execute("SELECT reclamation_id FROM details_reclamations WHERE id = ?", (id,))
    result = cursor.fetchone()

    if not result:
        flash('Détail non trouvé', 'danger')
        conn.close()
        return redirect(url_for('reclamations'))

    reclamation_id = result[0]

    # Supprimer le détail
    cursor.execute("DELETE FROM details_reclamations WHERE id = ?", (id,))
    conn.commit()

    flash('Détail supprimé avec succès', 'success')
    conn.close()

    return redirect(url_for('voir_reclamation', id=reclamation_id))

@app.route('/interventions/ajouter_detail/<int:id>', methods=['GET', 'POST'])
@login_required
def ajouter_detail_intervention(id):
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Vérifier si l'intervention existe
    cursor.execute("SELECT * FROM interventions WHERE id = ?", (id,))
    intervention = cursor.fetchone()

    if not intervention:
        flash('Intervention non trouvée', 'danger')
        conn.close()
        return redirect(url_for('interventions'))

    # Récupérer les régions et sites pour les listes déroulantes
    cursor.execute("SELECT DISTINCT nom_region FROM regions ORDER BY nom_region")
    regions = cursor.fetchall()

    cursor.execute("""
        SELECT DISTINCT s.nom_site as site, r.nom_region as region
        FROM sites s
        LEFT JOIN regions r ON s.region_id = r.id
        ORDER BY s.nom_site
    """)
    sites = cursor.fetchall()

    if request.method == 'POST':
        # Récupérer les données du formulaire
        region = request.form['region']
        nom_site = request.form['nom_site']
        nbr_systeme = request.form['nbr_systeme']
        date_intervention = request.form['date_intervention']
        technicien = request.form['technicien']
        situation = request.form['situation']
        etat_materiel = request.form['etat_materiel']
        observation = request.form['observation']
        telephone_chef_site = request.form['telephone_chef_site']
        telephone_securite = request.form['telephone_securite']
        technicien_contact = request.form['technicien_contact']
        gps = request.form['gps']

        # Insérer le nouveau détail
        cursor.execute('''
        INSERT INTO details_interventions (intervention_id, region, nom_site, nbr_systeme,
                                         date_intervention, technicien, situation, etat_materiel,
                                         observation, telephone_chef_site, telephone_securite,
                                         technicien_contact, gps)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (id, region, nom_site, nbr_systeme, date_intervention, technicien,
             situation, etat_materiel, observation, telephone_chef_site,
             telephone_securite, technicien_contact, gps))

        conn.commit()
        flash('Détail ajouté avec succès', 'success')
        return redirect(url_for('details_intervention', id=id))

    conn.close()
    return render_template('ajouter_detail_intervention.html', intervention=intervention, regions=regions, sites=sites)

@app.route('/interventions/modifier_detail/<int:id>', methods=['GET', 'POST'])
@login_required
def modifier_detail_intervention(id):
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer le détail d'intervention
    cursor.execute("SELECT * FROM details_interventions WHERE id = ?", (id,))
    detail = cursor.fetchone()

    if not detail:
        flash('Détail d\'intervention non trouvé', 'danger')
        conn.close()
        return redirect(url_for('interventions'))

    # Récupérer l'intervention associée
    cursor.execute("SELECT * FROM interventions WHERE id = ?", (detail['intervention_id'],))
    intervention = cursor.fetchone()

    # Récupérer les régions et sites pour les listes déroulantes
    cursor.execute("SELECT DISTINCT nom_region FROM regions ORDER BY nom_region")
    regions = cursor.fetchall()

    cursor.execute("""
        SELECT DISTINCT s.nom_site as site, r.nom_region as region
        FROM sites s
        LEFT JOIN regions r ON s.region_id = r.id
        ORDER BY s.nom_site
    """)
    sites = cursor.fetchall()

    if request.method == 'POST':
        region = request.form['region']
        nom_site = request.form['nom_site']
        nbr_systeme = request.form['nbr_systeme']
        date_intervention = request.form['date_intervention']
        technicien = request.form['technicien']
        situation = request.form['situation']
        etat_materiel = request.form['etat_materiel']
        observation = request.form['observation']
        telephone_chef_site = request.form['telephone_chef_site']
        telephone_securite = request.form['telephone_securite']
        technicien_contact = request.form['technicien_contact']
        gps = request.form['gps']

        # Mettre à jour le détail
        cursor.execute('''
        UPDATE details_interventions SET
            region = ?, nom_site = ?, nbr_systeme = ?, date_intervention = ?,
            technicien = ?, situation = ?, etat_materiel = ?, observation = ?,
            telephone_chef_site = ?, telephone_securite = ?, technicien_contact = ?, gps = ?
        WHERE id = ?
        ''', (region, nom_site, nbr_systeme, date_intervention, technicien,
             situation, etat_materiel, observation, telephone_chef_site,
             telephone_securite, technicien_contact, gps, id))

        conn.commit()
        flash('Détail mis à jour avec succès', 'success')
        return redirect(url_for('details_intervention', id=detail['intervention_id']))

    conn.close()
    return render_template('modifier_detail_intervention.html', detail=detail, intervention=intervention, regions=regions, sites=sites)




# Route pour la carte du Maroc
@app.route('/carte')
@login_required
def carte():
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    try:
        # Récupérer toutes les régions
        cursor.execute("SELECT * FROM regions ORDER BY nom")
        regions = cursor.fetchall()

        # Récupérer tous les sites avec leurs coordonnées GPS
        cursor.execute("""
        SELECT s.*, r.nom as region_nom
        FROM sites s
        JOIN regions r ON s.region_id = r.id
        ORDER BY r.nom, s.nom
        """)
        sites = cursor.fetchall()

        # Ajouter des données de test si aucune donnée n'existe
        if not sites:
            # Insérer quelques régions de test
            test_regions = [
                ('Casablanca-Settat', 'Région économique du Maroc'),
                ('Rabat-Salé-Kénitra', 'Région administrative'),
                ('Fès-Meknès', 'Région historique'),
                ('Marrakech-Safi', 'Région touristique'),
                ('Tanger-Tétouan-Al Hoceïma', 'Région du nord'),
                ('Souss-Massa', 'Région du sud'),
                ('Oriental', 'Région de l\'est'),
                ('Béni Mellal-Khénifra', 'Région du centre'),
                ('Drâa-Tafilalet', 'Région du sud-est')
            ]

            for nom, desc in test_regions:
                cursor.execute("INSERT OR IGNORE INTO regions (nom, description) VALUES (?, ?)", (nom, desc))

            # Insérer quelques sites de test avec coordonnées GPS précises
            cursor.execute("SELECT id FROM regions LIMIT 9")
            region_ids = [row[0] for row in cursor.fetchall()]

            if region_ids:
                test_sites = [
                    (region_ids[0], 'Site Casablanca Centre', 'Avenue Mohammed V, Casablanca', '0522123456', 'Ahmed Alami', '<EMAIL>', '33.5731,-7.5898'),
                    (region_ids[1], 'Site Rabat Agdal', 'Avenue Allal Ben Abdellah, Rabat', '0537654321', 'Fatima Benali', '<EMAIL>', '34.0209,-6.8416'),
                    (region_ids[2], 'Site Fès Médina', 'Rue Talaa Kebira, Fès', '0535987654', 'Omar Tazi', '<EMAIL>', '34.0181,-5.0078'),
                    (region_ids[3], 'Site Marrakech Gueliz', 'Avenue Mohammed VI, Marrakech', '0524456789', 'Aicha Mansouri', '<EMAIL>', '31.6295,-7.9811'),
                    (region_ids[4], 'Site Tanger Ville', 'Boulevard Pasteur, Tanger', '0539321654', 'Youssef Chraibi', '<EMAIL>', '35.7595,-5.8340'),
                    (region_ids[5], 'Site Agadir Marina', 'Marina d\'Agadir', '0528987654', 'Laila Bennani', '<EMAIL>', '30.4278,-9.5981'),
                    (region_ids[6], 'Site Oujda Centre', 'Boulevard Mohammed V, Oujda', '0536456789', 'Hassan Alaoui', '<EMAIL>', '34.6814,-1.9086'),
                    (region_ids[7], 'Site Beni Mellal', 'Avenue Hassan II, Beni Mellal', '0523789456', 'Nadia Berrada', '<EMAIL>', '32.3373,-6.3498'),
                    (region_ids[8], 'Site Ouarzazate', 'Avenue Mohammed V, Ouarzazate', '0524654321', 'Karim Ouali', '<EMAIL>', '30.9335,-6.9370')
                ]

                for site_data in test_sites:
                    cursor.execute("""
                        INSERT OR IGNORE INTO sites (region_id, nom, adresse, telephone, responsable, email, gps)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, site_data)

            # Insérer quelques marchés de test
            test_marches = [
                ('MAR001', 'INCENDIE', '2024-01-15', 'Maintenance système incendie Casablanca', 'Société ABC Sécurité', 150000, '12 mois', 'Mensuelle', 'Casablanca'),
                ('MAR002', 'CAMERA', '2024-02-01', 'Installation caméras surveillance Rabat', 'Entreprise XYZ Surveillance', 200000, '6 mois', 'Trimestrielle', 'Rabat'),
                ('MAR003', 'EXTINCTEUR', '2024-01-20', 'Maintenance extincteurs Fès', 'SARL DEF Protection', 80000, '24 mois', 'Semestrielle', 'Fès'),
                ('MAR004', 'TELEPHONEE', '2024-03-01', 'Système téléphonique Marrakech', 'Tech Solutions Maroc', 120000, '18 mois', 'Mensuelle', 'Marrakech'),
                ('MAR005', 'SVS', '2024-02-15', 'Système de sécurité Tanger', 'Security Pro Nord', 180000, '12 mois', 'Bimestrielle', 'Tanger'),
                ('MAR006', 'INCENDIE', '2024-03-10', 'Protection incendie Agadir', 'Fire Safety Sud', 95000, '15 mois', 'Mensuelle', 'Agadir'),
                ('MAR007', 'CAMERA', '2024-01-25', 'Surveillance vidéo Oujda', 'Vision Est', 110000, '8 mois', 'Bimestrielle', 'Oujda')
            ]

            for marche_data in test_marches:
                cursor.execute("""
                    INSERT OR IGNORE INTO marches (numero, domaine, date, objet, client, montant, delai_execution, periode_interventions, lieu)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, marche_data)

            conn.commit()

            # Récupérer les données mises à jour
            cursor.execute("""
            SELECT s.*, r.nom as region_nom
            FROM sites s
            JOIN regions r ON s.region_id = r.id
            ORDER BY r.nom, s.nom
            """)
            sites = cursor.fetchall()

        # Récupérer les marchés
        cursor.execute("""
        SELECT m.*,
               COUNT(DISTINCT i.id) as interventions_count,
               COUNT(DISTINCT r.id) as reclamations_count
        FROM marches m
        LEFT JOIN interventions i ON m.numero = i.numero_marche
        LEFT JOIN reclamations r ON m.numero = r.numero_marche
        GROUP BY m.id, m.numero, m.domaine, m.date, m.objet, m.client, m.montant, m.delai_execution, m.periode_interventions, m.lieu, m.date_ordre_service, m.caution_definitif, m.mode_paiement, m.gps
        ORDER BY m.date DESC
        """)
        marches = cursor.fetchall()

        # Récupérer les interventions avec coordonnées GPS
        cursor.execute("""
        SELECT i.id, i.client, i.domaine, i.lieu, d.region, d.nom_site, d.gps, d.situation, d.etat_materiel
        FROM interventions i
        JOIN details_interventions d ON i.id = d.intervention_id
        ORDER BY i.date_creation DESC
        """)
        interventions = cursor.fetchall()

        # Récupérer les réclamations avec coordonnées GPS
        cursor.execute("""
        SELECT r.id, r.client, r.domaine, r.lieu, d.region, d.nom_site, d.gps, d.situation, d.etat_materiel
        FROM reclamations r
        JOIN details_reclamations d ON r.id = d.reclamation_id
        ORDER BY r.date_creation DESC
        """)
        reclamations = cursor.fetchall()

    except Exception as e:
        print(f"Erreur lors de la récupération des données: {e}")
        # Valeurs par défaut en cas d'erreur
        regions = []
        sites = []
        marches = []
        interventions = []
        reclamations = []

    finally:
        conn.close()

    # Convertir les Row objects en dictionnaires pour JSON serialization
    regions_list = [dict(row) for row in regions] if regions else []
    sites_list = [dict(row) for row in sites] if sites else []
    marches_list = [dict(row) for row in marches] if marches else []
    interventions_list = [dict(row) for row in interventions] if interventions else []
    reclamations_list = [dict(row) for row in reclamations] if reclamations else []

    # Enregistrer l'activité
    log_user_activity("Consultation", "Carte du Maroc", f"Consultation de la carte avec {len(sites_list)} sites, {len(marches_list)} marchés")

    return render_template('carte_simple.html',
                          regions=regions_list,
                          sites=sites_list,
                          marches=marches_list,
                          interventions=interventions_list,
                          reclamations=reclamations_list)

# Route de test pour la carte
@app.route('/carte_test')
@login_required
def carte_test():
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    try:
        # Récupérer tous les sites avec leurs coordonnées GPS
        cursor.execute("""
        SELECT s.*, r.nom as region_nom
        FROM sites s
        JOIN regions r ON s.region_id = r.id
        ORDER BY r.nom, s.nom
        """)
        sites = cursor.fetchall()

        # Récupérer les marchés
        cursor.execute("""
        SELECT m.*,
               COUNT(DISTINCT i.id) as interventions_count,
               COUNT(DISTINCT r.id) as reclamations_count
        FROM marches m
        LEFT JOIN interventions i ON m.numero = i.numero_marche
        LEFT JOIN reclamations r ON m.numero = r.numero_marche
        GROUP BY m.id, m.numero, m.domaine, m.date, m.objet, m.client, m.montant, m.delai_execution, m.periode_interventions, m.lieu, m.date_ordre_service, m.caution_definitif, m.mode_paiement, m.gps
        ORDER BY m.date DESC
        """)
        marches = cursor.fetchall()

        # Récupérer les interventions avec coordonnées GPS
        cursor.execute("""
        SELECT i.id, i.client, i.domaine, i.lieu, d.region, d.nom_site, d.gps, d.situation, d.etat_materiel
        FROM interventions i
        JOIN details_interventions d ON i.id = d.intervention_id
        ORDER BY i.date_creation DESC
        """)
        interventions = cursor.fetchall()

        # Récupérer les réclamations avec coordonnées GPS
        cursor.execute("""
        SELECT r.id, r.client, r.domaine, r.lieu, d.region, d.nom_site, d.gps, d.situation, d.etat_materiel
        FROM reclamations r
        JOIN details_reclamations d ON r.id = d.reclamation_id
        ORDER BY r.date_creation DESC
        """)
        reclamations = cursor.fetchall()

    except Exception as e:
        print(f"Erreur lors de la récupération des données: {e}")
        sites = []
        marches = []
        interventions = []
        reclamations = []

    conn.close()

    # Convertir les Row objects en dictionnaires pour JSON serialization
    sites_list = [dict(row) for row in sites] if sites else []
    marches_list = [dict(row) for row in marches] if marches else []
    interventions_list = [dict(row) for row in interventions] if interventions else []
    reclamations_list = [dict(row) for row in reclamations] if reclamations else []

    return render_template('carte_test.html',
                          sites=sites_list,
                          marches=marches_list,
                          interventions=interventions_list,
                          reclamations=reclamations_list)

# Route pour la gestion de la base de données
@app.route('/database-management')
@login_required
def database_management():
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    try:
        # Compter les enregistrements dans chaque table
        cursor.execute("SELECT COUNT(*) as count FROM sites")
        sites_count = cursor.fetchone()['count']

        cursor.execute("SELECT COUNT(*) as count FROM marches")
        marches_count = cursor.fetchone()['count']

        cursor.execute("SELECT COUNT(*) as count FROM interventions")
        interventions_count = cursor.fetchone()['count']

        cursor.execute("SELECT COUNT(*) as count FROM reclamations")
        reclamations_count = cursor.fetchone()['count']

        conn.close()

        # Enregistrer l'activité
        log_user_activity("Consultation", "Gestion Base de Données", "Accès à la page de gestion de base de données")

        return render_template('database_management.html',
                             sites_count=sites_count,
                             marches_count=marches_count,
                             interventions_count=interventions_count,
                             reclamations_count=reclamations_count)
    except Exception as e:
        conn.close()
        flash(f'Erreur lors du chargement de la gestion de base de données: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

# API Routes pour l'export/import de base de données
@app.route('/api/export/<format>')
@login_required
def api_export_database(format):
    """API pour exporter la base de données"""
    if session.get('role') != 'admin':
        return jsonify({'error': 'Accès non autorisé'}), 403

    try:
        import os
        import shutil
        from datetime import datetime

        # Créer le dossier de sauvegarde s'il n'existe pas
        backup_dir = 'backups'
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        if format == 'sql':
            # Copier le fichier de base de données SQLite
            backup_filename = f'maintenance_backup_{timestamp}.db'
            backup_path = os.path.join(backup_dir, backup_filename)
            shutil.copy2('maintenance.db', backup_path)

            # Enregistrer l'activité
            log_user_activity("Export", "Base de Données", f"Export SQL: {backup_filename}")

            return send_from_directory(backup_dir, backup_filename, as_attachment=True)

        elif format == 'excel':
            # Export vers Excel
            import pandas as pd

            backup_filename = f'maintenance_export_{timestamp}.xlsx'
            backup_path = os.path.join(backup_dir, backup_filename)

            conn = sqlite3.connect('maintenance.db')

            with pd.ExcelWriter(backup_path, engine='openpyxl') as writer:
                # Exporter chaque table
                tables = ['sites', 'marches', 'interventions', 'reclamations', 'regions', 'users']
                for table in tables:
                    try:
                        df = pd.read_sql_query(f"SELECT * FROM {table}", conn)
                        df.to_excel(writer, sheet_name=table, index=False)
                    except Exception as e:
                        print(f"Erreur lors de l'export de la table {table}: {e}")

            conn.close()

            # Enregistrer l'activité
            log_user_activity("Export", "Base de Données", f"Export Excel: {backup_filename}")

            return send_from_directory(backup_dir, backup_filename, as_attachment=True)

        elif format == 'json':
            # Export vers JSON
            import json

            backup_filename = f'maintenance_export_{timestamp}.json'
            backup_path = os.path.join(backup_dir, backup_filename)

            conn = sqlite3.connect('maintenance.db')
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()

            data = {}
            tables = ['sites', 'marches', 'interventions', 'reclamations', 'regions', 'users']

            for table in tables:
                try:
                    cursor.execute(f"SELECT * FROM {table}")
                    rows = cursor.fetchall()
                    data[table] = [dict(row) for row in rows]
                except Exception as e:
                    print(f"Erreur lors de l'export de la table {table}: {e}")
                    data[table] = []

            conn.close()

            with open(backup_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)

            # Enregistrer l'activité
            log_user_activity("Export", "Base de Données", f"Export JSON: {backup_filename}")

            return send_from_directory(backup_dir, backup_filename, as_attachment=True)

        else:
            return jsonify({'error': 'Format non supporté'}), 400

    except Exception as e:
        print(f"Erreur lors de l'export: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/import', methods=['POST'])
@login_required
def api_import_database():
    """API pour importer des données"""
    if session.get('role') != 'admin':
        return jsonify({'error': 'Accès non autorisé'}), 403

    try:
        if 'file' not in request.files:
            return jsonify({'error': 'Aucun fichier sélectionné'}), 400

        file = request.files['file']
        import_type = request.form.get('import_type', 'append')

        if file.filename == '':
            return jsonify({'error': 'Aucun fichier sélectionné'}), 400

        # Sauvegarder le fichier temporairement
        import os
        from datetime import datetime

        upload_dir = 'uploads'
        if not os.path.exists(upload_dir):
            os.makedirs(upload_dir)

        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"import_{timestamp}_{file.filename}"
        filepath = os.path.join(upload_dir, filename)
        file.save(filepath)

        # Traiter selon le type de fichier
        if file.filename.endswith('.db'):
            # Import de base de données SQLite
            if import_type == 'replace':
                # Faire une sauvegarde avant remplacement
                backup_filename = f'backup_before_import_{timestamp}.db'
                backup_path = os.path.join('backups', backup_filename)
                shutil.copy2('maintenance.db', backup_path)

                # Remplacer la base de données
                shutil.copy2(filepath, 'maintenance.db')

        elif file.filename.endswith(('.xlsx', '.xls')):
            # Import depuis Excel
            import pandas as pd

            conn = sqlite3.connect('maintenance.db')

            # Lire le fichier Excel
            excel_file = pd.ExcelFile(filepath)

            for sheet_name in excel_file.sheet_names:
                if sheet_name in ['sites', 'marches', 'interventions', 'reclamations', 'regions', 'users']:
                    df = pd.read_excel(filepath, sheet_name=sheet_name)

                    if import_type == 'replace':
                        # Vider la table avant import
                        conn.execute(f"DELETE FROM {sheet_name}")

                    # Insérer les données
                    df.to_sql(sheet_name, conn, if_exists='append', index=False)

            conn.commit()
            conn.close()

        elif file.filename.endswith('.json'):
            # Import depuis JSON
            import json

            with open(filepath, 'r', encoding='utf-8') as f:
                data = json.load(f)

            conn = sqlite3.connect('maintenance.db')
            cursor = conn.cursor()

            for table_name, records in data.items():
                if table_name in ['sites', 'marches', 'interventions', 'reclamations', 'regions', 'users']:
                    if import_type == 'replace':
                        cursor.execute(f"DELETE FROM {table_name}")

                    # Insérer les enregistrements
                    for record in records:
                        columns = ', '.join(record.keys())
                        placeholders = ', '.join(['?' for _ in record.keys()])
                        values = list(record.values())

                        cursor.execute(f"INSERT INTO {table_name} ({columns}) VALUES ({placeholders})", values)

            conn.commit()
            conn.close()

        # Nettoyer le fichier temporaire
        os.remove(filepath)

        # Enregistrer l'activité
        log_user_activity("Import", "Base de Données", f"Import {import_type}: {file.filename}")

        return jsonify({'success': True, 'message': 'Import réussi'})

    except Exception as e:
        print(f"Erreur lors de l'import: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/backup-history')
@login_required
def api_backup_history():
    """API pour récupérer l'historique des sauvegardes"""
    if session.get('role') != 'admin':
        return jsonify({'error': 'Accès non autorisé'}), 403

    try:
        import os
        from datetime import datetime

        backup_dir = 'backups'
        if not os.path.exists(backup_dir):
            return jsonify([])

        backups = []
        for filename in os.listdir(backup_dir):
            filepath = os.path.join(backup_dir, filename)
            if os.path.isfile(filepath):
                stat = os.stat(filepath)
                size = stat.st_size
                modified = datetime.fromtimestamp(stat.st_mtime)

                # Déterminer le type
                if filename.endswith('.db'):
                    file_type = 'sql'
                elif filename.endswith('.xlsx'):
                    file_type = 'excel'
                elif filename.endswith('.json'):
                    file_type = 'json'
                else:
                    file_type = 'unknown'

                backups.append({
                    'filename': filename,
                    'size': size,
                    'size_mb': round(size / (1024 * 1024), 2),
                    'modified': modified.strftime('%Y-%m-%d %H:%M:%S'),
                    'type': file_type
                })

        # Trier par date de modification (plus récent en premier)
        backups.sort(key=lambda x: x['modified'], reverse=True)

        return jsonify(backups)

    except Exception as e:
        print(f"Erreur lors de la récupération de l'historique: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/backup-config', methods=['GET', 'POST'])
@login_required
def api_backup_config():
    """API pour gérer la configuration des sauvegardes automatiques"""
    if session.get('role') != 'admin':
        return jsonify({'error': 'Accès non autorisé'}), 403

    try:
        import json
        config_file = 'backup_config.json'

        if request.method == 'GET':
            # Récupérer la configuration actuelle
            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
            else:
                # Configuration par défaut
                config = {
                    'daily_backup': {
                        'enabled': True,
                        'time': '02:00',
                        'keep_days': 7
                    },
                    'weekly_backup': {
                        'enabled': True,
                        'day': 'sunday',
                        'time': '03:00',
                        'keep_weeks': 4
                    },
                    'monthly_backup': {
                        'enabled': True,
                        'day': 1,
                        'time': '04:00',
                        'keep_months': 12
                    },
                    'auto_cleanup': {
                        'enabled': True,
                        'max_backup_size_gb': 5.0
                    }
                }

            return jsonify(config)

        elif request.method == 'POST':
            # Mettre à jour la configuration
            new_config = request.get_json()

            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(new_config, f, indent=2, ensure_ascii=False)

            # Redémarrer le planificateur si nécessaire
            try:
                from backup_scheduler import BackupScheduler
                global backup_scheduler
                if 'backup_scheduler' in globals():
                    backup_scheduler.stop()
                backup_scheduler = BackupScheduler()
                backup_scheduler.run_in_background()
            except Exception as e:
                print(f"Erreur lors du redémarrage du planificateur: {e}")

            # Enregistrer l'activité
            log_user_activity("Configuration", "Sauvegarde Automatique", "Mise à jour de la configuration")

            return jsonify({'success': True, 'message': 'Configuration mise à jour'})

    except Exception as e:
        print(f"Erreur lors de la gestion de la configuration: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/manual-backup')
@login_required
def api_manual_backup():
    """API pour créer une sauvegarde manuelle"""
    if session.get('role') != 'admin':
        return jsonify({'error': 'Accès non autorisé'}), 403

    try:
        from backup_scheduler import BackupScheduler
        scheduler = BackupScheduler()
        backup_file = scheduler.create_backup('manual')

        if backup_file:
            # Enregistrer l'activité
            log_user_activity("Sauvegarde", "Manuelle", f"Sauvegarde créée: {backup_file}")
            return jsonify({'success': True, 'filename': backup_file})
        else:
            return jsonify({'error': 'Échec de la création de la sauvegarde'}), 500

    except Exception as e:
        print(f"Erreur lors de la sauvegarde manuelle: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/delete-backup/<filename>', methods=['DELETE'])
@login_required
def api_delete_backup(filename):
    """API pour supprimer une sauvegarde"""
    if session.get('role') != 'admin':
        return jsonify({'error': 'Accès non autorisé'}), 403

    try:
        import os
        backup_dir = 'backups'
        filepath = os.path.join(backup_dir, filename)

        # Vérifier que le fichier existe et est dans le dossier backups
        if not os.path.exists(filepath):
            return jsonify({'error': 'Fichier de sauvegarde non trouvé'}), 404

        # Vérifier que le chemin est sécurisé (dans le dossier backups)
        if not os.path.commonpath([os.path.abspath(backup_dir), os.path.abspath(filepath)]) == os.path.abspath(backup_dir):
            return jsonify({'error': 'Accès non autorisé au fichier'}), 403

        # Supprimer le fichier
        os.remove(filepath)

        # Logger l'action
        log_user_activity("Suppression", "Sauvegarde", f"Suppression de la sauvegarde: {filename}")

        return jsonify({'success': True, 'message': f'Sauvegarde {filename} supprimée avec succès'})
    except Exception as e:
        print(f"Erreur lors de la suppression: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/download-backup/<filename>')
@login_required
def api_download_backup(filename):
    """API pour télécharger une sauvegarde"""
    if session.get('role') != 'admin':
        return jsonify({'error': 'Accès non autorisé'}), 403

    try:
        import os
        backup_dir = 'backups'
        filepath = os.path.join(backup_dir, filename)

        # Vérifier que le fichier existe et est dans le dossier backups
        if not os.path.exists(filepath):
            return jsonify({'error': 'Fichier de sauvegarde non trouvé'}), 404

        # Vérifier que le chemin est sécurisé
        if not os.path.commonpath([os.path.abspath(backup_dir), os.path.abspath(filepath)]) == os.path.abspath(backup_dir):
            return jsonify({'error': 'Accès non autorisé au fichier'}), 403

        # Logger l'action
        log_user_activity("Téléchargement", "Sauvegarde", f"Téléchargement de la sauvegarde: {filename}")

        return send_from_directory(backup_dir, filename, as_attachment=True)
    except Exception as e:
        print(f"Erreur lors du téléchargement: {e}")
        return jsonify({'error': str(e)}), 500

# API Routes pour les données dynamiques
@app.route('/api/regions')
@login_required
def api_get_regions():
    """API pour récupérer la liste des régions"""
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        try:
            cursor.execute("SELECT * FROM regions ORDER BY COALESCE(nom_region, nom)")
        except sqlite3.OperationalError:
            try:
                cursor.execute("SELECT * FROM regions ORDER BY nom")
            except:
                cursor.execute("SELECT * FROM regions ORDER BY id")

        regions = cursor.fetchall()
        conn.close()

        regions_list = []
        for region in regions:
            region_dict = dict(region)
            region_id = region_dict.get('id', 0)
            nom_region = region_dict.get('nom_region')
            nom = region_dict.get('nom')
            display_name = nom_region if nom_region else (nom if nom else f'Région {region_id}')

            regions_list.append({
                'id': region_id,
                'nom': display_name,
                'nom_region': display_name
            })

        return jsonify(regions_list)
    except Exception as e:
        print(f"❌ Erreur API Regions: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/sites/<int:region_id>')
@login_required
def api_get_sites_by_region(region_id):
    """API pour récupérer les sites d'une région"""
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # جرب أسماء أعمدة مختلفة للمواقع
        try:
            cursor.execute("SELECT id, nom_site FROM sites WHERE region_id = ? ORDER BY nom_site", (region_id,))
        except:
            try:
                cursor.execute("SELECT id, nom FROM sites WHERE region_id = ? ORDER BY nom", (region_id,))
            except:
                cursor.execute("SELECT * FROM sites WHERE region_id = ? ORDER BY id", (region_id,))

        sites = cursor.fetchall()
        conn.close()

        sites_list = []
        for site in sites:
            site_dict = dict(site)
            site_id = site_dict.get('id', 0)
            nom_site = site_dict.get('nom_site')
            nom = site_dict.get('nom')
            display_name = nom_site if nom_site else (nom if nom else f'Site {site_id}')

            sites_list.append({
                'id': site_id,
                'nom': display_name,
                'nom_site': display_name,
                'region_id': region_id
            })

        return jsonify(sites_list)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/marches')
@login_required
def api_get_marches():
    """API pour récupérer la liste des marchés"""
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()


        try:
            cursor.execute("SELECT numero_marche as numero, objet_marche as objet FROM marches ORDER BY numero_marche")
        except:
            try:
                cursor.execute("SELECT numero, objet FROM marches ORDER BY numero")
            except:
                cursor.execute("SELECT * FROM marches ORDER BY id LIMIT 10")

        marches = cursor.fetchall()
        conn.close()

        marches_list = []
        for marche in marches:
            # تحديد الحقول المتاحة مع معالجة أفضل
            marche_dict = dict(marche)
            numero = marche_dict.get('numero') or marche_dict.get('numero_marche') or str(marche_dict.get('id', ''))
            objet = marche_dict.get('objet') or marche_dict.get('objet_marche') or 'Marché sans objet'

            marches_list.append({
                'numero': str(numero),
                'objet': str(objet)
            })

        print(f"📡 API Marches: إرجاع {len(marches_list)} مشروع")
        for marche in marches_list[:3]:  # عرض أول 3 مشاريع للتحقق
            print(f"  - Numero: {marche['numero']}, Objet: {marche['objet']}")

        return jsonify(marches_list)
    except Exception as e:
        return jsonify({'error': str(e)}), 500

# Routes pour la gestion des régions et sites
@app.route('/regions')
@login_required
def regions():
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Récupérer toutes les régions avec gestion d'erreur
        try:
            cursor.execute("SELECT * FROM regions ORDER BY COALESCE(nom_region, nom) ASC")
        except sqlite3.OperationalError:
            # Si nom_region n'existe pas, utiliser nom
            cursor.execute("SELECT * FROM regions ORDER BY nom ASC")

        regions = cursor.fetchall()

        # Récupérer le nombre de sites par région
        regions_with_count = []
        for region in regions:
            try:
                cursor.execute("SELECT COUNT(*) as count FROM sites WHERE region_id = ?", (region['id'],))
                count = cursor.fetchone()['count']
            except:
                count = 0

            region_dict = dict(region)
            region_dict['sites_count'] = count
            # S'assurer que nom_region existe
            if 'nom_region' not in region_dict or not region_dict['nom_region']:
                region_dict['nom_region'] = region_dict.get('nom', f'Région {region_dict["id"]}')
            regions_with_count.append(region_dict)

        conn.close()

        return render_template('regions.html', regions=regions_with_count)

    except Exception as e:
        print(f"Erreur dans /regions: {e}")
        import traceback
        traceback.print_exc()
        flash(f'Erreur lors du chargement des régions: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/regions/ajouter', methods=['GET', 'POST'])
@login_required
def ajouter_region():
    if request.method == 'POST':
        nom_region = request.form['nom_region']
        description = request.form.get('description', '')

        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        # Vérifier si la région existe déjà
        cursor.execute("SELECT * FROM regions WHERE nom_region = ?", (nom_region,))
        if cursor.fetchone():
            flash('Cette région existe déjà', 'danger')
            conn.close()
            return redirect(url_for('ajouter_region'))

        # Insérer la nouvelle région
        cursor.execute('''
        INSERT INTO regions (nom_region, description)
        VALUES (?, ?)
        ''', (nom_region, description))

        conn.commit()
        conn.close()

        flash('Région ajoutée avec succès', 'success')
        return redirect(url_for('regions'))

    return render_template('ajouter_region.html')

@app.route('/regions/modifier/<int:id>', methods=['GET', 'POST'])
@login_required
def modifier_region(id):
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer la région
    cursor.execute("SELECT * FROM regions WHERE id = ?", (id,))
    region = cursor.fetchone()

    if not region:
        flash('Région non trouvée', 'danger')
        conn.close()
        return redirect(url_for('regions'))

    if request.method == 'POST':
        nom_region = request.form['nom_region']
        description = request.form.get('description', '')

        # Vérifier si le nom existe déjà pour une autre région
        cursor.execute("SELECT * FROM regions WHERE nom_region = ? AND id != ?", (nom_region, id))
        if cursor.fetchone():
            flash('Ce nom de région est déjà utilisé', 'danger')
        else:
            # Mettre à jour la région
            cursor.execute('''
            UPDATE regions SET nom_region = ?, nom = ?, description = ?
            WHERE id = ?
            ''', (nom_region, nom_region, description, id))

            conn.commit()
            flash('Région mise à jour avec succès', 'success')
            return redirect(url_for('regions'))

    conn.close()
    return render_template('modifier_region.html', region=region)

@app.route('/regions/supprimer/<int:id>')
@login_required
def supprimer_region(id):
    conn = sqlite3.connect('maintenance.db')
    cursor = conn.cursor()

    # Vérifier si la région existe
    cursor.execute("SELECT * FROM regions WHERE id = ?", (id,))
    if not cursor.fetchone():
        flash('Région non trouvée', 'danger')
        conn.close()
        return redirect(url_for('regions'))

    # Vérifier si la région a des sites
    cursor.execute("SELECT COUNT(*) FROM sites WHERE region_id = ?", (id,))
    count = cursor.fetchone()[0]

    if count > 0:
        flash('Impossible de supprimer cette région car elle contient des sites', 'danger')
    else:
        cursor.execute("DELETE FROM regions WHERE id = ?", (id,))
        conn.commit()
        flash('Région supprimée avec succès', 'success')

    conn.close()
    return redirect(url_for('regions'))

@app.route('/sites')
@login_required
def sites():
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()

        # Récupérer les filtres s'ils existent
        region_id = request.args.get('region_id', '')

        # Construire la requête SQL avec les filtres
        query = "SELECT s.*, COALESCE(r.nom_region, r.nom) as region_nom FROM sites s LEFT JOIN regions r ON s.region_id = r.id WHERE 1=1"
        params = []

        if region_id:
            query += " AND s.region_id = ?"
            params.append(region_id)

        query += " ORDER BY COALESCE(r.nom_region, r.nom), COALESCE(s.nom_site, s.nom)"

        cursor.execute(query, params)
        sites = cursor.fetchall()

        # Récupérer la liste des régions pour le filtre
        try:
            cursor.execute("SELECT * FROM regions ORDER BY COALESCE(nom_region, nom)")
        except sqlite3.OperationalError:
            cursor.execute("SELECT * FROM regions ORDER BY nom")
        regions = cursor.fetchall()

        conn.close()

        return render_template('sites.html', sites=sites, regions=regions, region_id=region_id)

    except Exception as e:
        print(f"Erreur dans /sites: {e}")
        flash(f'Erreur lors du chargement des sites: {str(e)}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/sites/ajouter', methods=['GET', 'POST'])
@login_required
def ajouter_site():
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer la liste des régions pour le formulaire
    try:
        cursor.execute("SELECT * FROM regions ORDER BY COALESCE(nom_region, nom)")
    except sqlite3.OperationalError:
        cursor.execute("SELECT * FROM regions ORDER BY nom")
    regions = cursor.fetchall()

    if request.method == 'POST':
        region_id = request.form['region_id']
        nom_site = request.form['nom_site']
        adresse = request.form.get('adresse', '')
        coordonnees = request.form.get('coordonnees', '')

        # Vérifier si la région existe
        cursor.execute("SELECT * FROM regions WHERE id = ?", (region_id,))
        if not cursor.fetchone():
            flash('Région non trouvée', 'danger')
            conn.close()
            return redirect(url_for('ajouter_site'))

        # Vérifier si le site existe déjà dans cette région
        try:
            cursor.execute("SELECT * FROM sites WHERE region_id = ? AND nom_site = ?", (region_id, nom_site))
        except sqlite3.OperationalError:
            # Si nom_site n'existe pas, utiliser nom
            cursor.execute("SELECT * FROM sites WHERE region_id = ? AND nom = ?", (region_id, nom_site))

        if cursor.fetchone():
            flash('Ce site existe déjà dans cette région', 'danger')
            conn.close()
            return redirect(url_for('ajouter_site'))

        # Insérer le nouveau site
        cursor.execute('''
        INSERT INTO sites (region_id, nom, nom_site, adresse, gps)
        VALUES (?, ?, ?, ?, ?)
        ''', (region_id, nom_site, nom_site, adresse, coordonnees))

        conn.commit()
        conn.close()

        flash('Site ajouté avec succès', 'success')
        return redirect(url_for('sites'))

    conn.close()
    return render_template('ajouter_site.html', regions=regions)

@app.route('/sites/modifier/<int:id>', methods=['GET', 'POST'])
@login_required
def modifier_site(id):
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer le site
    cursor.execute("SELECT * FROM sites WHERE id = ?", (id,))
    site = cursor.fetchone()

    if not site:
        flash('Site non trouvé', 'danger')
        conn.close()
        return redirect(url_for('sites'))

    # Récupérer la liste des régions pour le formulaire
    try:
        cursor.execute("SELECT * FROM regions ORDER BY COALESCE(nom_region, nom)")
    except sqlite3.OperationalError:
        cursor.execute("SELECT * FROM regions ORDER BY nom")
    regions = cursor.fetchall()

    if request.method == 'POST':
        region_id = request.form['region_id']
        nom_site = request.form['nom_site']
        adresse = request.form.get('adresse', '')
        coordonnees = request.form.get('coordonnees', '')

        # Vérifier si le site existe déjà dans cette région (pour un autre site)
        try:
            cursor.execute("SELECT * FROM sites WHERE region_id = ? AND nom_site = ? AND id != ?", (region_id, nom_site, id))
        except sqlite3.OperationalError:
            cursor.execute("SELECT * FROM sites WHERE region_id = ? AND nom = ? AND id != ?", (region_id, nom_site, id))

        if cursor.fetchone():
            flash('Ce nom de site est déjà utilisé dans cette région', 'danger')
        else:
            # Mettre à jour le site
            cursor.execute('''
            UPDATE sites SET region_id = ?, nom = ?, nom_site = ?, adresse = ?, gps = ?
            WHERE id = ?
            ''', (region_id, nom_site, nom_site, adresse, coordonnees, id))

            conn.commit()
            flash('Site mis à jour avec succès', 'success')
            return redirect(url_for('sites'))

    conn.close()
    return render_template('modifier_site.html', site=site, regions=regions)

@app.route('/sites/supprimer/<int:id>')
@login_required
def supprimer_site(id):
    conn = sqlite3.connect('maintenance.db')
    cursor = conn.cursor()

    # Vérifier si le site existe
    cursor.execute("SELECT * FROM sites WHERE id = ?", (id,))
    if not cursor.fetchone():
        flash('Site non trouvé', 'danger')
        conn.close()
        return redirect(url_for('sites'))

    # Supprimer le site
    cursor.execute("DELETE FROM sites WHERE id = ?", (id,))
    conn.commit()
    flash('Site supprimé avec succès', 'success')

    conn.close()
    return redirect(url_for('sites'))

# Route pour servir les fichiers uploadés
@app.route('/uploads/<filename>')
def uploaded_file(filename):
    """Servir les fichiers uploadés de manière sécurisée"""
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

# Routes pour les rapports d'impression
@app.route('/rapport/intervention/<int:id>')
@login_required
def rapport_intervention(id):
    """Générer un rapport d'intervention pour impression"""
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer l'intervention
    cursor.execute("SELECT * FROM interventions WHERE id = ?", (id,))
    intervention = cursor.fetchone()

    if not intervention:
        flash('Intervention non trouvée', 'danger')
        return redirect(url_for('interventions'))

    # Récupérer les détails de l'intervention
    cursor.execute("SELECT * FROM details_interventions WHERE intervention_id = ?", (id,))
    details = cursor.fetchall()

    # Récupérer les détails des extincteurs si applicable
    extincteurs = []
    if intervention['domaine'] == 'EXTINCTEUR':
        cursor.execute("SELECT * FROM details_extincteurs WHERE intervention_id = ?", (id,))
        extincteurs = cursor.fetchall()

    # Récupérer les informations de la société
    cursor.execute("SELECT * FROM societe LIMIT 1")
    societe = cursor.fetchone()

    conn.close()

    # Ajouter la date actuelle
    from datetime import datetime
    date_now = datetime.now().strftime('%d/%m/%Y %H:%M')

    return render_template('rapport_intervention.html',
                         intervention=intervention,
                         details=details,
                         extincteurs=extincteurs,
                         societe=societe,
                         date_now=date_now)

@app.route('/rapport/marche/<numero>')
@login_required
def rapport_marche(numero):
    """Générer un rapport de marché pour impression"""
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer le marché
    cursor.execute("SELECT * FROM marches WHERE numero = ?", (numero,))
    marche = cursor.fetchone()

    if not marche:
        flash('Marché non trouvé', 'danger')
        return redirect(url_for('marches'))

    # Récupérer les interventions liées
    cursor.execute("SELECT * FROM interventions WHERE numero_marche = ? ORDER BY date_creation DESC", (numero,))
    interventions = cursor.fetchall()

    # Récupérer les réclamations liées
    cursor.execute("SELECT * FROM reclamations WHERE numero_marche = ? ORDER BY date_creation DESC", (numero,))
    reclamations = cursor.fetchall()

    # Récupérer les informations de la société
    cursor.execute("SELECT * FROM societe LIMIT 1")
    societe = cursor.fetchone()

    conn.close()

    # Ajouter la date actuelle
    from datetime import datetime
    date_now = datetime.now().strftime('%d/%m/%Y %H:%M')

    return render_template('rapport_marche.html',
                         marche=marche,
                         interventions=interventions,
                         reclamations=reclamations,
                         societe=societe,
                         date_now=date_now)

# Route pour le journal des activités des utilisateurs
@app.route('/logs')
@login_required
@admin_required
def logs_utilisateurs():
    """Afficher le journal des activités des utilisateurs"""
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer les filtres
    utilisateur_filter = request.args.get('utilisateur', '')
    module_filter = request.args.get('module', '')
    action_filter = request.args.get('action', '')
    date_debut = request.args.get('date_debut', '')
    date_fin = request.args.get('date_fin', '')

    # Construire la requête avec filtres
    query = "SELECT * FROM logs_utilisateurs WHERE 1=1"
    params = []

    if utilisateur_filter:
        query += " AND nom_utilisateur LIKE ?"
        params.append(f"%{utilisateur_filter}%")

    if module_filter:
        query += " AND module = ?"
        params.append(module_filter)

    if action_filter:
        query += " AND action LIKE ?"
        params.append(f"%{action_filter}%")

    if date_debut:
        query += " AND date(date_creation) >= ?"
        params.append(date_debut)

    if date_fin:
        query += " AND date(date_creation) <= ?"
        params.append(date_fin)

    query += " ORDER BY date_creation DESC LIMIT 1000"

    cursor.execute(query, params)
    logs = cursor.fetchall()

    # Récupérer les modules et actions uniques pour les filtres
    cursor.execute("SELECT DISTINCT module FROM logs_utilisateurs ORDER BY module")
    modules = [row['module'] for row in cursor.fetchall()]

    cursor.execute("SELECT DISTINCT action FROM logs_utilisateurs ORDER BY action")
    actions = [row['action'] for row in cursor.fetchall()]

    cursor.execute("SELECT DISTINCT nom_utilisateur FROM logs_utilisateurs ORDER BY nom_utilisateur")
    utilisateurs = [row['nom_utilisateur'] for row in cursor.fetchall()]

    conn.close()

    # Enregistrer l'activité de consultation des logs
    log_user_activity("Consultation", "Journal des activités", f"Filtres: utilisateur={utilisateur_filter}, module={module_filter}")

    return render_template('logs_utilisateurs.html',
                         logs=logs,
                         modules=modules,
                         actions=actions,
                         utilisateurs=utilisateurs,
                         filters={
                             'utilisateur': utilisateur_filter,
                             'module': module_filter,
                             'action': action_filter,
                             'date_debut': date_debut,
                             'date_fin': date_fin
                         })

@app.route('/rapport/reclamation/<int:id>')
@login_required
def rapport_reclamation(id):
    """Générer un rapport de réclamation pour impression"""
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer la réclamation
    cursor.execute("SELECT * FROM reclamations WHERE id = ?", (id,))
    reclamation = cursor.fetchone()

    if not reclamation:
        flash('Réclamation non trouvée', 'danger')
        return redirect(url_for('reclamations'))

    # Récupérer les détails de la réclamation
    cursor.execute("SELECT * FROM details_reclamations WHERE reclamation_id = ?", (id,))
    details = cursor.fetchall()

    # Récupérer les informations de la société
    cursor.execute("SELECT * FROM societe LIMIT 1")
    societe = cursor.fetchone()

    conn.close()

    # Ajouter la date actuelle
    from datetime import datetime
    date_now = datetime.now().strftime('%d/%m/%Y %H:%M')

    return render_template('rapport_reclamation.html',
                         reclamation=reclamation,
                         details=details,
                         societe=societe,
                         date_now=date_now)

@app.route('/rapport/sites')
@login_required
def rapport_sites():
    """Générer un rapport des sites pour impression"""
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer tous les sites avec leurs régions
    cursor.execute("""
    SELECT s.*, r.nom as region_nom
    FROM sites s
    JOIN regions r ON s.region_id = r.id
    ORDER BY r.nom, s.nom
    """)
    sites = cursor.fetchall()

    # Récupérer les informations de la société
    cursor.execute("SELECT * FROM societe LIMIT 1")
    societe = cursor.fetchone()

    conn.close()

    # Ajouter la date actuelle
    from datetime import datetime
    date_now = datetime.now().strftime('%d/%m/%Y %H:%M')

    return render_template('rapport_sites.html',
                         sites=sites,
                         societe=societe,
                         date_now=date_now)

@app.route('/rapport/utilisateurs')
@login_required
def rapport_utilisateurs():
    """Générer un rapport des utilisateurs pour impression"""
    conn = sqlite3.connect('maintenance.db')
    conn.row_factory = sqlite3.Row
    cursor = conn.cursor()

    # Récupérer tous les utilisateurs
    cursor.execute("SELECT * FROM utilisateurs ORDER BY role DESC, nom, prenom")
    utilisateurs = cursor.fetchall()

    # Récupérer les informations de la société
    cursor.execute("SELECT * FROM societe LIMIT 1")
    societe = cursor.fetchone()

    conn.close()

    # Ajouter la date actuelle
    from datetime import datetime
    date_now = datetime.now().strftime('%d/%m/%Y %H:%M')

    return render_template('rapport_utilisateurs.html',
                         utilisateurs=utilisateurs,
                         societe=societe,
                         date_now=date_now)

if __name__ == '__main__':
    # Démarrer le système de sauvegarde automatique
    try:
        from backup_scheduler import BackupScheduler
        backup_scheduler = BackupScheduler()
        backup_scheduler.run_in_background()
        print("✅ Système de sauvegarde automatique démarré")
    except Exception as e:
        print(f"⚠️ Erreur lors du démarrage du système de sauvegarde: {e}")

    # Configuration réseau pour accès multi-utilisateurs
    import socket

    # Obtenir l'adresse IP locale
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
    except:
        local_ip = "*************"

    print("🌐 Système de Gestion de Maintenance")
    print("=" * 50)
    print(f"🏠 Accès local: http://127.0.0.1:5000")
    print(f"🌍 Accès réseau: http://{local_ip}:5000")
    print("👥 Mode multi-utilisateurs activé")
    print("=" * 50)

    app.run(debug=False, host='0.0.0.0', port=5000, threaded=True)


# Routes للاستيراد من Excel لكل نموذج
@app.route('/api/import/marches', methods=['POST'])
@login_required
def api_import_marches():
    """استيراد الأسواق من Excel"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'Aucun fichier fourni'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'Aucun fichier sélectionné'}), 400

        if not file.filename.endswith(('.xlsx', '.xls')):
            return jsonify({'error': 'Le fichier doit être au format Excel'}), 400

        import pandas as pd


        df = pd.read_excel(file)

        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        success_count = 0
        error_count = 0

        for index, row in df.iterrows():
            try:
                cursor.execute("""
                    INSERT INTO marches (numero, domaine, date, objet, client, montant, delai_execution,
                                       periode_interventions, lieu, date_ordre_service, caution_definitif,
                                       mode_paiement, gps)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row.get('numero', ''),
                    row.get('domaine', ''),
                    row.get('date', ''),
                    row.get('objet', ''),
                    row.get('client', ''),
                    row.get('montant', 0),
                    row.get('delai_execution', ''),
                    row.get('periode_interventions', ''),
                    row.get('lieu', ''),
                    row.get('date_ordre_service', ''),
                    row.get('caution_definitif', ''),
                    row.get('mode_paiement', ''),
                    row.get('gps', '')
                ))
                success_count += 1
            except Exception as e:
                error_count += 1
                print(f"خطأ في السطر {index}: {e}")

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'{success_count} enregistrements importés avec succès. {error_count} erreurs.'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/import/interventions', methods=['POST'])
@login_required
def api_import_interventions():
    """استيراد التدخلات من Excel"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'Aucun fichier fourni'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'Aucun fichier sélectionné'}), 400

        if not file.filename.endswith(('.xlsx', '.xls')):
            return jsonify({'error': 'Le fichier doit être au format Excel'}), 400

        import pandas as pd

        # قراءة ملف Excel
        df = pd.read_excel(file)

        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        success_count = 0
        error_count = 0

        for index, row in df.iterrows():
            try:
                cursor.execute("""
                    INSERT INTO interventions (client, numero_marche, objet_marche, delai_execution,
                                             domaine, periode_interventions, semestre, periode_marche, lieu)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row.get('client', ''),
                    row.get('numero_marche', ''),
                    row.get('objet_marche', ''),
                    row.get('delai_execution', ''),
                    row.get('domaine', ''),
                    row.get('periode_interventions', ''),
                    row.get('semestre', ''),
                    row.get('periode_marche', ''),
                    row.get('lieu', '')
                ))
                success_count += 1
            except Exception as e:
                error_count += 1
                print(f"خطأ في السطر {index}: {e}")

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'{success_count} enregistrements importés avec succès. {error_count} erreurs.'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/import/reclamations', methods=['POST'])
@login_required
def api_import_reclamations():
    """استيراد الشكاوى من Excel"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'Aucun fichier fourni'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'Aucun fichier sélectionné'}), 400

        if not file.filename.endswith(('.xlsx', '.xls')):
            return jsonify({'error': 'Le fichier doit être au format Excel'}), 400

        import pandas as pd

        # قراءة ملف Excel
        df = pd.read_excel(file)

        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        success_count = 0
        error_count = 0

        for index, row in df.iterrows():
            try:
                cursor.execute("""
                    INSERT INTO reclamations (client, numero_marche, objet_marche, delai_execution,
                                            domaine, periode_interventions, periode_marche, lieu)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, (
                    row.get('client', ''),
                    row.get('numero_marche', ''),
                    row.get('objet_marche', ''),
                    row.get('delai_execution', ''),
                    row.get('domaine', ''),
                    row.get('periode_interventions', ''),
                    row.get('periode_marche', ''),
                    row.get('lieu', '')
                ))
                success_count += 1
            except Exception as e:
                error_count += 1
                print(f"خطأ في السطر {index}: {e}")

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'{success_count} enregistrements importés avec succès. {error_count} erreurs.'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/import/regions', methods=['POST'])
@login_required
def api_import_regions():
    """استيراد المناطق من Excel"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'Aucun fichier fourni'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'Aucun fichier sélectionné'}), 400

        if not file.filename.endswith(('.xlsx', '.xls')):
            return jsonify({'error': 'Le fichier doit être au format Excel'}), 400

        import pandas as pd

        # قراءة ملف Excel
        df = pd.read_excel(file)

        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        success_count = 0
        error_count = 0

        for index, row in df.iterrows():
            try:
                cursor.execute("""
                    INSERT INTO regions (nom_region, nom, description)
                    VALUES (?, ?, ?)
                """, (
                    row.get('nom_region', ''),
                    row.get('nom_region', ''),  # نسخ نفس القيمة
                    row.get('description', '')
                ))
                success_count += 1
            except Exception as e:
                error_count += 1
                print(f"خطأ في السطر {index}: {e}")

        conn.commit()
        conn.close()

        return jsonify({
            'success': True,
            'message': f'{success_count} enregistrements importés avec succès. {error_count} erreurs.'
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/import/sites', methods=['POST'])
def api_import_sites():
    """Import sites from Excel file or JSON data"""
    try:
        print(f"Request method: {request.method}")
        print(f"Content-Type: {request.content_type}")
        print(f"Is JSON: {request.is_json}")
        print(f"Files in request: {list(request.files.keys())}")

        # Check if it's a file upload or JSON data
        if 'file' in request.files:
            # File upload
            file = request.files['file']
            print(f"File received: {file.filename}")

            if file.filename == '':
                return jsonify({'error': 'Aucun fichier sélectionné'}), 400

            if not file.filename.endswith(('.xlsx', '.xls')):
                return jsonify({'error': 'Le fichier doit être au format Excel'}), 400

            try:
                import pandas as pd
                import openpyxl  # Required for Excel files
                df = pd.read_excel(file, engine='openpyxl')
                import_data = df.to_dict('records')
                print(f"Excel file read successfully: {len(import_data)} records")
            except ImportError as e:
                error_msg = f'Module manquant: {str(e)}. Installez pandas et openpyxl.'
                print(f"Import error: {error_msg}")
                return jsonify({'error': error_msg}), 500
            except Exception as e:
                error_msg = f'Erreur lecture Excel: {str(e)}'
                print(f"Excel read error: {error_msg}")
                return jsonify({'error': error_msg}), 500
        elif request.is_json:
            # JSON data from frontend
            data = request.get_json()
            print(f"JSON data received: {len(data.get('data', [])) if data and 'data' in data else 0} records")

            if not data or 'data' not in data:
                return jsonify({'error': 'Aucune donnée fournie'}), 400
            import_data = data['data']
        else:
            error_msg = f'Type de requête non supporté. Content-Type: {request.content_type}'
            print(f"Unsupported request: {error_msg}")
            return jsonify({'error': 'Aucun fichier ou données fournis'}), 400

        if not isinstance(import_data, list) or len(import_data) == 0:
            return jsonify({'error': 'Les données doivent être une liste non vide'}), 400

        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        success_count = 0
        error_count = 0

        for index, row in enumerate(import_data):
            try:

                nom_site = (row.get('NOM DU SITE') or row.get('nom_site') or
                           row.get('Nom du site') or row.get('Site') or '')
                adresse = (row.get('ADRESSE') or row.get('adresse') or
                          row.get('Adresse') or row.get('Address') or '')
                telephone = (row.get('TÉLÉPHONE') or row.get('telephone') or
                           row.get('Téléphone') or row.get('Phone') or '')
                responsable = (row.get('RESPONSABLE') or row.get('responsable') or
                             row.get('Responsable') or row.get('Manager') or '')
                email = (row.get('EMAIL') or row.get('email') or
                        row.get('Email') or row.get('E-mail') or '')

                cursor.execute("""
                    INSERT INTO sites (nom_site, nom, region_id, adresse, coordonnees,
                                     telephone, responsable, email, gps)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (nom_site, nom_site, 1, adresse, '', telephone, responsable, email, ''))
                success_count += 1
            except Exception as e:
                error_count += 1
                print(f"Erreur ligne {index}: {e}")

        conn.commit()
        conn.close()

        log_user_activity("Import", "Sites", f"Importé {success_count} sites depuis Excel")

        return jsonify({
            'success': True,
            'message': f'{success_count} enregistrements importés avec succès. {error_count} erreurs.'
        })

    except Exception as e:
        print(f"Erreur import sites: {e}")
        return jsonify({'error': str(e)}), 500

# Routes de suppression simples pour tous les modèles
@app.route('/delete-marche/<string:numero>')
@login_required
def delete_marche_simple(numero):
    try:
        print(f"🗑️ DELETE MARCHE: {numero}")

        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        cursor.execute("DELETE FROM marches WHERE numero = ?", (numero,))
        rows_deleted = cursor.rowcount

        if rows_deleted > 0:
            conn.commit()
            flash(f'Marché {numero} supprimé avec succès', 'success')
            print(f"✅ Marché {numero} supprimé")
        else:
            flash(f'Marché {numero} non trouvé', 'danger')
            print(f"❌ Marché {numero} non trouvé")

        conn.close()
        return redirect(url_for('marches'))

    except Exception as e:
        print(f"❌ Erreur: {e}")
        flash(f'Erreur: {str(e)}', 'danger')
        return redirect(url_for('marches'))

@app.route('/delete-utilisateur/<int:id>')
@login_required
@admin_required
def delete_utilisateur_simple(id):
    try:
        print(f"🗑️ DELETE UTILISATEUR: {id}")

        if session.get('utilisateur_id') == id:
            flash('Vous ne pouvez pas supprimer votre propre compte', 'danger')
            return redirect(url_for('utilisateurs'))

        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        cursor.execute("DELETE FROM utilisateurs WHERE id = ?", (id,))
        rows_deleted = cursor.rowcount

        if rows_deleted > 0:
            conn.commit()
            flash(f'Utilisateur supprimé avec succès', 'success')
            print(f"✅ Utilisateur {id} supprimé")
        else:
            flash(f'Utilisateur non trouvé', 'danger')
            print(f"❌ Utilisateur {id} non trouvé")

        conn.close()
        return redirect(url_for('utilisateurs'))

    except Exception as e:
        print(f"❌ Erreur: {e}")
        flash(f'Erreur: {str(e)}', 'danger')
        return redirect(url_for('utilisateurs'))

@app.route('/delete-intervention/<int:id>')
@login_required
def delete_intervention_simple(id):
    try:
        print(f"🗑️ DELETE INTERVENTION: {id}")

        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        cursor.execute("DELETE FROM interventions WHERE id = ?", (id,))
        rows_deleted = cursor.rowcount

        if rows_deleted > 0:
            conn.commit()
            flash(f'Intervention supprimée avec succès', 'success')
            print(f"✅ Intervention {id} supprimée")
        else:
            flash(f'Intervention non trouvée', 'danger')
            print(f"❌ Intervention {id} non trouvée")

        conn.close()
        return redirect(url_for('interventions'))

    except Exception as e:
        print(f"❌ Erreur: {e}")
        flash(f'Erreur: {str(e)}', 'danger')
        return redirect(url_for('interventions'))

@app.route('/delete-reclamation/<int:id>')
@login_required
def delete_reclamation_simple(id):
    try:
        print(f"🗑️ DELETE RECLAMATION: {id}")

        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        cursor.execute("DELETE FROM reclamations WHERE id = ?", (id,))
        rows_deleted = cursor.rowcount

        if rows_deleted > 0:
            conn.commit()
            flash(f'Réclamation supprimée avec succès', 'success')
            print(f"✅ Réclamation {id} supprimée")
        else:
            flash(f'Réclamation non trouvée', 'danger')
            print(f"❌ Réclamation {id} non trouvée")

        conn.close()
        return redirect(url_for('reclamations'))

    except Exception as e:
        print(f"❌ Erreur: {e}")
        flash(f'Erreur: {str(e)}', 'danger')
        return redirect(url_for('reclamations'))

@app.route('/delete-region/<int:id>')
@login_required
def delete_region_simple(id):
    try:
        print(f"🗑️ DELETE REGION: {id}")

        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        cursor.execute("DELETE FROM regions WHERE id = ?", (id,))
        rows_deleted = cursor.rowcount

        if rows_deleted > 0:
            conn.commit()
            flash(f'Région supprimée avec succès', 'success')
            print(f"✅ Région {id} supprimée")
        else:
            flash(f'Région non trouvée', 'danger')
            print(f"❌ Région {id} non trouvée")

        conn.close()
        return redirect(url_for('regions'))

    except Exception as e:
        print(f"❌ Erreur: {e}")
        flash(f'Erreur: {str(e)}', 'danger')
        return redirect(url_for('regions'))

@app.route('/delete-site/<int:id>')
@login_required
def delete_site_simple(id):
    try:
        print(f"🗑️ DELETE SITE: {id}")

        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()

        cursor.execute("DELETE FROM sites WHERE id = ?", (id,))
        rows_deleted = cursor.rowcount

        if rows_deleted > 0:
            conn.commit()
            flash(f'Site supprimé avec succès', 'success')
            print(f"✅ Site {id} supprimé")
        else:
            flash(f'Site non trouvé', 'danger')
            print(f"❌ Site {id} non trouvé")

        conn.close()
        return redirect(url_for('sites'))

    except Exception as e:
        print(f"❌ Erreur: {e}")
        flash(f'Erreur: {str(e)}', 'danger')
        return redirect(url_for('sites'))

# API للتحقق من تسجيل الدخول
@app.route('/api/check-login')
def api_check_login():
    """التحقق من حالة تسجيل الدخول"""
    if 'utilisateur_id' in session:
        return jsonify({'logged_in': True, 'user': session.get('nom_utilisateur')}), 200
    else:
        return jsonify({'logged_in': False}), 401

# معالجات الأخطاء للـ API
@app.errorhandler(401)
def unauthorized_error(error):
    """معالج خطأ عدم التصريح"""
    if request.path.startswith('/api/'):
        return jsonify({'error': 'Non autorisé - veuillez vous connecter'}), 401
    return redirect(url_for('login'))

@app.errorhandler(403)
def forbidden_error(error):
    """معالج خطأ الوصول المحظور"""
    if request.path.startswith('/api/'):
        return jsonify({'error': 'Accès refusé - permissions insuffisantes'}), 403
    return render_template('error.html', error='Accès refusé'), 403

@app.errorhandler(404)
def not_found_error(error):
    """معالج خطأ عدم وجود الصفحة"""
    if request.path.startswith('/api/'):
        return jsonify({'error': 'Route non trouvé'}), 404
    return render_template('error.html', error='Page non trouvée'), 404

@app.errorhandler(500)
def internal_error(error):
    """معالج خطأ الخادم الداخلي"""
    if request.path.startswith('/api/'):
        return jsonify({'error': 'Erreur serveur interne'}), 500
    return render_template('error.html', error='Erreur serveur'), 500

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)