{% extends "rapport_base.html" %}

{% block title %}Rapport de Marché N° {{ marche.numero }} - {{ company_info.nom if company_info and company_info.nom else 'Système de Gestion de Maintenance' }}{% endblock %}

{% block rapport_title %}Rapport de Marché{% endblock %}

{% block rapport_subtitle %}Marché N° {{ marche.numero }} - {{ marche.domaine }}{% endblock %}

{% block rapport_meta %}
<div class="rapport-meta">
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-hashtag me-2"></i>N° de Marché:</span>
        <span class="meta-value">{{ marche.numero }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-user me-2"></i>Client:</span>
        <span class="meta-value">{{ marche.client }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-cogs me-2"></i>Domaine:</span>
        <span class="meta-value">
            {% if marche.domaine == 'SVS' %}
                <span class="badge-custom badge-info">SVS</span>
            {% elif marche.domaine == 'EXTINCTEUR' %}
                <span class="badge-custom badge-danger">EXTINCTEUR</span>
            {% elif marche.domaine == 'SYSTEME D\'INCENDIE' %}
                <span class="badge-custom badge-warning">SYSTÈME D'INCENDIE</span>
            {% elif marche.domaine == 'SYSTEME D\'ALARME' %}
                <span class="badge-custom badge-info">SYSTÈME D'ALARME</span>
            {% elif marche.domaine == 'SYSTEME TELEPHONIQUE' %}
                <span class="badge-custom badge-success">SYSTÈME TÉLÉPHONIQUE</span>
            {% else %}
                <span class="badge-custom badge-secondary">{{ marche.domaine }}</span>
            {% endif %}
        </span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-calendar me-2"></i>Date du Marché:</span>
        <span class="meta-value">{{ marche.date }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-euro-sign me-2"></i>Montant:</span>
        <span class="meta-value">{{ "{:,.2f}".format(marche.montant) }} DH</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-map-marker-alt me-2"></i>Lieu:</span>
        <span class="meta-value">{{ marche.lieu }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-clock me-2"></i>Délai d'Exécution:</span>
        <span class="meta-value">{{ marche.delai_execution }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-calendar-alt me-2"></i>Période d'Interventions:</span>
        <span class="meta-value">{{ marche.periode_interventions }}</span>
    </div>
</div>
{% endblock %}

{% block rapport_content %}
<!-- Informations détaillées du marché -->
<div class="section-title">
    <i class="fas fa-file-contract me-2"></i>Informations Détaillées du Marché
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <table class="table table-custom">
            <tbody>
                <tr>
                    <td class="fw-bold" style="width: 25%;">Objet du marché</td>
                    <td colspan="3">{{ marche.objet }}</td>
                </tr>
                <tr>
                    <td class="fw-bold">Date d'ordre de service</td>
                    <td>{{ marche.date_ordre_service or '-' }}</td>
                    <td class="fw-bold">Caution définitive</td>
                    <td>{{ marche.caution_definitif or '-' }}</td>
                </tr>
                <tr>
                    <td class="fw-bold">Mode de paiement</td>
                    <td colspan="3">{{ marche.mode_paiement or '-' }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Interventions liées au marché -->
{% if interventions %}
<div class="section-title">
    <i class="fas fa-tools me-2"></i>Interventions Liées ({{ interventions|length }})
</div>

<div class="table-responsive">
    <table class="table table-custom">
        <thead>
            <tr>
                <th>ID</th>
                <th>Domaine</th>
                <th>Client</th>
                <th>Lieu</th>
                <th>Période</th>
                <th>Date de Création</th>
                <th>Statut</th>
            </tr>
        </thead>
        <tbody>
            {% for intervention in interventions %}
            <tr>
                <td>{{ intervention.id }}</td>
                <td>
                    {% if intervention.domaine == 'SVS' %}
                        <span class="badge-custom badge-info">SVS</span>
                    {% elif intervention.domaine == 'EXTINCTEUR' %}
                        <span class="badge-custom badge-danger">EXTINCTEUR</span>
                    {% elif intervention.domaine == 'SYSTEME D\'INCENDIE' %}
                        <span class="badge-custom badge-warning">SYS. INCENDIE</span>
                    {% elif intervention.domaine == 'SYSTEME D\'ALARME' %}
                        <span class="badge-custom badge-info">SYS. ALARME</span>
                    {% elif intervention.domaine == 'SYSTEME TELEPHONIQUE' %}
                        <span class="badge-custom badge-success">SYS. TÉLÉPHONIQUE</span>
                    {% else %}
                        <span class="badge-custom badge-secondary">{{ intervention.domaine }}</span>
                    {% endif %}
                </td>
                <td>{{ intervention.client }}</td>
                <td>{{ intervention.lieu }}</td>
                <td>{{ intervention.periode_interventions }}</td>
                <td>{{ intervention.date_creation }}</td>
                <td><span class="badge-custom badge-success">Actif</span></td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- Réclamations liées au marché -->
{% if reclamations %}
<div class="section-title">
    <i class="fas fa-exclamation-circle me-2"></i>Réclamations Liées ({{ reclamations|length }})
</div>

<div class="table-responsive">
    <table class="table table-custom">
        <thead>
            <tr>
                <th>ID</th>
                <th>Domaine</th>
                <th>Client</th>
                <th>Lieu</th>
                <th>Date de Création</th>
                <th>Statut</th>
            </tr>
        </thead>
        <tbody>
            {% for reclamation in reclamations %}
            <tr>
                <td>{{ reclamation.id }}</td>
                <td>
                    {% if reclamation.domaine == 'SVS' %}
                        <span class="badge-custom badge-info">SVS</span>
                    {% elif reclamation.domaine == 'EXTINCTEUR' %}
                        <span class="badge-custom badge-danger">EXTINCTEUR</span>
                    {% elif reclamation.domaine == 'SYSTEME D\'INCENDIE' %}
                        <span class="badge-custom badge-warning">SYS. INCENDIE</span>
                    {% elif reclamation.domaine == 'SYSTEME D\'ALARME' %}
                        <span class="badge-custom badge-info">SYS. ALARME</span>
                    {% elif reclamation.domaine == 'SYSTEME TELEPHONIQUE' %}
                        <span class="badge-custom badge-success">SYS. TÉLÉPHONIQUE</span>
                    {% else %}
                        <span class="badge-custom badge-secondary">{{ reclamation.domaine }}</span>
                    {% endif %}
                </td>
                <td>{{ reclamation.client }}</td>
                <td>{{ reclamation.lieu }}</td>
                <td>{{ reclamation.date_creation }}</td>
                <td><span class="badge-custom badge-warning">En cours</span></td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- Analyse financière -->
<div class="section-title">
    <i class="fas fa-chart-line me-2"></i>Analyse Financière
</div>

<div class="row">
    <div class="col-md-4">
        <div class="card border-0 bg-light">
            <div class="card-body text-center">
                <h5 class="text-primary">{{ "{:,.2f}".format(marche.montant) }} DH</h5>
                <p class="text-muted mb-0">Montant Total</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-0 bg-light">
            <div class="card-body text-center">
                <h5 class="text-success">{{ interventions|length if interventions else 0 }}</h5>
                <p class="text-muted mb-0">Interventions</p>
            </div>
        </div>
    </div>
    <div class="col-md-4">
        <div class="card border-0 bg-light">
            <div class="card-body text-center">
                <h5 class="text-warning">{{ reclamations|length if reclamations else 0 }}</h5>
                <p class="text-muted mb-0">Réclamations</p>
            </div>
        </div>
    </div>
</div>

<!-- Suivi et performance -->
<div class="section-title">
    <i class="fas fa-tachometer-alt me-2"></i>Suivi et Performance
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card border-0 bg-light">
            <div class="card-body">
                <h6 class="card-title text-primary">
                    <i class="fas fa-calendar-check me-2"></i>Échéances
                </h6>
                <ul class="list-unstyled mb-0">
                    <li><strong>Délai d'exécution:</strong> {{ marche.delai_execution }}</li>
                    <li><strong>Période d'interventions:</strong> {{ marche.periode_interventions }}</li>
                    {% if marche.date_ordre_service %}
                    <li><strong>Date d'ordre de service:</strong> {{ marche.date_ordre_service }}</li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card border-0 bg-light">
            <div class="card-body">
                <h6 class="card-title text-success">
                    <i class="fas fa-check-circle me-2"></i>État d'Avancement
                </h6>
                <ul class="list-unstyled mb-0">
                    <li><strong>Statut:</strong> <span class="badge-custom badge-success">Actif</span></li>
                    <li><strong>Progression:</strong> En cours</li>
                    <li><strong>Prochaine échéance:</strong> À définir</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Notes et observations -->
<div class="mt-4 p-3 bg-light rounded">
    <h6 class="text-muted mb-2">
        <i class="fas fa-sticky-note me-2"></i>Notes et Observations
    </h6>
    <p class="mb-0 text-muted">
        Ce marché est en cours d'exécution selon les termes et conditions établis.
        Le suivi régulier des interventions et la gestion proactive des réclamations
        garantissent la qualité du service fourni au client.
    </p>
</div>
{% endblock %}
