#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص نهائي صحيح للتكرارات
"""

import os

def correct_final_check():
    """فحص نهائي صحيح"""
    
    print("🔍 فحص نهائي صحيح للتكرارات")
    print("=" * 50)
    
    templates_info = {
        'templates/ajouter_marche.html': {
            'description': 'نموذج إضافة مارشيه',
            'expected_options': 1,
            'expected_badges': 0
        },
        'templates/modifier_marche.html': {
            'description': 'نموذج تعديل مارشيه',
            'expected_options': 1,
            'expected_badges': 0
        },
        'templates/marches.html': {
            'description': 'صفحة عرض المارشيه + فلتر',
            'expected_options': 1,
            'expected_badges': 1
        },
        'templates/details_marche.html': {
            'description': 'صفحة تفاصيل المارشيه',
            'expected_options': 0,
            'expected_badges': 3  # في 3 أقسام
        },
        'templates/ajouter_intervention.html': {
            'description': 'نموذج إضافة إنترفنشن',
            'expected_options': 1,
            'expected_badges': 0
        },
        'templates/interventions.html': {
            'description': 'صفحة عرض الإنترفنشن + فلتر',
            'expected_options': 1,
            'expected_badges': 1
        },
        'templates/ajouter_reclamation.html': {
            'description': 'نموذج إضافة ريكلاماسيون',
            'expected_options': 1,
            'expected_badges': 0
        },
        'templates/reclamations.html': {
            'description': 'صفحة عرض الريكلاماسيون + فلتر',
            'expected_options': 1,
            'expected_badges': 1
        },
        'templates/voir_reclamation.html': {
            'description': 'صفحة عرض ريكلاماسيون واحدة',
            'expected_options': 0,
            'expected_badges': 1
        }
    }
    
    total_clean = 0
    total_files = len(templates_info)
    
    for template_path, info in templates_info.items():
        print(f"\n📄 {info['description']}:")
        print(f"   📁 {template_path}")
        
        if not os.path.exists(template_path):
            print(f"   ❌ الملف غير موجود")
            continue
        
        with open(template_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # عد خيارات select
        option_count = 0
        badge_count = 0
        
        for i, line in enumerate(lines, 1):
            if 'AFFICHAGE DYNAMIQUE' in line:
                if '<option' in line and 'value=' in line:
                    option_count += 1
                    print(f"      خيار select في السطر {i}")
                elif 'badge' in line:
                    badge_count += 1
                    print(f"      badge في السطر {i}")
                else:
                    print(f"      مرجع آخر في السطر {i}: {line.strip()[:50]}...")
        
        # التحقق من التطابق مع المتوقع
        options_ok = option_count == info['expected_options']
        badges_ok = badge_count == info['expected_badges']
        
        print(f"   📊 خيارات select: {option_count}/{info['expected_options']} {'✅' if options_ok else '❌'}")
        print(f"   🎨 badges: {badge_count}/{info['expected_badges']} {'✅' if badges_ok else '❌'}")
        
        if options_ok and badges_ok:
            print(f"   🎉 مثالي!")
            total_clean += 1
        else:
            print(f"   ⚠️ يحتاج مراجعة")
    
    # النتيجة النهائية
    print(f"\n📊 النتيجة النهائية:")
    print(f"   📁 إجمالي الملفات: {total_files}")
    print(f"   ✅ ملفات مثالية: {total_clean}")
    print(f"   ❌ ملفات تحتاج مراجعة: {total_files - total_clean}")
    print(f"   📈 نسبة النجاح: {(total_clean/total_files)*100:.1f}%")
    
    if total_clean == total_files:
        print(f"\n🎉 ممتاز! جميع الملفات مثالية!")
        print(f"✅ L'AFFICHAGE DYNAMIQUE متاح في جميع القوائم بدون تكرار")
        print(f"🚀 النظام جاهز للاستخدام")
        
        print(f"\n💡 كيفية الاستخدام:")
        print(f"   1. 📝 في نماذج الإضافة: اختر 'L'AFFICHAGE DYNAMIQUE' من قائمة Domaine")
        print(f"   2. 🔍 في صفحات العرض: استخدم الفلتر للبحث")
        print(f"   3. 🎨 في العرض: سيظهر badge داكن اللون")
        print(f"   4. 🛠️ في الإنترفنشن: ستجد جدول خاص للتفاصيل")
        
        return True
    else:
        print(f"\n⚠️ بعض الملفات تحتاج مراجعة")
        return False

if __name__ == "__main__":
    success = correct_final_check()
    
    print(f"\n🎯 الخلاصة:")
    print("=" * 50)
    
    if success:
        print(f"✅ تم إضافة L'AFFICHAGE DYNAMIQUE بنجاح!")
        print(f"🎉 لا توجد تكرارات")
        print(f"🚀 جاهز للاستخدام")
    else:
        print(f"⚠️ يرجى مراجعة الملفات المذكورة أعلاه")
        print(f"🔧 قد تحتاج إصلاحات إضافية")
