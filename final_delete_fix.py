#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إصلاح نهائي شامل لجميع أزرار الحذف
Final comprehensive fix for all delete buttons
"""

import sqlite3
import os
import shutil
from datetime import datetime

def backup_database():
    """عمل نسخة احتياطية من قاعدة البيانات"""
    if os.path.exists('maintenance.db'):
        backup_name = f'maintenance_final_delete_fix_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
        shutil.copy2('maintenance.db', backup_name)
        print(f"✅ تم حفظ نسخة احتياطية: {backup_name}")
        return backup_name
    return None

def fix_all_table_columns():
    """إصلاح جميع أعمدة الجداول"""
    
    conn = sqlite3.connect('maintenance.db')
    cursor = conn.cursor()
    
    print("🔧 إصلاح جميع أعمدة الجداول...")
    
    try:
        # إصلاح جدول marches
        print("\n📋 إصلاح جدول marches:")
        cursor.execute("PRAGMA table_info(marches)")
        marches_columns = [column[1] for column in cursor.fetchall()]
        print(f"   الأعمدة الموجودة: {marches_columns}")
        
        missing_marches_columns = {
            'numero': 'TEXT',
            'objet': 'TEXT', 
            'client': 'TEXT',
            'date': 'DATE',
            'domaine': 'TEXT',
            'lieu': 'TEXT',
            'periode_interventions': 'TEXT',
            'montant': 'REAL'
        }
        
        for col, col_type in missing_marches_columns.items():
            if col not in marches_columns:
                cursor.execute(f"ALTER TABLE marches ADD COLUMN {col} {col_type}")
                print(f"   ✅ تم إضافة عمود {col}")
        
        # نسخ البيانات
        if 'numero_marche' in marches_columns:
            cursor.execute("UPDATE marches SET numero = numero_marche WHERE numero IS NULL")
        if 'objet_marche' in marches_columns:
            cursor.execute("UPDATE marches SET objet = objet_marche WHERE objet IS NULL")
        
        # إصلاح جدول interventions
        print("\n🔧 إصلاح جدول interventions:")
        cursor.execute("PRAGMA table_info(interventions)")
        interventions_columns = [column[1] for column in cursor.fetchall()]
        print(f"   الأعمدة الموجودة: {interventions_columns}")
        
        missing_interventions_columns = {
            'domaine': 'TEXT',
            'client': 'TEXT',
            'date_creation': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'lieu': 'TEXT',
            'date_intervention': 'DATE',
            'periode_interventions': 'TEXT',
            'periode_marche': 'TEXT'
        }
        
        for col, col_type in missing_interventions_columns.items():
            if col not in interventions_columns:
                cursor.execute(f"ALTER TABLE interventions ADD COLUMN {col} {col_type}")
                print(f"   ✅ تم إضافة عمود {col}")
        
        # نسخ البيانات
        if 'type_intervention' in interventions_columns:
            cursor.execute("UPDATE interventions SET domaine = type_intervention WHERE domaine IS NULL")
        
        # إصلاح جدول reclamations
        print("\n📞 إصلاح جدول reclamations:")
        cursor.execute("PRAGMA table_info(reclamations)")
        reclamations_columns = [column[1] for column in cursor.fetchall()]
        print(f"   الأعمدة الموجودة: {reclamations_columns}")
        
        missing_reclamations_columns = {
            'domaine': 'TEXT',
            'client': 'TEXT', 
            'date_creation': 'TIMESTAMP DEFAULT CURRENT_TIMESTAMP',
            'lieu': 'TEXT',
            'periode_interventions': 'TEXT',
            'periode_marche': 'TEXT'
        }
        
        for col, col_type in missing_reclamations_columns.items():
            if col not in reclamations_columns:
                cursor.execute(f"ALTER TABLE reclamations ADD COLUMN {col} {col_type}")
                print(f"   ✅ تم إضافة عمود {col}")
        
        # نسخ البيانات
        if 'type_reclamation' in reclamations_columns:
            cursor.execute("UPDATE reclamations SET domaine = type_reclamation WHERE domaine IS NULL")
        
        conn.commit()
        print("✅ تم إصلاح جميع أعمدة الجداول")
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الأعمدة: {e}")
        conn.rollback()
        return False
    
    finally:
        conn.close()
    
    return True

def test_all_delete_operations():
    """اختبار جميع عمليات الحذف"""
    
    print("\n🧪 اختبار جميع عمليات الحذف...")
    
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # اختبار المستخدمين
        print("\n👥 اختبار حذف المستخدمين:")
        try:
            cursor.execute("SELECT COUNT(*) FROM users WHERE role = 'admin'")
            admin_count = cursor.fetchone()[0]
            cursor.execute("SELECT id, username, role FROM users LIMIT 3")
            users = cursor.fetchall()
            print(f"   ✅ عدد المديرين: {admin_count}")
            print(f"   ✅ عدد المستخدمين: {len(users)}")
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
        
        # اختبار الأسواق
        print("\n📋 اختبار حذف الأسواق:")
        try:
            cursor.execute("SELECT numero, objet, client FROM marches LIMIT 3")
            marches = cursor.fetchall()
            print(f"   ✅ عدد الأسواق: {len(marches)}")
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
        
        # اختبار التدخلات
        print("\n🔧 اختبار حذف التدخلات:")
        try:
            cursor.execute("SELECT id, domaine, client FROM interventions LIMIT 3")
            interventions = cursor.fetchall()
            print(f"   ✅ عدد التدخلات: {len(interventions)}")
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
        
        # اختبار الشكاوى
        print("\n📞 اختبار حذف الشكاوى:")
        try:
            cursor.execute("SELECT id, domaine, client FROM reclamations LIMIT 3")
            reclamations = cursor.fetchall()
            print(f"   ✅ عدد الشكاوى: {len(reclamations)}")
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
        
        # اختبار المناطق
        print("\n🌍 اختبار حذف المناطق:")
        try:
            cursor.execute("SELECT id, nom_region FROM regions LIMIT 3")
            regions = cursor.fetchall()
            print(f"   ✅ عدد المناطق: {len(regions)}")
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
        
        # اختبار المواقع
        print("\n📍 اختبار حذف المواقع:")
        try:
            cursor.execute("SELECT id, nom_site FROM sites LIMIT 3")
            sites = cursor.fetchall()
            print(f"   ✅ عدد المواقع: {len(sites)}")
        except Exception as e:
            print(f"   ❌ خطأ: {e}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

def create_delete_button_templates():
    """إنشاء قوالب أزرار الحذف الصحيحة"""
    
    print("\n📝 قوالب أزرار الحذف الصحيحة:")
    
    templates = {
        "المستخدمين (users)": """
<!-- في templates/utilisateurs.html -->
<button type="button"
        class="btn btn-sm btn-danger"
        onclick="confirmDelete('{{ utilisateur.username }}', '{{ url_for('supprimer_utilisateur', id=utilisateur.id) }}')"
        title="Supprimer">
    <i class="fas fa-trash"></i>
</button>""",
        
        "الأسواق (marches)": """
<!-- في templates/marches.html -->
<button type="button"
        class="btn btn-sm btn-danger"
        onclick="confirmDelete('{{ marche.numero }}', '{{ url_for('supprimer_marche', numero=marche.numero) }}')"
        title="Supprimer">
    <i class="fas fa-trash"></i>
</button>""",
        
        "التدخلات (interventions)": """
<!-- في templates/interventions.html -->
<button type="button"
        class="btn btn-sm btn-danger"
        onclick="confirmDelete('Intervention #{{ intervention.id }}', '{{ url_for('supprimer_intervention', id=intervention.id) }}')"
        title="Supprimer">
    <i class="fas fa-trash"></i>
</button>""",
        
        "الشكاوى (reclamations)": """
<!-- في templates/reclamations.html -->
<button type="button"
        class="btn btn-sm btn-danger"
        onclick="confirmDelete('Réclamation #{{ reclamation.id }}', '{{ url_for('supprimer_reclamation', id=reclamation.id) }}')"
        title="Supprimer">
    <i class="fas fa-trash"></i>
</button>""",
        
        "المناطق (regions)": """
<!-- في templates/regions.html -->
<button type="button"
        class="btn btn-sm btn-danger"
        onclick="confirmDelete('{{ region.nom_region }}', '{{ url_for('supprimer_region', id=region.id) }}')"
        title="Supprimer">
    <i class="fas fa-trash"></i>
</button>""",
        
        "المواقع (sites)": """
<!-- في templates/sites.html -->
<button type="button"
        class="btn btn-sm btn-danger"
        onclick="confirmDelete('{{ site.nom_site }}', '{{ url_for('supprimer_site', id=site.id) }}')"
        title="Supprimer">
    <i class="fas fa-trash"></i>
</button>"""
    }
    
    for module, template in templates.items():
        print(f"\n🔹 {module}:")
        print(template)

def main():
    """الدالة الرئيسية"""
    
    print("🗑️ إصلاح نهائي شامل لجميع أزرار الحذف")
    print("🗑️ Final Comprehensive Delete Buttons Fix")
    print("=" * 70)
    
    # عمل نسخة احتياطية
    backup_file = backup_database()
    
    # إصلاح جميع أعمدة الجداول
    if fix_all_table_columns():
        print("✅ تم إصلاح جميع أعمدة الجداول")
    else:
        print("❌ فشل في إصلاح أعمدة الجداول")
        return
    
    # اختبار جميع عمليات الحذف
    if test_all_delete_operations():
        print("✅ جميع عمليات الحذف تعمل")
    else:
        print("❌ مشاكل في عمليات الحذف")
    
    # عرض قوالب أزرار الحذف الصحيحة
    create_delete_button_templates()
    
    print("\n🎉 تم الانتهاء من الإصلاح النهائي!")
    print("🎉 Final fix completed!")
    
    if backup_file:
        print(f"\n💾 النسخة الاحتياطية: {backup_file}")
        print(f"💾 Backup file: {backup_file}")
    
    print("\n📋 الخطوات التالية:")
    print("📋 Next steps:")
    print("   1. تشغيل البرنامج")
    print("   1. Run the application")
    print("   2. اختبار أزرار الحذف في كل صفحة")
    print("   2. Test delete buttons on each page")
    print("   3. يجب أن تعمل جميع أزرار الحذف الآن")
    print("   3. All delete buttons should work now")
    
    print("\n🔧 إذا لم تعمل أزرار الحذف:")
    print("🔧 If delete buttons don't work:")
    print("   1. تأكد من وجود ملف static/js/delete-modal.js")
    print("   1. Make sure static/js/delete-modal.js exists")
    print("   2. تأكد من تحميل الملف في base.html")
    print("   2. Make sure the file is loaded in base.html")
    print("   3. تأكد من استخدام confirmDelete() في الأزرار")
    print("   3. Make sure buttons use confirmDelete()")
    
    input("\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
