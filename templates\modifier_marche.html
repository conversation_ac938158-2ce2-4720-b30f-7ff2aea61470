{% extends "base.html" %}

{% block title %}Modifier un Marché - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Modifier un Marché{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-edit"></i> Modifier le marché N° {{ marche.numero }}
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('modifier_marche', numero=marche.numero) }}">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="numero" class="form-label">N° de marché</label>
                            <input type="text" class="form-control" id="numero" value="{{ marche.numero }}" disabled>
                            <small class="text-muted">Le numéro de marché ne peut pas être modifié</small>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="domaine" class="form-label">Domaine</label>
                            <select class="form-select" id="domaine" name="domaine" required>
                                <option value="">Sélectionner un domaine</option>
                                <option value="SVS" {% if marche.domaine == 'SVS' %}selected{% endif %}>SVS</option>
                                <option value="EXTINCTEUR" {% if marche.domaine == 'EXTINCTEUR' %}selected{% endif %}>EXTINCTEUR</option>
                                <option value="SYSTEME D'INCENDIE" {% if marche.domaine == 'SYSTEME D\'INCENDIE' %}selected{% endif %}>SYSTEME D'INCENDIE</option>
                                <option value="SYSTEME D'ALARME" {% if marche.domaine == 'SYSTEME D\'ALARME' %}selected{% endif %}>SYSTEME D'ALARME</option>
                                <option value="SYSTEME TELEPHONIQUE" {% if marche.domaine == 'SYSTEME TELEPHONIQUE' %}selected{% endif %}>SYSTEME TELEPHONIQUE</option>
                                <option value="L'AFFICHAGE DYNAMIQUE" {% if marche.domaine == "L'AFFICHAGE DYNAMIQUE" %}selected{% endif %}>L'AFFICHAGE DYNAMIQUE</option>
                            </select>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="date" class="form-label">Date</label>
                            <input type="date" class="form-control" id="date" name="date" value="{{ marche.date }}" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="objet" class="form-label">Objet de marché</label>
                            <input type="text" class="form-control" id="objet" name="objet" value="{{ marche.objet }}" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="client" class="form-label">Client</label>
                            <input type="text" class="form-control" id="client" name="client" value="{{ marche.client }}" required>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="montant" class="form-label">Montant de marché (DH)</label>
                            <input type="number" step="0.01" class="form-control" id="montant" name="montant" value="{{ marche.montant }}" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="delai_execution" class="form-label">Délai d'exécution</label>
                            <input type="text" class="form-control" id="delai_execution" name="delai_execution" value="{{ marche.delai_execution }}" placeholder="Ex: 12 mois">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="periode_interventions" class="form-label">Période d'interventions</label>
                            <select class="form-select" id="periode_interventions" name="periode_interventions">
                                <option value="">Sélectionner une période</option>
                                <option value="annuel" {% if marche.periode_interventions == 'annuel' %}selected{% endif %}>Annuel</option>
                                <option value="semestriel" {% if marche.periode_interventions == 'semestriel' %}selected{% endif %}>Semestriel</option>
                                <option value="trimestriel" {% if marche.periode_interventions == 'trimestriel' %}selected{% endif %}>Trimestriel</option>
                                <option value="mensuel" {% if marche.periode_interventions == 'mensuel' %}selected{% endif %}>Mensuel</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="lieu" class="form-label">Lieu</label>
                            <input type="text" class="form-control" id="lieu" name="lieu" value="{{ marche.lieu }}">
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="date_ordre_service" class="form-label">Date d'ordre de service</label>
                            <input type="date" class="form-control" id="date_ordre_service" name="date_ordre_service" value="{{ marche.date_ordre_service }}">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="caution_definitif" class="form-label">Caution définitif</label>
                            <input type="text" class="form-control" id="caution_definitif" name="caution_definitif" value="{{ marche.caution_definitif }}">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="mode_paiement" class="form-label">Mode de paiement</label>
                            <select class="form-select" id="mode_paiement" name="mode_paiement">
                                <option value="">Sélectionner un mode</option>
                                <option value="Chèque" {% if marche.mode_paiement == 'Chèque' %}selected{% endif %}>Chèque</option>
                                <option value="Espèce" {% if marche.mode_paiement == 'Espèce' %}selected{% endif %}>Espèce</option>
                                <option value="Ordre de Virement" {% if marche.mode_paiement == 'Ordre de Virement' %}selected{% endif %}>Ordre de Virement</option>
                            </select>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer les modifications
                        </button>
                        <a href="{{ url_for('marches') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
