/* ==================== MAP STYLES ====================*/

/* Map Container */
.map-container {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    background: #f8f9fa;
}

#map {
    height: 600px;
    width: 100%;
    border-radius: 15px;
    z-index: 1;
}

/* Loading Spinner */
.loading-spinner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Error Display */
.error-display {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    text-align: center;
    max-width: 400px;
}

/* Custom Marker Styles */
.custom-marker-icon {
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
}

.custom-marker-wrapper {
    background: transparent !important;
    border: none !important;
}

/* Marker Animation */
@keyframes markerPulse {
    0% {
        transform: rotate(-45deg) scale(1);
        box-shadow: 0 4px 15px rgba(0,0,0,0.4);
    }
    50% {
        transform: rotate(-45deg) scale(1.1);
        box-shadow: 0 6px 20px rgba(0,0,0,0.6);
    }
    100% {
        transform: rotate(-45deg) scale(1);
        box-shadow: 0 4px 15px rgba(0,0,0,0.4);
    }
}

.marker-pulse {
    animation: markerPulse 2s infinite;
}

/* Popup Styles */
.leaflet-popup-content-wrapper {
    border-radius: 12px !important;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
    border: 1px solid #e9ecef !important;
}

.leaflet-popup-content {
    margin: 0 !important;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
}

.marker-popup {
    min-width: 250px;
    max-width: 350px;
}

.marker-popup h6 {
    margin: 0 0 15px 0;
    padding: 15px 20px 10px 20px;
    font-weight: 600;
    font-size: 1.1rem;
    border-bottom: 2px solid #f8f9fa;
    display: flex;
    align-items: center;
    gap: 8px;
}

.marker-popup h6 i {
    font-size: 1.2rem;
}

.info-table {
    width: 100%;
    margin: 0;
    padding: 0 20px 15px 20px;
}

.info-table td {
    padding: 6px 0;
    border: none;
    font-size: 0.9rem;
    line-height: 1.4;
}

.info-table td:first-child {
    font-weight: 600;
    color: #495057;
    width: 35%;
    padding-right: 10px;
}

.info-table td:last-child {
    color: #6c757d;
}

/* Leaflet Popup Arrow */
.leaflet-popup-tip {
    background: white !important;
    border: 1px solid #e9ecef !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

/* Filter Buttons */
.filter-buttons {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.filter-buttons .btn {
    border-radius: 25px;
    padding: 8px 20px;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.filter-buttons .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.5s;
}

.filter-buttons .btn:hover::before {
    left: 100%;
}

.filter-buttons .btn.active {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0,0,0,0.15);
}

.filter-buttons .btn-outline-primary.active {
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-color: #007bff;
    color: white;
}

.filter-buttons .btn-outline-success.active {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    border-color: #28a745;
    color: white;
}

.filter-buttons .btn-outline-warning.active {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    border-color: #ffc107;
    color: #212529;
}

.filter-buttons .btn-outline-danger.active {
    background: linear-gradient(135deg, #dc3545, #c82333);
    border-color: #dc3545;
    color: white;
}

/* Statistics Cards */
.stats-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.15);
}

.stats-card .card-body {
    padding: 20px;
}

.stats-card h5 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 5px;
}

.stats-card small {
    font-size: 0.85rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Legend */
.legend-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.legend-item {
    display: flex;
    align-items: center;
    padding: 8px 0;
    font-weight: 500;
    transition: all 0.2s ease;
}

.legend-item:hover {
    transform: translateX(5px);
}

.legend-badge {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 12px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

/* Map Controls */
.leaflet-control-zoom {
    border-radius: 8px !important;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.leaflet-control-zoom a {
    border-radius: 8px !important;
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    color: #495057 !important;
    font-weight: 600 !important;
    transition: all 0.2s ease !important;
}

.leaflet-control-zoom a:hover {
    background: #007bff !important;
    color: white !important;
    transform: scale(1.05) !important;
}

.leaflet-control-scale {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 6px !important;
    padding: 4px 8px !important;
    font-weight: 500 !important;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    #map {
        height: 400px;
    }
    
    .marker-popup {
        min-width: 200px;
        max-width: 280px;
    }
    
    .marker-popup h6 {
        font-size: 1rem;
        padding: 12px 15px 8px 15px;
    }
    
    .info-table {
        padding: 0 15px 12px 15px;
    }
    
    .info-table td {
        font-size: 0.85rem;
        padding: 4px 0;
    }
    
    .filter-buttons .btn {
        padding: 6px 15px;
        font-size: 0.8rem;
        margin: 2px;
    }
    
    .stats-card h5 {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    #map {
        height: 350px;
        border-radius: 10px;
    }
    
    .filter-buttons {
        padding: 10px;
        border-radius: 8px;
    }
    
    .filter-buttons .btn {
        padding: 5px 12px;
        font-size: 0.75rem;
        border-radius: 20px;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .map-container {
        background: #2c3e50;
    }
    
    .filter-buttons,
    .stats-card,
    .legend-card {
        background: rgba(44, 62, 80, 0.95);
        border-color: rgba(255, 255, 255, 0.1);
        color: #ecf0f1;
    }
    
    .leaflet-control-zoom a {
        background: rgba(44, 62, 80, 0.95) !important;
        color: #ecf0f1 !important;
    }
    
    .leaflet-control-scale {
        background: rgba(44, 62, 80, 0.9) !important;
        color: #ecf0f1 !important;
    }
}

/* Print Styles */
@media print {
    .filter-buttons,
    .leaflet-control-zoom,
    .leaflet-control-scale {
        display: none !important;
    }
    
    #map {
        height: 500px !important;
        border-radius: 0 !important;
        box-shadow: none !important;
    }
    
    .map-container {
        border-radius: 0 !important;
        box-shadow: none !important;
    }
}
