{% extends "rapport_base.html" %}

{% block title %}Rapport d'Intervention N° {{ intervention.id }} - {{ company_info.nom if company_info and company_info.nom else 'Système de Gestion de Maintenance' }}{% endblock %}

{% block rapport_title %}Rapport d'Intervention{% endblock %}

{% block rapport_subtitle %}Intervention N° {{ intervention.id }} - {{ intervention.domaine }}{% endblock %}

{% block rapport_meta %}
<div class="rapport-meta">
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-hashtag me-2"></i>N° d'Intervention:</span>
        <span class="meta-value">{{ intervention.id }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-user me-2"></i>Client:</span>
        <span class="meta-value">{{ intervention.client }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-file-contract me-2"></i>N° de Marché:</span>
        <span class="meta-value">{{ intervention.numero_marche }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-cogs me-2"></i>Domaine:</span>
        <span class="meta-value">
            {% if intervention.domaine == 'SVS' %}
                <span class="badge-custom badge-info">SVS</span>
            {% elif intervention.domaine == 'EXTINCTEUR' %}
                <span class="badge-custom badge-danger">EXTINCTEUR</span>
            {% elif intervention.domaine == 'SYSTEME D\'INCENDIE' %}
                <span class="badge-custom badge-warning">SYSTÈME D'INCENDIE</span>
            {% elif intervention.domaine == 'SYSTEME D\'ALARME' %}
                <span class="badge-custom badge-info">SYSTÈME D'ALARME</span>
            {% elif intervention.domaine == 'SYSTEME TELEPHONIQUE' %}
                <span class="badge-custom badge-success">SYSTÈME TÉLÉPHONIQUE</span>
            {% else %}
                <span class="badge-custom badge-secondary">{{ intervention.domaine }}</span>
            {% endif %}
        </span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-map-marker-alt me-2"></i>Lieu:</span>
        <span class="meta-value">{{ intervention.lieu }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-calendar me-2"></i>Date de Création:</span>
        <span class="meta-value">{{ intervention.date_creation }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-clock me-2"></i>Période d'Interventions:</span>
        <span class="meta-value">{{ intervention.periode_interventions }}</span>
    </div>
    {% if intervention.semestre %}
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-calendar-alt me-2"></i>Semestre:</span>
        <span class="meta-value">{{ intervention.semestre }}</span>
    </div>
    {% endif %}
</div>
{% endblock %}

{% block rapport_content %}
<!-- Informations du marché -->
<div class="section-title">
    <i class="fas fa-file-contract me-2"></i>Informations du Marché
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <table class="table table-custom">
            <tbody>
                <tr>
                    <td class="fw-bold" style="width: 40%;">Objet du marché</td>
                    <td>{{ intervention.objet_marche }}</td>
                </tr>
                <tr>
                    <td class="fw-bold">Délai d'exécution</td>
                    <td>{{ intervention.delai_execution }}</td>
                </tr>
                <tr>
                    <td class="fw-bold">Période du marché</td>
                    <td>{{ intervention.periode_marche }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Détails de l'intervention -->
{% if details %}
<div class="section-title">
    <i class="fas fa-list-alt me-2"></i>Détails de l'Intervention
</div>

<div class="table-responsive">
    <table class="table table-custom">
        <thead>
            <tr>
                <th>Région</th>
                <th>Site</th>
                <th>Nbr Système</th>
                <th>Date Intervention</th>
                <th>Technicien</th>
                <th>Situation</th>
                <th>État Matériel</th>
                <th>Observation</th>
            </tr>
        </thead>
        <tbody>
            {% for detail in details %}
            <tr>
                <td>{{ detail.region or '-' }}</td>
                <td>{{ detail.nom_site or '-' }}</td>
                <td class="text-center">{{ detail.nbr_systeme or '-' }}</td>
                <td>{{ detail.date_intervention or '-' }}</td>
                <td>{{ detail.technicien or '-' }}</td>
                <td>
                    {% if detail.situation == 'Réglé' %}
                        <span class="badge-custom badge-success">Réglé</span>
                    {% elif detail.situation == 'Pas encore' %}
                        <span class="badge-custom badge-warning">En attente</span>
                    {% elif detail.situation == 'Problème' %}
                        <span class="badge-custom badge-danger">Problème</span>
                    {% else %}
                        <span class="badge-custom badge-info">{{ detail.situation or '-' }}</span>
                    {% endif %}
                </td>
                <td>{{ detail.etat_materiel or '-' }}</td>
                <td>{{ detail.observation or '-' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- Détails spécifiques aux extincteurs -->
{% if intervention.domaine == 'EXTINCTEUR' and extincteurs %}
<div class="section-title">
    <i class="fas fa-fire-extinguisher me-2"></i>Détails des Extincteurs
</div>

<div class="table-responsive">
    <table class="table table-custom">
        <thead>
            <tr>
                <th>Type</th>
                <th>Capacité</th>
                <th>N° Série</th>
                <th>Date Fabrication</th>
                <th>Date Contrôle</th>
                <th>Prochaine Visite</th>
                <th>État</th>
                <th>Observation</th>
            </tr>
        </thead>
        <tbody>
            {% for extincteur in extincteurs %}
            <tr>
                <td>{{ extincteur.type or '-' }}</td>
                <td>{{ extincteur.capacite or '-' }}</td>
                <td>{{ extincteur.numero_serie or '-' }}</td>
                <td>{{ extincteur.date_fabrication or '-' }}</td>
                <td>{{ extincteur.date_controle or '-' }}</td>
                <td>{{ extincteur.prochaine_visite or '-' }}</td>
                <td>
                    {% if extincteur.etat == 'Bon' %}
                        <span class="badge-custom badge-success">Bon</span>
                    {% elif extincteur.etat == 'Défaillant' %}
                        <span class="badge-custom badge-danger">Défaillant</span>
                    {% elif extincteur.etat == 'À remplacer' %}
                        <span class="badge-custom badge-warning">À remplacer</span>
                    {% else %}
                        <span class="badge-custom badge-info">{{ extincteur.etat or '-' }}</span>
                    {% endif %}
                </td>
                <td>{{ extincteur.observation or '-' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- Résumé et recommandations -->
<div class="section-title">
    <i class="fas fa-clipboard-check me-2"></i>Résumé et Recommandations
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card border-0 bg-light">
            <div class="card-body">
                <h6 class="card-title text-primary">
                    <i class="fas fa-chart-pie me-2"></i>Statistiques
                </h6>
                {% if details %}
                <ul class="list-unstyled mb-0">
                    <li><strong>Total des sites:</strong> {{ details|length }}</li>
                    <li><strong>Sites réglés:</strong> {{ details|selectattr('situation', 'equalto', 'Réglé')|list|length }}</li>
                    <li><strong>Sites en attente:</strong> {{ details|selectattr('situation', 'equalto', 'Pas encore')|list|length }}</li>
                    <li><strong>Sites avec problème:</strong> {{ details|selectattr('situation', 'equalto', 'Problème')|list|length }}</li>
                </ul>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card border-0 bg-light">
            <div class="card-body">
                <h6 class="card-title text-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>Recommandations
                </h6>
                <ul class="list-unstyled mb-0">
                    <li>• Vérifier régulièrement l'état des équipements</li>
                    <li>• Maintenir un planning de maintenance préventive</li>
                    <li>• Former le personnel sur les procédures de sécurité</li>
                    <li>• Documenter toutes les interventions</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Notes additionnelles -->
<div class="mt-4 p-3 bg-light rounded">
    <h6 class="text-muted mb-2">
        <i class="fas fa-sticky-note me-2"></i>Notes additionnelles
    </h6>
    <p class="mb-0 text-muted">
        Cette intervention a été réalisée conformément aux normes en vigueur et aux spécifications du marché.
        Tous les équipements ont été vérifiés et testés selon les procédures établies.
    </p>
</div>
{% endblock %}
