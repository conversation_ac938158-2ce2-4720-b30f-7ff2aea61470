#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Système de sauvegarde automatique pour l'application de maintenance
Permet de programmer des sauvegardes automatiques quotidiennes, hebdomadaires ou mensuelles
"""

import os
import sqlite3
import shutil
import json
import schedule
import time
import threading
from datetime import datetime, timedelta
import logging

# Configuration du logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backup_scheduler.log'),
        logging.StreamHandler()
    ]
)

class BackupScheduler:
    def __init__(self, db_path='maintenance.db', backup_dir='backups'):
        self.db_path = db_path
        self.backup_dir = backup_dir
        self.config_file = 'backup_config.json'
        self.running = False
        
        # Créer le dossier de sauvegarde s'il n'existe pas
        if not os.path.exists(self.backup_dir):
            os.makedirs(self.backup_dir)
        
        # Charger la configuration
        self.load_config()
        
        # Configurer les tâches
        self.setup_schedules()
    
    def load_config(self):
        """Charger la configuration depuis le fichier JSON"""
        default_config = {
            'daily_backup': {
                'enabled': True,
                'time': '02:00',
                'keep_days': 7
            },
            'weekly_backup': {
                'enabled': True,
                'day': 'sunday',
                'time': '03:00',
                'keep_weeks': 4
            },
            'monthly_backup': {
                'enabled': True,
                'day': 1,
                'time': '04:00',
                'keep_months': 12
            },
            'auto_cleanup': {
                'enabled': True,
                'max_backup_size_gb': 5.0
            }
        }
        
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self.config = json.load(f)
            else:
                self.config = default_config
                self.save_config()
        except Exception as e:
            logging.error(f"Erreur lors du chargement de la configuration: {e}")
            self.config = default_config
    
    def save_config(self):
        """Sauvegarder la configuration dans le fichier JSON"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logging.error(f"Erreur lors de la sauvegarde de la configuration: {e}")
    
    def setup_schedules(self):
        """Configurer les tâches programmées"""
        schedule.clear()
        
        # Sauvegarde quotidienne
        if self.config['daily_backup']['enabled']:
            schedule.every().day.at(self.config['daily_backup']['time']).do(
                self.daily_backup
            )
            logging.info(f"Sauvegarde quotidienne programmée à {self.config['daily_backup']['time']}")
        
        # Sauvegarde hebdomadaire
        if self.config['weekly_backup']['enabled']:
            day = self.config['weekly_backup']['day'].lower()
            time_str = self.config['weekly_backup']['time']
            
            if day == 'monday':
                schedule.every().monday.at(time_str).do(self.weekly_backup)
            elif day == 'tuesday':
                schedule.every().tuesday.at(time_str).do(self.weekly_backup)
            elif day == 'wednesday':
                schedule.every().wednesday.at(time_str).do(self.weekly_backup)
            elif day == 'thursday':
                schedule.every().thursday.at(time_str).do(self.weekly_backup)
            elif day == 'friday':
                schedule.every().friday.at(time_str).do(self.weekly_backup)
            elif day == 'saturday':
                schedule.every().saturday.at(time_str).do(self.weekly_backup)
            else:  # sunday
                schedule.every().sunday.at(time_str).do(self.weekly_backup)
            
            logging.info(f"Sauvegarde hebdomadaire programmée le {day} à {time_str}")
        
        # Sauvegarde mensuelle
        if self.config['monthly_backup']['enabled']:
            schedule.every().day.at(self.config['monthly_backup']['time']).do(
                self.check_monthly_backup
            )
            logging.info(f"Vérification mensuelle programmée à {self.config['monthly_backup']['time']}")
        
        # Nettoyage automatique
        if self.config['auto_cleanup']['enabled']:
            schedule.every().day.at("05:00").do(self.cleanup_old_backups)
            logging.info("Nettoyage automatique programmé à 05:00")
    
    def create_backup(self, backup_type='manual'):
        """Créer une sauvegarde de la base de données"""
        try:
            if not os.path.exists(self.db_path):
                logging.error(f"Base de données non trouvée: {self.db_path}")
                return False
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_filename = f'maintenance_{backup_type}_{timestamp}.db'
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            # Copier la base de données
            shutil.copy2(self.db_path, backup_path)
            
            # Vérifier que la sauvegarde est valide
            if self.verify_backup(backup_path):
                file_size = os.path.getsize(backup_path)
                logging.info(f"Sauvegarde {backup_type} créée: {backup_filename} ({file_size} bytes)")
                return backup_filename
            else:
                os.remove(backup_path)
                logging.error(f"Sauvegarde {backup_type} invalide, supprimée")
                return False
                
        except Exception as e:
            logging.error(f"Erreur lors de la création de la sauvegarde {backup_type}: {e}")
            return False
    
    def verify_backup(self, backup_path):
        """Vérifier qu'une sauvegarde est valide"""
        try:
            conn = sqlite3.connect(backup_path)
            cursor = conn.cursor()
            
            # Vérifier que les tables principales existent
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['sites', 'marches', 'interventions', 'reclamations', 'regions', 'utilisateurs']
            for table in required_tables:
                if table not in tables:
                    logging.warning(f"Table manquante dans la sauvegarde: {table}")
            
            conn.close()
            return True
            
        except Exception as e:
            logging.error(f"Erreur lors de la vérification de la sauvegarde: {e}")
            return False
    
    def daily_backup(self):
        """Effectuer une sauvegarde quotidienne"""
        logging.info("Début de la sauvegarde quotidienne")
        backup_file = self.create_backup('daily')
        if backup_file:
            self.cleanup_daily_backups()
        return backup_file
    
    def weekly_backup(self):
        """Effectuer une sauvegarde hebdomadaire"""
        logging.info("Début de la sauvegarde hebdomadaire")
        backup_file = self.create_backup('weekly')
        if backup_file:
            self.cleanup_weekly_backups()
        return backup_file
    
    def check_monthly_backup(self):
        """Vérifier s'il faut effectuer une sauvegarde mensuelle"""
        today = datetime.now()
        if today.day == self.config['monthly_backup']['day']:
            logging.info("Début de la sauvegarde mensuelle")
            backup_file = self.create_backup('monthly')
            if backup_file:
                self.cleanup_monthly_backups()
            return backup_file
        return None
    
    def cleanup_daily_backups(self):
        """Nettoyer les anciennes sauvegardes quotidiennes"""
        try:
            keep_days = self.config['daily_backup']['keep_days']
            cutoff_date = datetime.now() - timedelta(days=keep_days)
            
            for filename in os.listdir(self.backup_dir):
                if filename.startswith('maintenance_daily_'):
                    filepath = os.path.join(self.backup_dir, filename)
                    file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                    
                    if file_time < cutoff_date:
                        os.remove(filepath)
                        logging.info(f"Sauvegarde quotidienne supprimée: {filename}")
                        
        except Exception as e:
            logging.error(f"Erreur lors du nettoyage des sauvegardes quotidiennes: {e}")
    
    def cleanup_weekly_backups(self):
        """Nettoyer les anciennes sauvegardes hebdomadaires"""
        try:
            keep_weeks = self.config['weekly_backup']['keep_weeks']
            cutoff_date = datetime.now() - timedelta(weeks=keep_weeks)
            
            for filename in os.listdir(self.backup_dir):
                if filename.startswith('maintenance_weekly_'):
                    filepath = os.path.join(self.backup_dir, filename)
                    file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                    
                    if file_time < cutoff_date:
                        os.remove(filepath)
                        logging.info(f"Sauvegarde hebdomadaire supprimée: {filename}")
                        
        except Exception as e:
            logging.error(f"Erreur lors du nettoyage des sauvegardes hebdomadaires: {e}")
    
    def cleanup_monthly_backups(self):
        """Nettoyer les anciennes sauvegardes mensuelles"""
        try:
            keep_months = self.config['monthly_backup']['keep_months']
            cutoff_date = datetime.now() - timedelta(days=keep_months * 30)
            
            for filename in os.listdir(self.backup_dir):
                if filename.startswith('maintenance_monthly_'):
                    filepath = os.path.join(self.backup_dir, filename)
                    file_time = datetime.fromtimestamp(os.path.getmtime(filepath))
                    
                    if file_time < cutoff_date:
                        os.remove(filepath)
                        logging.info(f"Sauvegarde mensuelle supprimée: {filename}")
                        
        except Exception as e:
            logging.error(f"Erreur lors du nettoyage des sauvegardes mensuelles: {e}")
    
    def cleanup_old_backups(self):
        """Nettoyer les anciennes sauvegardes selon la taille limite"""
        try:
            max_size_bytes = self.config['auto_cleanup']['max_backup_size_gb'] * 1024 * 1024 * 1024
            
            # Calculer la taille totale des sauvegardes
            total_size = 0
            backup_files = []
            
            for filename in os.listdir(self.backup_dir):
                filepath = os.path.join(self.backup_dir, filename)
                if os.path.isfile(filepath):
                    size = os.path.getsize(filepath)
                    mtime = os.path.getmtime(filepath)
                    backup_files.append((filepath, size, mtime))
                    total_size += size
            
            if total_size > max_size_bytes:
                # Trier par date de modification (plus ancien en premier)
                backup_files.sort(key=lambda x: x[2])
                
                # Supprimer les plus anciens jusqu'à respecter la limite
                for filepath, size, mtime in backup_files:
                    if total_size <= max_size_bytes:
                        break
                    
                    os.remove(filepath)
                    total_size -= size
                    logging.info(f"Sauvegarde supprimée pour respecter la limite de taille: {os.path.basename(filepath)}")
                    
        except Exception as e:
            logging.error(f"Erreur lors du nettoyage automatique: {e}")
    
    def start(self):
        """Démarrer le planificateur"""
        self.running = True
        logging.info("Planificateur de sauvegarde démarré")
        
        while self.running:
            schedule.run_pending()
            time.sleep(60)  # Vérifier toutes les minutes
    
    def stop(self):
        """Arrêter le planificateur"""
        self.running = False
        logging.info("Planificateur de sauvegarde arrêté")
    
    def run_in_background(self):
        """Exécuter le planificateur en arrière-plan"""
        thread = threading.Thread(target=self.start, daemon=True)
        thread.start()
        return thread

def main():
    """Fonction principale pour exécuter le planificateur"""
    scheduler = BackupScheduler()
    
    try:
        logging.info("Démarrage du système de sauvegarde automatique")
        scheduler.start()
    except KeyboardInterrupt:
        logging.info("Arrêt du système de sauvegarde automatique")
        scheduler.stop()

if __name__ == "__main__":
    main()
