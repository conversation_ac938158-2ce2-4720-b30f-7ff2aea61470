{% extends "base.html" %}

{% block title %}Modifier une Région - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Modifier une Région{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <i class="fas fa-map-marker-alt"></i> Modifier la région
            </div>
            <div class="card-body">
                <form method="POST" action="{{ url_for('modifier_region', id=region.id) }}">
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="nom" class="form-label">Nom de la région</label>
                            <input type="text" class="form-control" id="nom" name="nom" value="{{ region.nom }}" required>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3">{{ region.description }}</textarea>
                        </div>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Enregistrer les modifications
                        </button>
                        <a href="{{ url_for('regions') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
