#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار فتح المتصفح
"""

import os
import sys
import time
import threading
import webbrowser
import socket
import subprocess
from contextlib import closing

def find_free_port():
    """البحث عن منفذ متاح"""
    with closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as s:
        s.bind(('', 0))
        s.listen(1)
        port = s.getsockname()[1]
    return port

def test_browser_methods():
    """اختبار طرق فتح المتصفح"""
    
    print("🧪 اختبار طرق فتح المتصفح")
    print("=" * 50)
    
    test_url = "https://www.google.com"
    
    methods = [
        ("os.startfile", lambda: os.startfile(test_url)),
        ("subprocess start", lambda: subprocess.run(['start', test_url], shell=True)),
        ("subprocess cmd", lambda: subprocess.run(['cmd', '/c', 'start', test_url])),
        ("webbrowser.open", lambda: webbrowser.open(test_url)),
        ("webbrowser.open_new_tab", lambda: webbrowser.open_new_tab(test_url)),
        ("subprocess explorer", lambda: subprocess.run(['explorer', test_url])),
    ]
    
    working_methods = []
    
    for method_name, method_func in methods:
        print(f"\n🔧 اختبار: {method_name}")
        try:
            method_func()
            print(f"   ✅ نجح")
            working_methods.append(method_name)
            time.sleep(2)  # انتظار قصير
        except Exception as e:
            print(f"   ❌ فشل: {e}")
    
    print(f"\n📊 النتائج:")
    print(f"   ✅ الطرق الناجحة: {len(working_methods)}")
    print(f"   ❌ الطرق الفاشلة: {len(methods) - len(working_methods)}")
    
    if working_methods:
        print(f"\n🎯 الطرق الناجحة:")
        for method in working_methods:
            print(f"   • {method}")
    
    return working_methods

def test_local_server():
    """اختبار خادم محلي"""
    
    print(f"\n🌐 اختبار خادم محلي")
    print("=" * 50)
    
    try:
        from app import app
        
        port = find_free_port()
        url = f"http://127.0.0.1:{port}"
        
        print(f"📡 المنفذ: {port}")
        print(f"🌐 الرابط: {url}")
        
        def start_server():
            app.run(host='127.0.0.1', port=port, debug=False, use_reloader=False)
        
        def open_browser_after_delay():
            time.sleep(3)
            print(f"🔄 محاولة فتح المتصفح...")
            
            try:
                os.startfile(url)
                print(f"✅ تم فتح المتصفح بنجاح")
            except Exception as e:
                print(f"❌ فشل في فتح المتصفح: {e}")
        
        # تشغيل المتصفح في thread منفصل
        browser_thread = threading.Thread(target=open_browser_after_delay)
        browser_thread.daemon = True
        browser_thread.start()
        
        print(f"🚀 تشغيل الخادم...")
        print(f"⏰ سيتم فتح المتصفح خلال 3 ثوان")
        print(f"📝 اضغط Ctrl+C للإيقاف")
        
        start_server()
        
    except KeyboardInterrupt:
        print(f"\n⏹️ تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في الخادم: {e}")

def main():
    """الدالة الرئيسية"""
    
    print("🧪 اختبار فتح المتصفح لنظام إدارة الصيانة")
    print("🧪 Testing Browser Opening for Maintenance System")
    print()
    
    try:
        # اختبار طرق فتح المتصفح
        working_methods = test_browser_methods()
        
        if not working_methods:
            print(f"\n❌ لم تنجح أي طريقة لفتح المتصفح")
            print(f"💡 قد تحتاج لفتح المتصفح يدوياً")
        
        # سؤال المستخدم
        print(f"\n❓ هل تريد اختبار الخادم المحلي؟ (y/n): ", end="")
        choice = input().lower().strip()
        
        if choice in ['y', 'yes', 'نعم', '1']:
            test_local_server()
        else:
            print(f"✅ انتهى الاختبار")
        
    except KeyboardInterrupt:
        print(f"\n⏹️ تم إلغاء الاختبار")
    except Exception as e:
        print(f"\n❌ خطأ في الاختبار: {e}")

if __name__ == "__main__":
    main()
