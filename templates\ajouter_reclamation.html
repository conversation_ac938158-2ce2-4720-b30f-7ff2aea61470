{% extends "base.html" %}

{% block title %}Ajouter une Réclamation - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Ajouter une Réclamation{% endblock %}

{% block extra_css %}
<style>
  /* Styles pour les input-group */
  .input-group {
    margin-bottom: 0;
    border-radius: 0.25rem;
    transition: all 0.2s ease-in-out;
  }

  .input-group-prepend {
    display: flex;
  }

  .input-group-text {
    display: flex;
    align-items: center;
    padding: 0.375rem 0.75rem;
    font-size: 1rem;
    font-weight: 400;
    line-height: 1.5;
    color: #212529;
    text-align: center;
    white-space: nowrap;
    background-color: #e9ecef;
    border: 1px solid #ced4da;
    border-radius: 0.25rem 0 0 0.25rem;
  }

  .form-select, .form-control {
    border-radius: 0 0.25rem 0.25rem 0;
  }

    /* Boutons modernes */
    .btn-modern {
        border-radius: 8px;
        padding: 10px 20px;
        font-weight: 500;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border: none;
    }

    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    }

    .btn-modern:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .btn-modern i {
        font-size: 0.9em;
    }

    /* Couleurs spécifiques */
    .btn-primary.btn-modern {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    }

    .btn-success.btn-modern {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    }

    .btn-danger.btn-modern {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
    }

    .btn-warning.btn-modern {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #333;
    }

    .btn-info.btn-modern {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #333;
    }

    .btn-outline-primary.btn-modern {
        border: 2px solid #667eea;
        color: #667eea;
        background: transparent;
    }

    .btn-outline-primary.btn-modern:hover {
        background: #667eea;
        color: white;
    }

</style>
{% endblock %}

{% block content %}
<div class="container mt-4 mb-5">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h4 class="text-primary"><i class="fas fa-exclamation-circle me-2"></i> Ajouter une nouvelle réclamation</h4>
    <a href="{{ url_for('reclamations') }}" class="btn btn-outline-secondary">
      <i class="fas fa-arrow-left me-1"></i> Retour
    </a>
  </div>

  <!-- Card globale -->
  <div class="card shadow-sm">
    <div class="card-body">
      <form method="POST" action="{{ url_for('ajouter_reclamation') }}">
        <!-- Informations du marché -->
        <div class="row mb-4">
          <div class="col-md-6 mb-3">
            <label for="numero_marche" class="form-label">N° de marché</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-file-contract"></i></span>
              </div>
              <select class="form-select" id="numero_marche" name="numero_marche" required>
                <option value="">Sélectionner un marché</option>
                {% for marche in marches %}
                <option value="{{ marche.numero }}" data-client="{{ marche.client }}" data-objet="{{ marche.objet }}" data-delai="{{ marche.delai_execution }}" data-domaine="{{ marche.domaine }}" data-periode="{{ marche.periode_interventions }}" data-lieu="{{ marche.lieu }}">
                  {{ marche.numero }} - {{ marche.client }} ({{ marche.domaine }})
                </option>
                {% endfor %}
              </select>
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <label for="domaine" class="form-label">Domaine</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-cogs"></i></span>
              </div>
              <select class="form-select" id="domaine" name="domaine" required>
                <option value="">Sélectionner un domaine</option>
                <option value="SVS">SVS</option>
                <option value="EXTINCTEUR">EXTINCTEUR</option>
                <option value="SYSTEME D'INCENDIE">SYSTEME D'INCENDIE</option>
                <option value="SYSTEME D'ALARME">SYSTEME D'ALARME</option>
                <option value="SYSTEME TELEPHONIQUE">SYSTEME TELEPHONIQUE</option>
                <option value="L'AFFICHAGE DYNAMIQUE">L'AFFICHAGE DYNAMIQUE</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Informations client et objet -->
        <div class="row mb-4">
          <div class="col-md-6 mb-3">
            <label for="client" class="form-label">Client</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-user-tie"></i></span>
              </div>
              <input type="text" class="form-control" id="client" name="client" required>
            </div>
          </div>
          <div class="col-md-6 mb-3">
            <label for="objet_marche" class="form-label">Objet de marché</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-clipboard-list"></i></span>
              </div>
              <input type="text" class="form-control" id="objet_marche" name="objet_marche" required>
            </div>
          </div>
        </div>

        <!-- Informations périodes et délais -->
        <div class="row mb-4">
          <div class="col-md-3 mb-3">
            <label for="delai_execution" class="form-label">Délai d'exécution</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-clock"></i></span>
              </div>
              <input type="text" class="form-control" id="delai_execution" name="delai_execution">
            </div>
          </div>
          <div class="col-md-3 mb-3">
            <label for="periode_interventions" class="form-label">Période d'interventions</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
              </div>
              <select class="form-select" id="periode_interventions" name="periode_interventions">
                <option value="">Sélectionner une période</option>
                <option value="annuel">Annuel</option>
                <option value="semestriel">Semestriel</option>
                <option value="trimestriel">Trimestriel</option>
                <option value="mensuel">Mensuel</option>
              </select>
            </div>
          </div>
          <div class="col-md-3 mb-3">
            <label for="periode_marche" class="form-label">Période du marché</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-calendar-week"></i></span>
              </div>
              <input type="text" class="form-control" id="periode_marche" name="periode_marche" placeholder="Ex: 2023-2024">
            </div>
          </div>
          <div class="col-md-3 mb-3">
            <label for="lieu" class="form-label">Lieu</label>
            <div class="input-group">
              <div class="input-group-prepend">
                <span class="input-group-text"><i class="fas fa-map-marker-alt"></i></span>
              </div>
              <input type="text" class="form-control" id="lieu" name="lieu">
            </div>
          </div>
        </div>

        <div class="alert alert-info">
          <i class="fas fa-info-circle me-2"></i> Après avoir enregistré la réclamation, vous pourrez ajouter les détails spécifiques.
        </div>

        <!-- Boutons d'action -->
        <div class="d-flex justify-content-between mt-4">
          <button type="submit" class="btn btn-primary">
            <i class="fas fa-save me-1"></i> Enregistrer et continuer
          </button>
          <div>
            <button type="reset" class="btn btn-outline-secondary me-2">
              <i class="fas fa-undo me-1"></i> Réinitialiser
            </button>
            <a href="{{ url_for('reclamations') }}" class="btn btn-outline-danger">
              <i class="fas fa-times me-1"></i> Annuler
            </a>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-remplir les champs en fonction du marché sélectionné
        document.getElementById('numero_marche').addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];

            if (selectedOption.value) {
                document.getElementById('client').value = selectedOption.getAttribute('data-client');
                document.getElementById('objet_marche').value = selectedOption.getAttribute('data-objet');
                document.getElementById('delai_execution').value = selectedOption.getAttribute('data-delai');
                document.getElementById('lieu').value = selectedOption.getAttribute('data-lieu');

                // Sélectionner le domaine
                const domaine = selectedOption.getAttribute('data-domaine');
                const domaineSelect = document.getElementById('domaine');
                for (let i = 0; i < domaineSelect.options.length; i++) {
                    if (domaineSelect.options[i].value === domaine) {
                        domaineSelect.selectedIndex = i;
                        break;
                    }
                }

                // Sélectionner la période d'interventions
                const periode = selectedOption.getAttribute('data-periode');
                const periodeSelect = document.getElementById('periode_interventions');
                for (let i = 0; i < periodeSelect.options.length; i++) {
                    if (periodeSelect.options[i].value === periode) {
                        periodeSelect.selectedIndex = i;
                        break;
                    }
                }
            }
        });

        // Ajouter des effets visuels pour améliorer l'expérience utilisateur
        const formInputs = document.querySelectorAll('input, select');
        formInputs.forEach(input => {
            // Ajouter une classe lors du focus
            input.addEventListener('focus', function() {
                const inputGroup = this.closest('.input-group');
                if (inputGroup) {
                    inputGroup.style.borderColor = '#0d6efd';
                    inputGroup.style.boxShadow = '0 0 0 0.25rem rgba(13, 110, 253, 0.25)';
                    const icon = inputGroup.querySelector('.input-group-text');
                    if (icon) {
                        icon.style.backgroundColor = '#e9ecef';
                        icon.style.borderColor = '#0d6efd';
                    }
                }
            });

            // Retirer la classe lors de la perte du focus
            input.addEventListener('blur', function() {
                const inputGroup = this.closest('.input-group');
                if (inputGroup) {
                    inputGroup.style.borderColor = '';
                    inputGroup.style.boxShadow = '';
                    const icon = inputGroup.querySelector('.input-group-text');
                    if (icon) {
                        icon.style.backgroundColor = '';
                        icon.style.borderColor = '';
                    }
                }
            });
        });
    });
</script>
{% endblock %}
