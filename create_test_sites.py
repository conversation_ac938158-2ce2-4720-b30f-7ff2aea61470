#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script pour créer un fichier Excel de test pour l'import des sites
"""

import pandas as pd

# Données de test pour les sites
sites_data = [
    {
        'NOM DU SITE': 'Site Test 1',
        'ADRESSE': '123 Rue Test, Casablanca',
        'TÉLÉPHONE': '+212 522 123456',
        'RESPONSABLE': '<PERSON>',
        'EMAIL': '<EMAIL>'
    },
    {
        'NOM DU SITE': 'Site Test 2', 
        'ADRESSE': '456 Avenue Mohammed V, Rabat',
        'TÉLÉPHONE': '+212 537 654321',
        'RESPONSABLE': 'Fat<PERSON> Benali',
        'EMAIL': '<EMAIL>'
    },
    {
        'NOM DU SITE': 'Site Test 3',
        'ADRESSE': '789 Boulevard Hassan II, Marrakech',
        'TÉLÉPHONE': '+212 524 987654',
        'RESPONSABLE': '<PERSON>',
        'EMAIL': '<EMAIL>'
    }
]

# C<PERSON>er le DataFrame
df = pd.DataFrame(sites_data)

# Sauvegarder en Excel
filename = 'sites_test_import.xlsx'
df.to_excel(filename, index=False, sheet_name='Sites')

print(f"✅ Fichier Excel créé: {filename}")
print(f"📊 Nombre de sites: {len(sites_data)}")
print("\n📋 Colonnes:")
for col in df.columns:
    print(f"  - {col}")

print(f"\n🔍 Aperçu des données:")
print(df.to_string(index=False))
