
// Fonctions d'exportation améliorées

function exportTableToExcel(tableId, filename) {
    try {
        const table = document.getElementById(tableId);
        if (!table) {
            showNotification('Table non trouvée', 'error');
            return;
        }

        if (typeof XLSX === 'undefined') {
            showNotification('Bibliothèque Excel non chargée. Veuillez recharger la page.', 'error');
            return;
        }

        // Afficher un indicateur de chargement
        showNotification('Exportation en cours...', 'info');

        // Cloner la table pour nettoyer les données
        const tableClone = table.cloneNode(true);

        // Supprimer les colonnes d'actions
        const actionHeaders = tableClone.querySelectorAll('th:last-child');
        const actionCells = tableClone.querySelectorAll('td:last-child');

        actionHeaders.forEach(header => {
            if (header.textContent.toLowerCase().includes('action')) {
                header.remove();
            }
        });

        actionCells.forEach(cell => {
            if (cell.querySelector('.btn')) {
                cell.remove();
            }
        });

        // Créer le workbook
        const wb = XLSX.utils.table_to_book(tableClone, {
            sheet: "Données",
            raw: false
        });

        // Ajouter des métadonnées
        const ws = wb.Sheets["Données"];
        if (ws['!ref']) {
            ws['!cols'] = [
                {wch: 10}, {wch: 20}, {wch: 15}, {wch: 25},
                {wch: 20}, {wch: 15}, {wch: 20}, {wch: 15}
            ];
        }

        // Exporter le fichier
        const timestamp = new Date().toISOString().slice(0, 10);
        XLSX.writeFile(wb, `${filename}_${timestamp}.xlsx`);

        showNotification('Exportation réussie!', 'success');

    } catch (error) {
        console.error('Erreur lors de l\'exportation:', error);
        showNotification('Une erreur s\'est produite lors de l\'exportation', 'error');
    }
}

function printPage() {
    // Ajouter une classe pour l'impression
    document.body.classList.add('printing');

    // Attendre un peu pour que les styles s'appliquent
    setTimeout(() => {
        window.print();
        document.body.classList.remove('printing');
    }, 100);
}

// Fonction pour afficher des notifications
function showNotification(message, type = 'info') {
    // Supprimer les notifications existantes
    const existingNotifications = document.querySelectorAll('.custom-notification');
    existingNotifications.forEach(notification => notification.remove());

    // Créer la notification
    const notification = document.createElement('div');
    notification.className = `custom-notification alert alert-${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        animation: slideInRight 0.3s ease-out;
    `;
    notification.innerHTML = `
        <i class="fas fa-${getIconForType(type)} me-2"></i>
        ${message}
        <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
    `;

    document.body.appendChild(notification);

    // Supprimer automatiquement après 5 secondes
    setTimeout(() => {
        if (notification.parentElement) {
            notification.style.animation = 'slideOutRight 0.3s ease-out';
            setTimeout(() => notification.remove(), 300);
        }
    }, 5000);
}

function getIconForType(type) {
    switch(type) {
        case 'success': return 'check-circle';
        case 'error': return 'exclamation-circle';
        case 'warning': return 'exclamation-triangle';
        case 'info': return 'info-circle';
        default: return 'info-circle';
    }
}

// Ajouter les animations CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from { transform: translateX(100%); opacity: 0; }
        to { transform: translateX(0); opacity: 1; }
    }
    @keyframes slideOutRight {
        from { transform: translateX(0); opacity: 1; }
        to { transform: translateX(100%); opacity: 0; }
    }
    .custom-notification {
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    .custom-notification .btn-close {
        background: none;
        border: none;
        font-size: 18px;
        cursor: pointer;
        opacity: 0.7;
    }
    .custom-notification .btn-close:hover {
        opacity: 1;
    }
`;
document.head.appendChild(style);
