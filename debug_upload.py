#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشخيص مشكلة تحميل الشعار
"""

import sqlite3
import os
import requests
from werkzeug.utils import secure_filename

def debug_upload_issue():
    """تشخيص مشكلة تحميل الشعار"""
    print("🔍 تشخيص مشكلة تحميل الشعار")
    print("=" * 50)
    
    # 1. فحص قاعدة البيانات بالتفصيل
    print("1️⃣ فحص قاعدة البيانات...")
    try:
        conn = sqlite3.connect('maintenance.db')
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        # فحص بنية الجدول
        cursor.execute("PRAGMA table_info(societe)")
        columns = cursor.fetchall()
        print("   📋 أعمدة جدول societe:")
        for col in columns:
            print(f"      - {col['name']}: {col['type']} (nullable: {not col['notnull']})")
        
        # فحص البيانات الحالية
        cursor.execute("SELECT * FROM societe")
        rows = cursor.fetchall()
        print(f"\n   📊 عدد الصفوف: {len(rows)}")
        
        for i, row in enumerate(rows):
            print(f"   📝 الصف {i+1}:")
            for key in row.keys():
                value = row[key]
                if key == 'logo':
                    print(f"      {key}: {value} {'(فارغ)' if not value else '(موجود)'}")
                else:
                    print(f"      {key}: {value}")
        
        conn.close()
        
    except Exception as e:
        print(f"   ❌ خطأ في قاعدة البيانات: {e}")
    
    # 2. فحص مجلد uploads
    print("\n2️⃣ فحص مجلد uploads...")
    uploads_dir = 'static/uploads'
    if os.path.exists(uploads_dir):
        files = os.listdir(uploads_dir)
        print(f"   📁 عدد الملفات في {uploads_dir}: {len(files)}")
        for file in files:
            file_path = os.path.join(uploads_dir, file)
            size = os.path.getsize(file_path)
            print(f"      - {file} ({size} bytes)")
    else:
        print(f"   ❌ مجلد {uploads_dir} غير موجود")
    
    # 3. اختبار دالة secure_filename
    print("\n3️⃣ اختبار دالة secure_filename...")
    test_names = ["logo.png", "شعار.jpg", "logo test.gif"]
    for name in test_names:
        secure_name = secure_filename(name)
        print(f"   '{name}' -> '{secure_name}'")
    
    # 4. فحص أذونات المجلدات
    print("\n4️⃣ فحص أذونات المجلدات...")
    dirs_to_check = ['static', 'static/uploads', 'static/images']
    for dir_path in dirs_to_check:
        if os.path.exists(dir_path):
            stat_info = os.stat(dir_path)
            permissions = oct(stat_info.st_mode)[-3:]
            print(f"   📂 {dir_path}: أذونات {permissions}")
        else:
            print(f"   ❌ {dir_path}: غير موجود")
    
    # 5. محاولة إنشاء ملف اختبار
    print("\n5️⃣ اختبار كتابة ملف...")
    test_file = os.path.join(uploads_dir, 'test_write.txt')
    try:
        with open(test_file, 'w') as f:
            f.write('اختبار الكتابة')
        print(f"   ✅ تم إنشاء ملف اختبار: {test_file}")
        
        # حذف الملف
        os.remove(test_file)
        print(f"   🗑️ تم حذف ملف الاختبار")
        
    except Exception as e:
        print(f"   ❌ فشل في كتابة ملف اختبار: {e}")
    
    # 6. فحص متغيرات Flask
    print("\n6️⃣ فحص إعدادات Flask...")
    print("   📋 متغيرات مهمة:")
    print(f"      UPLOAD_FOLDER: static/uploads")
    print(f"      MAX_CONTENT_LENGTH: 50MB")
    print(f"      ALLOWED_EXTENSIONS: png, jpg, jpeg, gif, bmp, webp, svg")
    
    print("\n🔧 خطوات التشخيص:")
    print("1. جرب تحميل شعار صغير (أقل من 1MB)")
    print("2. افتح أدوات المطور في المتصفح (F12)")
    print("3. انتقل إلى تبويب Network")
    print("4. جرب تحميل الشعار ومراقبة الطلبات")
    print("5. تحقق من وجود أخطاء في Console")
    
    print("\n📝 ملاحظات:")
    print("- تأكد من النقر على 'Enregistrer les modifications'")
    print("- تأكد من أن الملف من النوع المدعوم")
    print("- تأكد من أن حجم الملف أقل من 10MB")

if __name__ == "__main__":
    debug_upload_issue()
