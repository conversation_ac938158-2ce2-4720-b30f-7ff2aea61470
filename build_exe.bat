@echo off
title بناء نظام إدارة الصيانة - إصدار محسن
echo.
echo ========================================
echo    بناء نظام إدارة الصيانة - إصدار محسن
echo    Building Maintenance System - Enhanced
echo ========================================
echo.

echo 🔍 فحص Python...
echo 🔍 Checking Python...
python --version
if errorlevel 1 (
    echo ❌ Python غير مثبت أو غير موجود في PATH
    echo ❌ Python is not installed or not in PATH
    pause
    exit /b 1
)

echo.
echo 🧹 تنظيف الملفات السابقة...
echo 🧹 Cleaning previous files...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo.
echo 🔨 بناء الملف التنفيذي المحسن...
echo 🔨 Building enhanced executable...
echo.

python build_exe.py

if errorlevel 1 (
    echo.
    echo ❌ فشل في بناء الملف التنفيذي
    echo ❌ Failed to build executable
    pause
    exit /b 1
)

echo.
echo 🧪 اختبار الملف التنفيذي...
echo 🧪 Testing executable...
python test_executable.py

echo.
echo 🎉 تم بناء النظام المحسن بنجاح!
echo 🎉 Enhanced system built successfully!
echo.
echo 📁 الملفات موجودة في مجلد dist
echo 📁 Files are located in dist folder
echo.
echo ✨ التحسينات الجديدة:
echo ✨ New improvements:
echo    🌐 فتح متصفح محسن
echo    🌐 Enhanced browser opening
echo    📱 رسائل تفاعلية
echo    📱 Interactive messages
echo    🔧 معالجة أخطاء محسنة
echo    🔧 Enhanced error handling
echo.
pause
