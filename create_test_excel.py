#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import os

def create_test_excel():
    """إنشاء ملف Excel تجريبي للمواقع"""
    
    # البيانات التجريبية
    data = {
        'NOM DU SITE': [
            'Site Test 1',
            'Site Test 2', 
            'Site Test 3',
            'Site Test 4',
            'Site Test 5'
        ],
        'RÉGION': [
            'Casablanca-Settat',
            'Rabat-Salé-Kénitra',
            'Marrakech-Safi',
            '<PERSON><PERSON>-<PERSON>knès',
            'Tanger-Tétouan-Al Hoceima'
        ],
        'ADRESSE': [
            '123 Rue Mohammed V, Casablanca',
            '456 Avenue Hassan II, Rabat',
            '789 Boulevard Zerktouni, Marrakech',
            '321 Place Nejjarine, Fès',
            '654 Corniche Tanger, Tanger'
        ],
        'TÉLÉPHONE': [
            '0522123456',
            '0537654321',
            '0524987654',
            '0535111222',
            '0539333444'
        ],
        'RESPONSABLE': [
            '<PERSON>',
            '<PERSON><PERSON>',
            '<PERSON>',
            '<PERSON><PERSON><PERSON>',
            '<PERSON><PERSON>'
        ],
        'EMAIL': [
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>',
            '<EMAIL>'
        ]
    }
    
    # إنشاء DataFrame
    df = pd.DataFrame(data)
    
    # حفظ كملف Excel
    filename = 'sites_test.xlsx'
    df.to_excel(filename, index=False, engine='openpyxl')
    
    print(f"✅ تم إنشاء ملف Excel: {filename}")
    print(f"📁 المسار: {os.path.abspath(filename)}")
    print(f"📊 عدد السجلات: {len(df)}")
    print("\n📋 معاينة البيانات:")
    print(df.to_string(index=False))

if __name__ == "__main__":
    create_test_excel()
