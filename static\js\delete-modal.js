/**
 * مكون حوار التأكيد المحسن للحذف
 */

class DeleteModal {
    constructor() {
        this.modal = null;
        this.createModal();
        this.bindEvents();
    }

    createModal() {
        // إنشاء HTML للحوار
        const modalHTML = `
            <div id="deleteModal" class="delete-modal">
                <div class="delete-modal-content">
                    <div class="delete-modal-header">
                        <h5 class="delete-modal-title">
                            <i class="fas fa-exclamation-triangle"></i>
                            Confirmer la suppression
                        </h5>
                        <button type="button" class="delete-modal-close" onclick="deleteModal.hide()">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <div class="delete-modal-body">
                        <div class="delete-modal-message">
                            Êtes-vous sûr de vouloir supprimer <span class="delete-modal-item" id="deleteItemName"></span> ?
                        </div>
                        <div class="delete-modal-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            Cette action est irréversible !
                        </div>
                    </div>
                    <div class="delete-modal-footer">
                        <button type="button" class="delete-modal-btn delete-modal-btn-cancel" onclick="deleteModal.hide()">
                            Annuler
                        </button>
                        <button type="button" class="delete-modal-btn delete-modal-btn-delete" id="confirmDeleteBtn">
                            <i class="fas fa-trash"></i> Supprimer
                        </button>
                    </div>
                </div>
            </div>
        `;

        // إضافة الحوار إلى الصفحة
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        this.modal = document.getElementById('deleteModal');
    }

    bindEvents() {
        // إغلاق الحوار عند النقر خارجه
        this.modal.addEventListener('click', (e) => {
            if (e.target === this.modal) {
                this.hide();
            }
        });

        // إغلاق الحوار بمفتاح Escape
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.modal.classList.contains('show')) {
                this.hide();
            }
        });
    }

    show(options = {}) {
        const {
            title = 'Confirmer la suppression',
            message = 'Êtes-vous sûr de vouloir supprimer',
            itemName = 'cet élément',
            onConfirm = () => {},
            confirmText = 'Supprimer',
            cancelText = 'Annuler'
        } = options;

        // تحديث محتوى الحوار
        this.modal.querySelector('.delete-modal-title').innerHTML = `
            <i class="fas fa-exclamation-triangle"></i>
            ${title}
        `;

        this.modal.querySelector('.delete-modal-message').innerHTML = `
            ${message} <span class="delete-modal-item">${itemName}</span> ?
        `;

        // تحديث أزرار الحوار
        const cancelBtn = this.modal.querySelector('.delete-modal-btn-cancel');
        const confirmBtn = this.modal.querySelector('.delete-modal-btn-delete');

        cancelBtn.textContent = cancelText;
        confirmBtn.innerHTML = `<i class="fas fa-trash"></i> ${confirmText}`;

        // ربط حدث التأكيد
        confirmBtn.onclick = () => {
            this.hide();
            onConfirm();
        };

        // عرض الحوار
        this.modal.classList.add('show');
        document.body.style.overflow = 'hidden';

        // تركيز على زر الإلغاء
        setTimeout(() => {
            cancelBtn.focus();
        }, 100);
    }

    hide() {
        this.modal.classList.remove('show');
        document.body.style.overflow = '';
    }

    // دالة مساعدة لإنشاء حوار حذف سريع
    static confirm(itemName, deleteUrl, options = {}) {
        const modal = window.deleteModal || new DeleteModal();

        modal.show({
            itemName: itemName,
            onConfirm: () => {
                // إضافة مؤشر تحميل
                const loadingOverlay = document.createElement('div');
                loadingOverlay.innerHTML = `
                    <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                                background: rgba(0,0,0,0.8); z-index: 10000;
                                display: flex; align-items: center; justify-content: center;">
                        <div style="background: white; padding: 30px; border-radius: 15px; text-align: center;">
                            <i class="fas fa-spinner fa-spin fa-2x text-danger mb-3"></i>
                            <div>Suppression en cours...</div>
                        </div>
                    </div>
                `;
                document.body.appendChild(loadingOverlay);

                // الانتقال إلى رابط الحذف
                window.location.href = deleteUrl;
            },
            ...options
        });
    }
}

// إنشاء مثيل عام
window.deleteModal = new DeleteModal();

// دالة مساعدة عامة
window.confirmDelete = (itemName, deleteUrl, options = {}) => {
    console.log('confirmDelete called with:', itemName, deleteUrl);
    DeleteModal.confirm(itemName, deleteUrl, options);
};

// تأكيد أن الملف تم تحميله
console.log('delete-modal.js loaded successfully');
console.log('confirmDelete function created:', typeof window.confirmDelete);

// تطبيق الحوار على جميع أزرار الحذف الموجودة
document.addEventListener('DOMContentLoaded', function() {
    // البحث عن جميع أزرار الحذف
    const deleteButtons = document.querySelectorAll(`
        a[href*="supprimer"],
        button[onclick*="supprimer"],
        .btn-danger[href*="supprimer"],
        .btn-delete,
        [data-action="delete"]
    `);

    deleteButtons.forEach(button => {
        // تجاهل الأزرار التي تستخدم بالفعل الحوار المحسن
        if (button.onclick && button.onclick.toString().includes('confirmDelete')) {
            return;
        }

        const href = button.getAttribute('href');
        if (href && href.includes('supprimer')) {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                // استخراج اسم العنصر من السياق
                let itemName = 'cet élément';
                const row = button.closest('tr');
                if (row) {
                    const firstCell = row.querySelector('td:nth-child(2)');
                    if (firstCell) {
                        itemName = firstCell.textContent.trim();
                    }
                }

                // عرض حوار التأكيد
                confirmDelete(itemName, href);
            });
        }
    });

    // إضافة أنماط CSS إذا لم تكن موجودة
    if (!document.querySelector('link[href*="delete-modal.css"]')) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = '/static/css/delete-modal.css';
        document.head.appendChild(link);
    }
});

// دوال مساعدة للاستخدام المباشر
window.showDeleteConfirm = (title, message, onConfirm) => {
    window.deleteModal.show({
        title: title,
        message: message,
        onConfirm: onConfirm
    });
};

window.hideDeleteConfirm = () => {
    window.deleteModal.hide();
};
