#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص جداول قاعدة البيانات
"""

import sqlite3

def check_tables():
    """فحص جداول قاعدة البيانات"""
    try:
        conn = sqlite3.connect('maintenance.db')
        cursor = conn.cursor()
        
        # الحصول على جميع الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        
        print("📊 الجداول الموجودة في قاعدة البيانات:")
        for table in tables:
            table_name = table[0]
            print(f"   - {table_name}")
            
            # فحص بنية الجدول
            cursor.execute(f"PRAGMA table_info({table_name})")
            columns = cursor.fetchall()
            if table_name == 'utilisateurs':
                print(f"     أعمدة جدول {table_name}:")
                for col in columns:
                    print(f"       - {col[1]} ({col[2]})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    check_tables()
