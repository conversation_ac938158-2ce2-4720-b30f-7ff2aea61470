{% extends "rapport_base.html" %}

{% block title %}Rapport de Réclamation N° {{ reclamation.id }} - {{ company_info.nom if company_info and company_info.nom else 'Système de Gestion de Maintenance' }}{% endblock %}

{% block rapport_title %}Rapport de Réclamation{% endblock %}

{% block rapport_subtitle %}Réclamation N° {{ reclamation.id }} - {{ reclamation.domaine }}{% endblock %}

{% block rapport_meta %}
<div class="rapport-meta">
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-hashtag me-2"></i>N° de Réclamation:</span>
        <span class="meta-value">{{ reclamation.id }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-user me-2"></i>Client:</span>
        <span class="meta-value">{{ reclamation.client }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-file-contract me-2"></i>N° de Marché:</span>
        <span class="meta-value">{{ reclamation.numero_marche }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-cogs me-2"></i>Domaine:</span>
        <span class="meta-value">
            {% if reclamation.domaine == 'SVS' %}
                <span class="badge-custom badge-info">SVS</span>
            {% elif reclamation.domaine == 'EXTINCTEUR' %}
                <span class="badge-custom badge-danger">EXTINCTEUR</span>
            {% elif reclamation.domaine == 'SYSTEME D\'INCENDIE' %}
                <span class="badge-custom badge-warning">SYSTÈME D'INCENDIE</span>
            {% elif reclamation.domaine == 'SYSTEME D\'ALARME' %}
                <span class="badge-custom badge-info">SYSTÈME D'ALARME</span>
            {% elif reclamation.domaine == 'SYSTEME TELEPHONIQUE' %}
                <span class="badge-custom badge-success">SYSTÈME TÉLÉPHONIQUE</span>
            {% else %}
                <span class="badge-custom badge-secondary">{{ reclamation.domaine }}</span>
            {% endif %}
        </span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-map-marker-alt me-2"></i>Lieu:</span>
        <span class="meta-value">{{ reclamation.lieu }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-calendar me-2"></i>Date de Création:</span>
        <span class="meta-value">{{ reclamation.date_creation }}</span>
    </div>
    <div class="meta-row">
        <span class="meta-label"><i class="fas fa-exclamation-circle me-2"></i>Statut:</span>
        <span class="meta-value">
            <span class="badge-custom badge-warning">En cours de traitement</span>
        </span>
    </div>
</div>
{% endblock %}

{% block rapport_content %}
<!-- Description de la réclamation -->
<div class="section-title">
    <i class="fas fa-file-alt me-2"></i>Description de la Réclamation
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card border-0 bg-light">
            <div class="card-body">
                <h6 class="card-title text-danger">
                    <i class="fas fa-exclamation-triangle me-2"></i>Nature du problème
                </h6>
                <p class="mb-0">
                    {{ reclamation.description or 'Description non fournie' }}
                </p>
            </div>
        </div>
    </div>
</div>

<!-- Informations du marché -->
<div class="section-title">
    <i class="fas fa-file-contract me-2"></i>Informations du Marché Concerné
</div>

<div class="row mb-4">
    <div class="col-md-6">
        <table class="table table-custom">
            <tbody>
                <tr>
                    <td class="fw-bold" style="width: 40%;">Objet du marché</td>
                    <td>{{ reclamation.objet_marche or '-' }}</td>
                </tr>
                <tr>
                    <td class="fw-bold">Délai d'exécution</td>
                    <td>{{ reclamation.delai_execution or '-' }}</td>
                </tr>
                <tr>
                    <td class="fw-bold">Période du marché</td>
                    <td>{{ reclamation.periode_marche or '-' }}</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Détails de la réclamation -->
{% if details %}
<div class="section-title">
    <i class="fas fa-list-alt me-2"></i>Détails de la Réclamation
</div>

<div class="table-responsive">
    <table class="table table-custom">
        <thead>
            <tr>
                <th>Région</th>
                <th>Site</th>
                <th>Date Réclamation</th>
                <th>Type de Problème</th>
                <th>Urgence</th>
                <th>Statut</th>
                <th>Action Entreprise</th>
                <th>Observation</th>
            </tr>
        </thead>
        <tbody>
            {% for detail in details %}
            <tr>
                <td>{{ detail.region or '-' }}</td>
                <td>{{ detail.nom_site or '-' }}</td>
                <td>{{ detail.date_reclamation or '-' }}</td>
                <td>{{ detail.type_probleme or '-' }}</td>
                <td>
                    {% if detail.urgence == 'Haute' %}
                        <span class="badge-custom badge-danger">Haute</span>
                    {% elif detail.urgence == 'Moyenne' %}
                        <span class="badge-custom badge-warning">Moyenne</span>
                    {% elif detail.urgence == 'Basse' %}
                        <span class="badge-custom badge-success">Basse</span>
                    {% else %}
                        <span class="badge-custom badge-info">{{ detail.urgence or '-' }}</span>
                    {% endif %}
                </td>
                <td>
                    {% if detail.statut == 'Résolu' %}
                        <span class="badge-custom badge-success">Résolu</span>
                    {% elif detail.statut == 'En cours' %}
                        <span class="badge-custom badge-warning">En cours</span>
                    {% elif detail.statut == 'En attente' %}
                        <span class="badge-custom badge-info">En attente</span>
                    {% else %}
                        <span class="badge-custom badge-secondary">{{ detail.statut or '-' }}</span>
                    {% endif %}
                </td>
                <td>{{ detail.action_entreprise or '-' }}</td>
                <td>{{ detail.observation or '-' }}</td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endif %}

<!-- Analyse et suivi -->
<div class="section-title">
    <i class="fas fa-chart-line me-2"></i>Analyse et Suivi
</div>

<div class="row">
    <div class="col-md-6">
        <div class="card border-0 bg-light">
            <div class="card-body">
                <h6 class="card-title text-primary">
                    <i class="fas fa-chart-pie me-2"></i>Statistiques
                </h6>
                {% if details %}
                <ul class="list-unstyled mb-0">
                    <li><strong>Total des réclamations:</strong> {{ details|length }}</li>
                    <li><strong>Réclamations résolues:</strong> {{ details|selectattr('statut', 'equalto', 'Résolu')|list|length }}</li>
                    <li><strong>En cours de traitement:</strong> {{ details|selectattr('statut', 'equalto', 'En cours')|list|length }}</li>
                    <li><strong>En attente:</strong> {{ details|selectattr('statut', 'equalto', 'En attente')|list|length }}</li>
                </ul>
                {% else %}
                <p class="mb-0 text-muted">Aucun détail disponible</p>
                {% endif %}
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card border-0 bg-light">
            <div class="card-body">
                <h6 class="card-title text-warning">
                    <i class="fas fa-tasks me-2"></i>Actions Correctives
                </h6>
                <ul class="list-unstyled mb-0">
                    <li>• Analyse approfondie du problème signalé</li>
                    <li>• Mise en place d'actions correctives immédiates</li>
                    <li>• Suivi régulier de l'évolution</li>
                    <li>• Communication avec le client</li>
                    <li>• Prévention de récurrence</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Plan d'action -->
<div class="section-title">
    <i class="fas fa-clipboard-list me-2"></i>Plan d'Action
</div>

<div class="row">
    <div class="col-md-12">
        <table class="table table-custom">
            <thead>
                <tr>
                    <th style="width: 30%;">Action</th>
                    <th style="width: 20%;">Responsable</th>
                    <th style="width: 20%;">Délai</th>
                    <th style="width: 30%;">Statut</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>Diagnostic initial du problème</td>
                    <td>Équipe technique</td>
                    <td>24h</td>
                    <td><span class="badge-custom badge-success">Terminé</span></td>
                </tr>
                <tr>
                    <td>Mise en place de la solution</td>
                    <td>Technicien spécialisé</td>
                    <td>48h</td>
                    <td><span class="badge-custom badge-warning">En cours</span></td>
                </tr>
                <tr>
                    <td>Test et validation</td>
                    <td>Responsable qualité</td>
                    <td>24h</td>
                    <td><span class="badge-custom badge-info">Planifié</span></td>
                </tr>
                <tr>
                    <td>Rapport final au client</td>
                    <td>Chef de projet</td>
                    <td>12h</td>
                    <td><span class="badge-custom badge-info">Planifié</span></td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Conclusion et recommandations -->
<div class="mt-4 p-3 bg-light rounded">
    <h6 class="text-muted mb-2">
        <i class="fas fa-lightbulb me-2"></i>Conclusion et Recommandations
    </h6>
    <p class="mb-2 text-muted">
        Cette réclamation fait l'objet d'un suivi attentif de notre équipe technique.
        Nous nous engageons à résoudre le problème dans les meilleurs délais tout en
        maintenant la qualité de service attendue.
    </p>
    <p class="mb-0 text-muted">
        <strong>Recommandations:</strong> Mise en place d'un contrôle qualité renforcé
        et formation complémentaire des équipes pour éviter la récurrence de ce type de problème.
    </p>
</div>
{% endblock %}
