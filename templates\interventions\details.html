{% extends "base.html" %}

{% block title %}Détails de l'Intervention - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Détails de l'Intervention - {{ intervention.domaine }}{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Informations générales</h5>
                    <div>
                        <button type="button" class="btn btn-primary btn-print" onclick="window.print()">
                            <i class="fas fa-print"></i> Imprimer
                        </button>
                        <a href="{{ url_for('interventions') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 40%">N° de marché</th>
                                <td>{{ intervention.numero_marche }}</td>
                            </tr>
                            <tr>
                                <th>Domaine</th>
                                <td>
                                    {% if intervention.domaine == 'SVS' %}
                                    <span class="badge bg-primary">SVS</span>
                                    {% elif intervention.domaine == 'EXTINCTEUR' %}
                                    <span class="badge bg-danger">EXTINCTEUR</span>
                                    {% elif intervention.domaine == 'SYSTEME D\'INCENDIE' %}
                                    <span class="badge bg-warning">SYSTEME D'INCENDIE</span>
                                    {% elif intervention.domaine == 'SYSTEME D\'ALARME' %}
                                    <span class="badge bg-info">SYSTEME D'ALARME</span>
                                    {% elif intervention.domaine == 'SYSTEME TELEPHONIQUE' %}
                                    <span class="badge bg-success">SYSTEME TELEPHONIQUE</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ intervention.domaine }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Client</th>
                                <td>{{ intervention.client }}</td>
                            </tr>
                            <tr>
                                <th>Objet de marché</th>
                                <td>{{ intervention.objet_marche }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 40%">Délai d'exécution</th>
                                <td>{{ intervention.delai_execution }}</td>
                            </tr>
                            <tr>
                                <th>Période d'interventions</th>
                                <td>{{ intervention.periode_interventions }}</td>
                            </tr>
                            <tr>
                                <th>Période du marché</th>
                                <td>{{ intervention.periode_marche }}</td>
                            </tr>
                            <tr>
                                <th>Lieu</th>
                                <td>{{ intervention.lieu }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Détails de l'intervention</h5>
                    <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addDetailModal">
                        <i class="fas fa-plus"></i> Ajouter un détail
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if details %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Région</th>
                                <th>Nom du site</th>
                                <th>Nbr de système</th>
                                <th>Date d'intervention</th>
                                <th>Technicien</th>
                                <th>Situation</th>
                                <th>État du matériel</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for detail in details %}
                            <tr>
                                <td>{{ detail.region }}</td>
                                <td>{{ detail.nom_site }}</td>
                                <td>{{ detail.nbr_systeme }}</td>
                                <td>{{ detail.date_intervention }}</td>
                                <td>{{ detail.technicien }}</td>
                                <td>
                                    {% if detail.situation == 'Réglé' %}
                                    <span class="badge bg-success">Réglé</span>
                                    {% elif detail.situation == 'Pas encore' %}
                                    <span class="badge bg-warning">Pas encore</span>
                                    {% elif detail.situation == 'Problème' %}
                                    <span class="badge bg-danger">Problème</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ detail.situation }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if detail.etat_materiel == 'Opérationnel' %}
                                    <span class="badge bg-success">Opérationnel</span>
                                    {% elif detail.etat_materiel == 'Non Opérationnel' %}
                                    <span class="badge bg-danger">Non Opérationnel</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ detail.etat_materiel }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewDetailModal{{ detail.id }}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button"
                                            class="btn btn-sm btn-danger"
                                            onclick="confirmDelete('ce détail d\'intervention', '{{ url_for('supprimer_detail_intervention', id=detail.id) }}')"
                                            title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>

                            <!-- Modal pour voir les détails -->
                            <div class="modal fade" id="viewDetailModal{{ detail.id }}" tabindex="-1" aria-labelledby="viewDetailModalLabel{{ detail.id }}" aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="viewDetailModalLabel{{ detail.id }}">Détails pour {{ detail.nom_site }}</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p><strong>Région:</strong> {{ detail.region }}</p>
                                                    <p><strong>Nom du site:</strong> {{ detail.nom_site }}</p>
                                                    <p><strong>Nombre de système:</strong> {{ detail.nbr_systeme }}</p>
                                                    <p><strong>Date d'intervention:</strong> {{ detail.date_intervention }}</p>
                                                    <p><strong>Technicien:</strong> {{ detail.technicien }}</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p><strong>Situation:</strong> {{ detail.situation }}</p>
                                                    <p><strong>État du matériel:</strong> {{ detail.etat_materiel }}</p>
                                                    <p><strong>Téléphone chef de site:</strong> {{ detail.telephone_chef_site }}</p>
                                                    <p><strong>Téléphone de sécurité:</strong> {{ detail.telephone_securite }}</p>
                                                    <p><strong>Technicien à contacter:</strong> {{ detail.technicien_contact }}</p>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <p><strong>Observation:</strong></p>
                                                    <p>{{ detail.observation }}</p>
                                                </div>
                                            </div>
                                            {% if detail.gps %}
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <p><strong>GPS:</strong> {{ detail.gps }}</p>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-center">Aucun détail d'intervention enregistré</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Modal pour ajouter un détail -->
<div class="modal fade" id="addDetailModal" tabindex="-1" aria-labelledby="addDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="addDetailModalLabel">Ajouter un détail d'intervention</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="POST" action="{{ url_for('details_intervention', id=intervention.id) }}">
                <input type="hidden" name="ajouter_detail" value="1">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="region" class="form-label">Région</label>
                            <input type="text" class="form-control" id="region" name="region" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="nom_site" class="form-label">Nom du site</label>
                            <input type="text" class="form-control" id="nom_site" name="nom_site" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="nbr_systeme" class="form-label">Nombre de système</label>
                            <input type="number" class="form-control" id="nbr_systeme" name="nbr_systeme" value="1" min="1">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="date_intervention" class="form-label">Date d'intervention</label>
                            <input type="date" class="form-control" id="date_intervention" name="date_intervention" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="technicien" class="form-label">Technicien</label>
                            <input type="text" class="form-control" id="technicien" name="technicien" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="situation" class="form-label">Situation</label>
                            <select class="form-select" id="situation" name="situation" required>
                                <option value="">Sélectionner une situation</option>
                                <option value="Réglé">Réglé</option>
                                <option value="Pas encore">Pas encore</option>
                                <option value="Problème">Problème</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="etat_materiel" class="form-label">État du matériel</label>
                            <select class="form-select" id="etat_materiel" name="etat_materiel" required>
                                <option value="">Sélectionner un état</option>
                                <option value="Opérationnel">Opérationnel</option>
                                <option value="Non Opérationnel">Non Opérationnel</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="observation" class="form-label">Observation</label>
                            <textarea class="form-control" id="observation" name="observation" rows="3"></textarea>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label for="telephone_chef_site" class="form-label">Téléphone chef de site</label>
                            <input type="text" class="form-control" id="telephone_chef_site" name="telephone_chef_site">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="telephone_securite" class="form-label">Téléphone de sécurité</label>
                            <input type="text" class="form-control" id="telephone_securite" name="telephone_securite">
                        </div>
                        <div class="col-md-4 mb-3">
                            <label for="technicien_contact" class="form-label">Technicien à contacter</label>
                            <input type="text" class="form-control" id="technicien_contact" name="technicien_contact">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label for="gps" class="form-label">GPS</label>
                            <input type="text" class="form-control" id="gps" name="gps" placeholder="Ex: 31.6295, -7.9811">
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Enregistrer</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Définir la date du jour par défaut
        const today = new Date().toISOString().split('T')[0];
        document.getElementById('date_intervention').value = today;
    });
</script>
{% endblock %}
