{% extends "base.html" %}

{% block title %}Détails de l'Intervention - Système de Gestion de Maintenance{% endblock %}

{% block page_title %}Détails de l'Intervention - {{ intervention.domaine }}{% endblock %}

{% block extra_head %}
<!-- مكتبة XLSX لتصدير Excel -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> Informations générales</h5>
                    <div>
                        <a href="{{ url_for('modifier_intervention', id=intervention.id) }}" class="btn btn-warning">
                            <i class="fas fa-edit"></i> Modifier
                        </a>
                        <a href="{{ url_for('interventions') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Retour
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 40%">N° de marché</th>
                                <td>{{ intervention.numero_marche }}</td>
                            </tr>
                            <tr>
                                <th>Domaine</th>
                                <td>
                                    {% if intervention.domaine == 'SVS' %}
                                    <span class="badge bg-primary">SVS</span>
                                    {% elif intervention.domaine == 'EXTINCTEUR' %}
                                    <span class="badge bg-danger">EXTINCTEUR</span>
                                    {% elif intervention.domaine == 'SYSTEME D\'INCENDIE' %}
                                    <span class="badge bg-warning">SYSTEME D'INCENDIE</span>
                                    {% elif intervention.domaine == 'SYSTEME D\'ALARME' %}
                                    <span class="badge bg-info">SYSTEME D'ALARME</span>
                                    {% elif intervention.domaine == 'SYSTEME TELEPHONIQUE' %}
                                    <span class="badge bg-success">SYSTEME TELEPHONIQUE</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ intervention.domaine }}</span>
                                    {% endif %}
                                </td>
                            </tr>
                            <tr>
                                <th>Client</th>
                                <td>{{ intervention.client }}</td>
                            </tr>
                            <tr>
                                <th>Objet de marché</th>
                                <td>{{ intervention.objet_marche }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-bordered">
                            <tr>
                                <th style="width: 40%">Délai d'exécution</th>
                                <td>{{ intervention.delai_execution }}</td>
                            </tr>
                            <tr>
                                <th>Période d'interventions</th>
                                <td>{{ intervention.periode_interventions }}</td>
                            </tr>
                            <tr>
                                <th>Période du marché</th>
                                <td>{{ intervention.periode_marche }}</td>
                            </tr>
                            <tr>
                                <th>Lieu</th>
                                <td>{{ intervention.lieu }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Détails de l'intervention</h5>
                    <div class="btn-group" role="group">
                        <a href="{{ url_for('ajouter_detail_intervention', id=intervention.id) }}" class="btn btn-success">
                            <i class="fas fa-plus"></i> Ajouter un détail
                        </a>
                        <button type="button" class="btn btn-primary" id="exportDetailsExcel">
                            <i class="fas fa-file-excel"></i> Exporter Excel
                        </button>
                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#importDetailsModal">
                            <i class="fas fa-file-import"></i> Importer Excel
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body">
                {% if details %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>Région</th>
                                <th>Nom du site</th>
                                <th>Nbr de système</th>
                                <th>Date d'intervention</th>
                                <th>Technicien</th>
                                <th>Situation</th>
                                <th>État du matériel</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for detail in details %}
                            <tr>
                                <td>{{ detail.region }}</td>
                                <td>{{ detail.nom_site }}</td>
                                <td>{{ detail.nbr_systeme }}</td>
                                <td>{{ detail.date_intervention }}</td>
                                <td>{{ detail.technicien }}</td>
                                <td>
                                    {% if detail.situation == 'Réglé' %}
                                    <span class="badge bg-success">Réglé</span>
                                    {% elif detail.situation == 'Pas encore' %}
                                    <span class="badge bg-warning">Pas encore</span>
                                    {% elif detail.situation == 'Problème' %}
                                    <span class="badge bg-danger">Problème</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ detail.situation }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if detail.etat_materiel == 'Opérationnel' %}
                                    <span class="badge bg-success">Opérationnel</span>
                                    {% elif detail.etat_materiel == 'Non Opérationnel' %}
                                    <span class="badge bg-danger">Non Opérationnel</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ detail.etat_materiel }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewDetailModal{{ detail.id }}">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button"
                                            class="btn btn-sm btn-danger"
                                            onclick="if(confirm('Êtes-vous sûr de vouloir supprimer ce détail d\'intervention ?\n\nCette action est irréversible !')) { window.location.href='{{ url_for('supprimer_detail_intervention', id=detail.id) }}'; }"
                                            title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </td>
                            </tr>

                            <!-- Modal pour voir les détails -->
                            <div class="modal fade" id="viewDetailModal{{ detail.id }}" tabindex="-1" aria-labelledby="viewDetailModalLabel{{ detail.id }}" aria-hidden="true">
                                <div class="modal-dialog modal-lg">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="viewDetailModalLabel{{ detail.id }}">Détails pour {{ detail.nom_site }}</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p><strong>Région:</strong> {{ detail.region }}</p>
                                                    <p><strong>Nom du site:</strong> {{ detail.nom_site }}</p>
                                                    <p><strong>Nombre de système:</strong> {{ detail.nbr_systeme }}</p>
                                                    <p><strong>Date d'intervention:</strong> {{ detail.date_intervention }}</p>
                                                    <p><strong>Technicien:</strong> {{ detail.technicien }}</p>
                                                </div>
                                                <div class="col-md-6">
                                                    <p><strong>Situation:</strong> {{ detail.situation }}</p>
                                                    <p><strong>État du matériel:</strong> {{ detail.etat_materiel }}</p>
                                                    <p><strong>Téléphone chef de site:</strong> {{ detail.telephone_chef_site }}</p>
                                                    <p><strong>Téléphone de sécurité:</strong> {{ detail.telephone_securite }}</p>
                                                    <p><strong>Technicien à contacter:</strong> {{ detail.technicien_contact }}</p>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <p><strong>Observation:</strong></p>
                                                    <p>{{ detail.observation }}</p>
                                                </div>
                                            </div>
                                            {% if detail.gps %}
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <p><strong>GPS:</strong> {{ detail.gps }}</p>
                                                </div>
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-center">Aucun détail d'intervention enregistré</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>



<!-- Modal pour importer des détails -->
<div class="modal fade" id="importDetailsModal" tabindex="-1" aria-labelledby="importDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="importDetailsModalLabel">Importer des détails d'intervention</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="importDetailsForm" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="detailsFile" class="form-label">Fichier Excel</label>
                        <input type="file" class="form-control" id="detailsFile" accept=".xlsx,.xls" required>
                        <div class="form-text">Formats acceptés: .xlsx, .xls</div>
                    </div>
                    <div class="mb-3">
                        <div class="alert alert-info">
                            <strong>Format attendu:</strong><br>
                            Colonnes: Région, Nom du site, Nbr de système, Date d'intervention, Technicien, Situation, État du matériel, Observation
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" id="importDetailsBtn">Importer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {

        // Export Excel pour les détails
        document.getElementById('exportDetailsExcel').addEventListener('click', function() {
            exportDetailsToExcel();
        });

        // Import Excel pour les détails
        document.getElementById('importDetailsBtn').addEventListener('click', function() {
            importDetailsFromExcel();
        });

        // Fonction d'export Excel pour les détails
        function exportDetailsToExcel() {
            const table = document.querySelector('.table-striped');
            if (!table) {
                alert('Aucune donnée à exporter');
                return;
            }

            const visibleRows = Array.from(table.querySelectorAll('tbody tr'));
            if (visibleRows.length === 0) {
                alert('Aucune donnée à exporter');
                return;
            }

            // إنشاء workbook جديد
            const wb = XLSX.utils.book_new();

            // إنشاء البيانات للجدول
            const data = [];

            // إضافة رؤوس الأعمدة
            data.push(['Région', 'Nom du site', 'Nbr de système', 'Date d\'intervention', 'Technicien', 'Situation', 'État du matériel']);

            // إضافة البيانات
            visibleRows.forEach(row => {
                const cells = row.querySelectorAll('td');
                if (cells.length >= 7) {
                    const rowData = [
                        cells[0]?.textContent.trim() || '',
                        cells[1]?.textContent.trim() || '',
                        cells[2]?.textContent.trim() || '',
                        cells[3]?.textContent.trim() || '',
                        cells[4]?.textContent.trim() || '',
                        cells[5]?.textContent.trim() || '',
                        cells[6]?.textContent.trim() || ''
                    ];
                    data.push(rowData);
                }
            });

            // إنشاء worksheet من البيانات
            const ws = XLSX.utils.aoa_to_sheet(data);

            // تحسين عرض الأعمدة
            const colWidths = [
                { wch: 20 }, // Région
                { wch: 25 }, // Nom du site
                { wch: 15 }, // Nbr de système
                { wch: 18 }, // Date d'intervention
                { wch: 20 }, // Technicien
                { wch: 15 }, // Situation
                { wch: 20 }  // État du matériel
            ];
            ws['!cols'] = colWidths;

            // إضافة worksheet إلى workbook
            XLSX.utils.book_append_sheet(wb, ws, 'Détails Intervention');

            // تصدير الملف
            const currentDate = new Date().toISOString().split('T')[0];
            XLSX.writeFile(wb, `details_intervention_${currentDate}.xlsx`);
        }

        // Fonction d'import Excel pour les détails
        function importDetailsFromExcel() {
            const fileInput = document.getElementById('detailsFile');
            const file = fileInput.files[0];

            if (!file) {
                alert('Veuillez sélectionner un fichier');
                return;
            }

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const firstSheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[firstSheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });

                    if (jsonData.length < 2) {
                        alert('Le fichier doit contenir au moins une ligne de données');
                        return;
                    }

                    // Traitement des données importées
                    console.log('Données importées:', jsonData);
                    alert('Import réussi! ' + (jsonData.length - 1) + ' lignes importées.');

                    // Fermer le modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('importDetailsModal'));
                    modal.hide();

                    // Recharger la page pour voir les nouvelles données
                    location.reload();

                } catch (error) {
                    console.error('Erreur lors de l\'import:', error);
                    alert('Erreur lors de l\'import du fichier: ' + error.message);
                }
            };
            reader.readAsArrayBuffer(file);
        }
    });
</script>
{% endblock %}
